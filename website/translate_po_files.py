#!/usr/bin/env python
import os
import polib
import deepl
import argparse
from tqdm import tqdm
import time
import re

# Configuration
SOURCE_LANGUAGE = 'en'  # Source language code (English)
LOCALE_DIR = os.path.join(os.path.dirname(__file__), 'locale')
PROJECT_LOCALE_DIR = os.path.join(os.path.dirname(__file__), 'project', 'locale')  # Additional locale directory
API_KEY = 'bb39e65c-6399-4317-8e3c-d8300a8d1fda:fx'  # DeepL API key

# Target languages mapping (Django locale code -> DeepL language code)
TARGET_LANGUAGES = {
    'de': 'DE',  # German
    'es': 'ES',  # Spanish
    'fr': 'FR',  # French
    'lt': 'LT',  # Lithuanian
    'pl': 'PL',  # Polish
    'ru': 'RU',  # Russian
    # Add more languages as needed
}


def prepare_text_for_translation(text):
    """
    Prepare text for translation by wrapping Django variables in DeepL-compatible XML tags
    
    This preserves the variables while maintaining context for accurate translation
    """
    if not text or text.isspace():
        return text
    
    # Find all Django variables format: %(name)s or %(name)d
    pattern = re.compile(r'%\(([a-zA-Z0-9_]+)\)([sd])')
    
    # Replace Django variables with XML-tagged versions that DeepL will preserve
    # Format: %(name)s becomes <dj id="name" format="s">%(name)s</dj>
    processed_text = pattern.sub(
        lambda m: f'<dj id="{m.group(1)}" format="{m.group(2)}">%({m.group(1)}){m.group(2)}</dj>',
        text
    )
    
    return processed_text


def translate_text(translator, text, target_lang):
    """Translate text using DeepL API, preserving Django variables with XML tags"""
    if not text or text.isspace():
        return text
    
    try:
        # Prepare text with XML tags around Django variables
        prepared_text = prepare_text_for_translation(text)
        
        # Check if the text contains any Django variables (and thus XML tags)
        has_variables = '<dj' in prepared_text
        
        # If text has variables, use the XML handling
        if has_variables:
            result = translator.translate_text(
                prepared_text,
                source_lang=SOURCE_LANGUAGE.upper(),
                target_lang=target_lang,
                tag_handling='xml',
                outline_detection=False,
                splitting_tags=['dj'],
                ignore_tags=['dj']
            )
        else:
            # Regular translation for text without Django variables
            result = translator.translate_text(
                text,
                source_lang=SOURCE_LANGUAGE.upper(),
                target_lang=target_lang
            )
            
        return result.text
    except Exception as e:
        print(f"Error translating text: {e}")
        print(f"Problematic text: {text}")
        # Return the original text if translation fails
        return text


def translate_po_file(po_file_path, target_lang, translator, update_only_empty=True, 
                     fuzzy_mode='ignore', delay=0.1):
    """
    Translate .po file entries
    
    Args:
        po_file_path: Path to the .po file
        target_lang: Target language code for DeepL
        translator: DeepL translator instance
        update_only_empty: If True, only translate empty strings
        fuzzy_mode: How to handle fuzzy translations:
            - 'ignore': leave fuzzy translations as is (default)
            - 'translate': translate and clear fuzzy flag
            - 'clear': only clear fuzzy flag
        delay: Delay in seconds between API calls to avoid rate limiting
    """
    print(f"Processing {po_file_path} to {target_lang}...")
    po = polib.pofile(po_file_path)
    
    # Create a list to store entries that need translation
    entries_to_translate = []
    fuzzy_entries_to_handle = []
    
    for entry in po:
        # Handle fuzzy entries
        if 'fuzzy' in entry.flags:
            if fuzzy_mode != 'ignore':
                fuzzy_entries_to_handle.append(entry)
            continue
            
        # Skip entries that have a translation if update_only_empty is True
        if update_only_empty and entry.msgstr:
            continue
            
        if not entry.obsolete and entry.msgid:
            entries_to_translate.append(entry)
    
    # Process fuzzy entries based on fuzzy_mode
    if fuzzy_entries_to_handle:
        print(f"Found {len(fuzzy_entries_to_handle)} fuzzy entries")
        
        if fuzzy_mode == 'clear':
            # Just clear fuzzy flag
            for entry in fuzzy_entries_to_handle:
                if 'fuzzy' in entry.flags:
                    entry.flags.remove('fuzzy')
            print(f"Cleared fuzzy flag from {len(fuzzy_entries_to_handle)} entries")
        
        elif fuzzy_mode == 'translate':
            # Translate and clear fuzzy flag
            print(f"Translating {len(fuzzy_entries_to_handle)} fuzzy entries...")
            for entry in tqdm(fuzzy_entries_to_handle, desc=f"Translating fuzzy to {target_lang}"):
                try:
                    # Translate the text
                    translated_text = translate_text(translator, entry.msgid, target_lang)
                    entry.msgstr = translated_text
                    
                    # If this is a plural form, translate all plural forms
                    if entry.msgid_plural:
                        entry.msgstr_plural = {
                            '0': translate_text(translator, entry.msgid_plural, target_lang)
                        }
                    
                    # Remove the fuzzy flag
                    if 'fuzzy' in entry.flags:
                        entry.flags.remove('fuzzy')
                        
                    # Adding a small delay to avoid rate limiting
                    time.sleep(delay)
                except Exception as e:
                    print(f"Error processing fuzzy entry {entry.msgid}: {e}")
    
    # Skip if no regular entries to translate
    if not entries_to_translate:
        if not fuzzy_entries_to_handle:
            print(f"No entries to translate in {po_file_path}")
        else:
            # We've handled fuzzy entries, save the file
            po.save(po_file_path)
            print(f"Saved translations to {po_file_path}")
        return

    # Show progress bar for regular translations
    for entry in tqdm(entries_to_translate, desc=f"Translating to {target_lang}"):
        try:
            # Translate the text
            translated_text = translate_text(translator, entry.msgid, target_lang)
            entry.msgstr = translated_text
            
            # If this is a plural form, translate all plural forms
            if entry.msgid_plural:
                entry.msgstr_plural = {
                    '0': translate_text(translator, entry.msgid_plural, target_lang)
                }
                
            # Adding a small delay to avoid rate limiting
            time.sleep(delay)
            
        except Exception as e:
            print(f"Error processing entry {entry.msgid}: {e}")
    
    # Save the translated file
    po.save(po_file_path)
    print(f"Saved translations to {po_file_path}")


def process_directory(locale_dir, django_lang, deepl_lang, translator, update_only_empty, fuzzy_mode, delay):
    """Process .po files in the given locale directory for a specific language"""
    locale_path = os.path.join(locale_dir, django_lang, 'LC_MESSAGES')
    
    if not os.path.exists(locale_path):
        print(f"Skipping {django_lang} in {locale_dir}: locale directory not found at {locale_path}")
        return
        
    # Process django.po and djangojs.po files
    for po_file in ['django.po', 'djangojs.po']:
        po_file_path = os.path.join(locale_path, po_file)
        
        if os.path.exists(po_file_path):
            translate_po_file(
                po_file_path, 
                deepl_lang, 
                translator,
                update_only_empty=update_only_empty,
                fuzzy_mode=fuzzy_mode,
                delay=delay
            )
        else:
            print(f"File not found: {po_file_path}")


def main():
    """
    Translate .po files using DeepL API
    
    Examples:
    
    # Translate only empty strings in all languages
    python translate_po_files.py
    
    # Translate all strings, including existing translations in all languages
    python translate_po_files.py --all
    
    # Translate only to Lithuanian and Polish
    python translate_po_files.py --languages=lt,pl
    
    # Translate and clear fuzzy flag for fuzzy entries
    python translate_po_files.py --fuzzy=translate
    
    # Only clear fuzzy flag for fuzzy entries (no translation)
    python translate_po_files.py --fuzzy=clear
    
    # Use a specific delay between API calls
    python translate_po_files.py --delay=0.2
    
    # Combine multiple options
    python translate_po_files.py --all --languages=lt,pl --fuzzy=translate --delay=0.2
    """
    parser = argparse.ArgumentParser(description='Translate .po files using DeepL API')
    parser.add_argument('--all', action='store_true', help='Translate all strings, including ones that already have translations')
    parser.add_argument('--languages', type=str, help='Comma separated list of target language codes (e.g., "de,es,fr")')
    parser.add_argument(
        '--fuzzy',
        choices=['ignore', 'translate', 'clear'],
        default='ignore',
        help='How to handle fuzzy translations: "ignore" (default) - leave fuzzy translations as is, '
             '"translate" - translate and clear fuzzy flag, "clear" - only clear fuzzy flag',
    )
    parser.add_argument(
        '--delay',
        type=float,
        default=0.1,
        help='Delay in seconds between API calls to avoid rate limiting (default: 0.1)',
    )
    parser.add_argument(
        '--skip-main-locale',
        action='store_true', 
        help='Skip processing files in the main locale directory (website/locale)'
    )
    parser.add_argument(
        '--skip-project-locale',
        action='store_true', 
        help='Skip processing files in the project locale directory (website/project/locale)'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run a test translation with variables first'
    )
    args = parser.parse_args()
    
    # Initialize DeepL translator
    translator = deepl.Translator(API_KEY)
    
    # Test Django variables handling if requested
    if args.test:
        test_text = "%(title)s %(code)s for %(brand)s %(model)s %(year)s. Best price, fast delivery."
        print(f"Original: {test_text}")
        for lang_code, deepl_code in TARGET_LANGUAGES.items():
            translated = translate_text(translator, test_text, deepl_code)
            print(f"{lang_code.upper()}: {translated}")
        return
    
    # Get available languages from DeepL
    try:
        available_languages = translator.get_target_languages()
        print("Available DeepL languages:")
        for lang in available_languages:
            print(f"- {lang.code}: {lang.name}")
    except Exception as e:
        print(f"Error connecting to DeepL API: {e}")
        return

    # Determine which languages to process
    languages_to_process = TARGET_LANGUAGES
    if args.languages:
        requested_langs = args.languages.split(',')
        languages_to_process = {k: v for k, v in TARGET_LANGUAGES.items() if k in requested_langs}
        
    print(f"Will translate to: {', '.join(languages_to_process.keys())}")
    print(f"Fuzzy handling mode: {args.fuzzy}")
    
    # Process locale directories
    locale_dirs_to_process = []
    if not args.skip_main_locale:
        locale_dirs_to_process.append(("Main locale", LOCALE_DIR))
    if not args.skip_project_locale and os.path.exists(PROJECT_LOCALE_DIR):
        locale_dirs_to_process.append(("Project locale", PROJECT_LOCALE_DIR))
        
    if not locale_dirs_to_process:
        print("No locale directories to process. Please check your arguments.")
        return
        
    print(f"Will process {len(locale_dirs_to_process)} locale directories")
    
    # Process each locale directory and language
    for locale_name, locale_dir in locale_dirs_to_process:
        print(f"\n=== Processing {locale_name} ({locale_dir}) ===")
        for django_lang, deepl_lang in languages_to_process.items():
            process_directory(
                locale_dir,
                django_lang,
                deepl_lang,
                translator,
                update_only_empty=not args.all,
                fuzzy_mode=args.fuzzy,
                delay=args.delay
            )


if __name__ == "__main__":
    main() 