# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FlatPage',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('url', models.Char<PERSON><PERSON>(max_length=100, verbose_name='URL', db_index=True)),
                ('title', models.CharField(max_length=200, verbose_name='title')),
                ('title_lt', models.CharField(max_length=200, null=True, verbose_name='title')),
                ('title_en', models.Char<PERSON>ield(max_length=200, null=True, verbose_name='title')),
                ('title_de', models.CharField(max_length=200, null=True, verbose_name='title')),
                ('title_ru', models.CharField(max_length=200, null=True, verbose_name='title')),
                ('title_pl', models.Char<PERSON>ield(max_length=200, null=True, verbose_name='title')),
                ('title_es', models.CharField(max_length=200, null=True, verbose_name='title')),
                ('title_fr', models.CharField(max_length=200, null=True, verbose_name='title')),
                ('meta_keywords', models.CharField(help_text='Keywords used by search engines', max_length=500, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_lt', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_en', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_de', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_ru', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_pl', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_es', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_keywords_fr', models.CharField(help_text='Keywords used by search engines', max_length=500, null=True, verbose_name='Meta keywords', blank=True)),
                ('meta_description', models.CharField(help_text='Description used by search engines', max_length=1000, verbose_name='Meta description', blank=True)),
                ('meta_description_lt', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_en', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_de', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_ru', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_pl', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_es', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('meta_description_fr', models.CharField(help_text='Description used by search engines', max_length=1000, null=True, verbose_name='Meta description', blank=True)),
                ('content', models.TextField(verbose_name='content', blank=True)),
                ('content_lt', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_en', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_de', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_ru', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_pl', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_es', models.TextField(null=True, verbose_name='content', blank=True)),
                ('content_fr', models.TextField(null=True, verbose_name='content', blank=True)),
                ('enable_comments', models.BooleanField(default=False, verbose_name='enable comments')),
                ('template_name', models.CharField(help_text="Example: 'flatpages/contact_page.html'. If this isn't provided, the system will use 'flatpages/default.html'.", max_length=70, verbose_name='template name', blank=True)),
                ('registration_required', models.BooleanField(default=False, help_text='If this is checked, only logged-in users will be able to view the page.', verbose_name='registration required')),
                ('sites', models.ManyToManyField(to='sites.Site')),
            ],
            options={
                'ordering': ('url',),
                'db_table': 'django_flatpage',
                'verbose_name': 'flat page',
                'verbose_name_plural': 'flat pages',
            },
            bases=(models.Model,),
        ),
    ]
