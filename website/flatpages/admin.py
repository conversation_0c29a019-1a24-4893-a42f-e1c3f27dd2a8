from django import forms
from django.conf import settings
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from flatpages.models import FlatPage as CustomFlatPage
from flatpages.forms import FlatpageForm


class FlatPageAdmin(admin.ModelAdmin):
    form = FlatpageForm
    exclude = (
        "title",
        "content",
        "meta_keywords",
        "meta_description",
        "enable_comments",
        "registration_required",
        "template_name",
    )
    list_display = ("url", "title", "get_sites")
    search_fields = ("url", "title")


admin.site.register(CustomFlatPage, FlatPageAdmin)
