

from django.db import models
from django.contrib.sites.models import Site
from django.utils.translation import gettext_lazy as _


class FlatPage(models.Model):
    url = models.Char<PERSON>ield(_('URL'), max_length=100, db_index=True)
    title = models.Char<PERSON>ield(_('title'), max_length=200)
    meta_keywords = models.CharField(
        _('Meta keywords'), max_length=500, help_text=_('Keywords used by search engines'), blank=True
    )
    meta_description = models.Char<PERSON>ield(
        _('Meta description'), max_length=1000, help_text=_('Description used by search engines'), blank=True
    )
    content = models.TextField(_('content'), blank=True)
    enable_comments = models.BooleanField(_('enable comments'), default=False)
    template_name = models.CharField(
        _('template name'),
        max_length=70,
        blank=True,
        help_text=_(
            "Example: 'flatpages/contact_page.html'. If this isn't provided, the system will use 'flatpages/default.html'."
        ),
    )
    registration_required = models.<PERSON>olean<PERSON>ield(
        _('registration required'),
        help_text=_("If this is checked, only logged-in users will be able to view the page."),
        default=False,
    )
    sites = models.ManyToManyField(Site)

    class Meta:
        db_table = 'django_flatpage'
        verbose_name = _('flat page')
        verbose_name_plural = _('flat pages')
        ordering = ('url',)

    def __str__(self):
        return "%s -- %s" % (self.url, self.title)

    def get_absolute_url(self):
        return self.url

    def get_sites(self):
        return ', '.join(list(self.sites.all().values_list('domain', flat=True)))

    get_sites.short_description = _('Sites')
