from django import forms
from django.conf import settings
from django.utils.translation import gettext, gettext_lazy as _

from flatpages.models import FlatPage


class FlatpageForm(forms.ModelForm):
    url = forms.RegexField(
        label=_("URL"),
        max_length=100,
        regex=r'^[-\w/\.~]+$',
        help_text=_("Example: '/about/contact/'. Make sure to have leading" " and trailing slashes."),
        error_messages={
            "invalid": "This value must contain only letters, numbers,"
            " dots, underscores, dashes, slashes or tildes."
        },
    )

    class Meta:
        fields = "__all__"
        model = FlatPage

    def clean_url(self):
        url = self.cleaned_data['url']
        if not url.startswith('/'):
            raise forms.ValidationError(gettext("URL is missing a leading slash."))
        if (
            settings.APPEND_SLASH
            and 'django.middleware.common.CommonMiddleware' in settings.MIDDLEWARE
            and not url.endswith('/')
        ):
            raise forms.ValidationError(gettext("URL is missing a trailing slash."))
        return url

    def clean(self):
        url = self.cleaned_data.get('url', None)
        sites = self.cleaned_data.get('sites', None)

        same_url = FlatPage.objects.filter(url=url)
        if self.instance.pk:
            same_url = same_url.exclude(pk=self.instance.pk)

        if sites and same_url.filter(sites__in=sites).exists():
            for site in sites:
                if same_url.filter(sites=site).exists():
                    raise forms.ValidationError(
                        _('Flatpage with url %(url)s already exists for site %(site)s' % {'url': url, 'site': site})
                    )

        return super(FlatpageForm, self).clean()
