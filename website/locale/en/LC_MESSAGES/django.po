# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-21 23:58+0300\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: flatpages/apps.py:8
msgid "Flat Pages"
msgstr ""

#: flatpages/forms.py:10 flatpages/models.py:9
msgid "URL"
msgstr ""

#: flatpages/forms.py:13
msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""

#: flatpages/forms.py:27
msgid "URL is missing a leading slash."
msgstr ""

#: flatpages/forms.py:33
msgid "URL is missing a trailing slash."
msgstr ""

#: flatpages/forms.py:48
#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

#: flatpages/models.py:10
msgid "title"
msgstr ""

#: flatpages/models.py:12
msgid "Meta keywords"
msgstr ""

#: flatpages/models.py:12
msgid "Keywords used by search engines"
msgstr ""

#: flatpages/models.py:15
msgid "Meta description"
msgstr ""

#: flatpages/models.py:15
msgid "Description used by search engines"
msgstr ""

#: flatpages/models.py:17
msgid "content"
msgstr ""

#: flatpages/models.py:18
msgid "enable comments"
msgstr ""

#: flatpages/models.py:20
msgid "template name"
msgstr ""

#: flatpages/models.py:24
msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""

#: flatpages/models.py:28
msgid "registration required"
msgstr ""

#: flatpages/models.py:29
msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

#: flatpages/models.py:36
msgid "flat page"
msgstr ""

#: flatpages/models.py:37
msgid "flat pages"
msgstr ""

#: flatpages/models.py:49
msgid "Sites"
msgstr ""

#: settings.py:610
msgid "Dashboard"
msgstr ""

#: settings.py:616 settings.py:620
msgid "Products"
msgstr ""

#: settings.py:625
msgid "Product description templates"
msgstr ""

#: settings.py:632
msgid "Fulfilment"
msgstr ""

#: settings.py:636
msgid "Order management"
msgstr ""

#: settings.py:643
msgid "Shipping"
msgstr ""

#: settings.py:647
msgid "Shipping charges"
msgstr ""

#: settings.py:652
msgid "Shipping zones"
msgstr ""

#: settings.py:659
msgid "User management"
msgstr ""

#: settings.py:663
msgid "Branches"
msgstr ""

#: settings.py:668
msgid "Managers"
msgstr ""

#: settings.py:675
msgid "Content"
msgstr ""

#: settings.py:679
msgid "Reviews"
msgstr ""

#: settings.py:686
msgid "Reports"
msgstr ""

#: settings.py:692
msgid "Messages"
msgstr ""

#: settings.py:696
msgid "RRR Stock Check Results"
msgstr ""

#: settings.py:701
msgid "eBay Stock Check Results"
msgstr ""

#: settings.py:708
msgid "PAP Warehouse"
msgstr ""

#: settings.py:712
msgid "Vehicles"
msgstr ""

#: settings.py:717
msgid "Vehicle parts"
msgstr ""

#: settings.py:722
msgid "Local sales / stock returns"
msgstr ""

#: settings.py:727
msgid "Locations"
msgstr ""

#: settings.py:732
msgid "Boxes"
msgstr ""

#: settings.py:737
msgid "Action log"
msgstr ""

#: settings.py:742
msgid "Categories"
msgstr ""

#: webtopay/models.py:93
msgid "payment did not succeed"
msgstr ""

#: webtopay/models.py:94
msgid "payment succeeded"
msgstr ""

#: webtopay/models.py:95
msgid "payment accepted, but not yet processed"
msgstr ""

#: webtopay/views.py:84
msgid "Transaction cancelled"
msgstr ""

#: webtopay/views.py:104 webtopay/views.py:151
msgid "A problem occurred communicating with PaySera - please try again later"
msgstr ""

#: webtopay/views.py:108 webtopay/views.py:159
msgid "No basket was found that corresponds to your transaction"
msgstr ""

#: webtopay/views.py:117
msgid "There was a problem processing your order. Please try confirming again."
msgstr ""

#: webtopay/views.py:163
msgid "Error processing response"
msgstr ""

#~ msgid "WebToPay ID"
#~ msgstr "Paysera ID"

#~ msgid "WebToPay password"
#~ msgstr "Paysera password"

#~ msgid "WebToPay verify code"
#~ msgstr "Paysera verify code"

#~ msgid "WebToPay"
#~ msgstr "Paysera"

#~ msgid "WebToPay payment failed"
#~ msgstr "Payment failed"

#~ msgid "Connecting to Mokejimai.lt ..."
#~ msgstr "Connecting to Paysera ..."

#~ msgid "WebToPay payment successful"
#~ msgstr "Payment successful"
