/* Legacy browser fixes for Tailwind CSS */

/* Fix for Tailwind's responsive classes */
.legacy-browser .lg\:w-1\/4 { width: 25% !important; }
.legacy-browser .lg\:w-3\/4 { width: 75% !important; }
.legacy-browser .lg\:flex-row { display: flex !important; flex-direction: row !important; }
.legacy-browser .lg\:block { display: block !important; }
.legacy-browser .lg\:hidden { display: none !important; }
.legacy-browser .lg\:gap-6 { gap: 1.5rem !important; }
.legacy-browser .lg\:gap-4 { gap: 1rem !important; }
.legacy-browser .lg\:gap-2 { gap: 0.5rem !important; }

/* Fix for buttons in older Chrome browsers */
.legacy-browser button,
.legacy-browser input[type="button"],
.legacy-browser input[type="submit"],
.legacy-browser .btn,
.legacy-browser [role="button"] {
    cursor: pointer !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 5 !important;
    overflow: visible !important;
}

/* Fix specific issue with buttons not responding to clicks in legacy browsers */
.legacy-browser button:after,
.legacy-browser input[type="button"]:after,
.legacy-browser input[type="submit"]:after,
.legacy-browser .btn:after,
.legacy-browser [role="button"]:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

/* Fix for Alpine.js transitions */
.legacy-browser [x-transition\:enter],
.legacy-browser [x-transition\:leave] {
    transition: none !important;
}

/* Force sidebar visibility on desktop for old browsers */
@media (min-width: 1024px) {
    .legacy-browser [x-show="mobileFiltersOpen.categories"],
    .legacy-browser [x-show="mobileFiltersOpen.extraAttributes"],
    .legacy-browser [x-show="mobileFiltersOpen.rangeFilters"],
    .legacy-browser [x-show="mobileFiltersOpen.vehicleFilter"] {
        display: block !important;
    }
    
    /* Common layout fixes for desktop */
    .legacy-browser .lg\:flex {
        display: flex !important;
    }
    
    .legacy-browser .lg\:grid-cols-2 {
        display: grid !important;
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }
    
    .legacy-browser .lg\:grid-cols-3 {
        display: grid !important;
        grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
    }
    
    .legacy-browser .lg\:grid-cols-4 {
        display: grid !important;
        grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
    }
}

/* Fix for xCloak directive */
.legacy-browser [x-cloak] {
    display: none !important;
}

/* Fix for mobile-specific elements */
@media (max-width: 1023px) {
    .legacy-browser .lg\:hidden {
        display: none !important;
    }
    
    .legacy-browser .md\:hidden {
        display: none !important;
    }
    
    .legacy-browser .md\:block {
        display: block !important;
    }
    
    .legacy-browser .lg\:flex {
        display: flex !important;
    }
}

/* Experimental fixes for Alpine.js */
.legacy-browser [x-data], 
.legacy-browser [x-init], 
.legacy-browser [x-show] {
    visibility: visible !important;
}

/* Ensure navigation links are visible on black background for legacy browsers */
.legacy-browser nav.bg-black a {
    color: #e0e0e0 !important; /* Light gray color */
}

.legacy-browser nav.bg-black a:hover {
    color: #ffffff !important; /* White color on hover */
}

/* Ensure button text and icons in black navbar are visible */
.legacy-browser nav.bg-black button {
    color: #e0e0e0 !important; /* Light gray for text and SVGs via currentColor */
}

.legacy-browser nav.bg-black button:hover {
    color: #ffffff !important; /* White for text and SVGs on hover */
}

/* Ensure the main 'Search for parts' container is visible */
.legacy-browser .bg-gray-100.p-4.rounded-lg.my-8.shadow-md {
    display: block !important;
}

/* Fix for the specific grid layout in 'Search for parts' form */
.legacy-browser .lg\:grid-cols-\[1fr_1fr_1fr_1fr_auto\] {
    display: grid !important;
    grid-template-columns: 1fr 1fr 1fr 1fr auto !important;
}

/* Ensure the flex container for the search button is visible and items are aligned */
.legacy-browser .flex.items-end {
    display: flex !important;
    align-items: flex-end !important;
}

/* Ensure the search button itself is explicitly visible, though Tailwind classes should handle it */
.legacy-browser button.bg-gray-700.text-white {
    display: flex !important; /* or inline-flex, depending on original styling if flex is an issue */
    visibility: visible !important;
    color: #ffffff !important;
    background-color: #374151 !important; /* Tailwind's gray-700 */
}

/* Ensure the vehicle filter container is visible */
.legacy-browser .bg-gray-100.p-4.rounded-lg.mb-6 {
    display: block !important;
}

/* Fix for the specific grid layout in vehicle_filter.html */
.legacy-browser .md\:grid-cols-\[1fr_1fr_1fr_auto\] {
    display: grid !important;
    grid-template-columns: 1fr 1fr 1fr auto !important;
}

/* Fix for manufacturer logos on homepage for legacy desktop browsers */
/* This targets the container for each logo item */
.legacy-browser .lg\:w-1\/8 {
    width: 12.5% !important; /* 8 items per row */
}

/* Fallback if lg is too small a breakpoint for some legacy interpretation, use md */
.legacy-browser .md\:w-1\/6 {
    width: 16.6666% !important; /* 6 items per row */
}

/* Ensure the parent flex container behaves as expected */
.legacy-browser .flex.flex-wrap.justify-center {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
}

/* The .flex.items-end rule added previously should cover the Apply button's container */
/* The button.bg-gray-700.text-white rule added previously should cover the Apply button styling */

/* Ensure currency dropdown panel has a white background */
.legacy-browser nav.bg-black div[role="menu"].absolute.bg-white {
    background-color: #ffffff !important;
    border: 1px solid #cccccc !important; /* Add a border for better visibility if shadow fails */
}

/* Ensure currency dropdown items have dark text and appropriate hover */
.legacy-browser nav.bg-black div[role="menu"].absolute.bg-white a {
    color: #374151 !important; /* Tailwind's gray-700 */
    background-color: transparent !important; /* Ensure no nav background bleeds */
}

.legacy-browser nav.bg-black div[role="menu"].absolute.bg-white a:hover {
    color: #1f2937 !important; /* Tailwind's gray-800 */
    background-color: #f3f4f6 !important; /* Tailwind's gray-100 */
}

/* Style the main search input (search_storefront) for legacy browsers */
.legacy-browser input#id_q.bg-gray-700 {
    background-color: #374151 !important; /* Tailwind's gray-700 */
    color: #ffffff !important; /* White text */
    padding: 0.5rem 1rem !important; /* Corresponds to px-4 py-2 */
    border-radius: 0.5rem !important; /* Corresponds to rounded-lg */
    border: 1px solid #4b5563 !important; /* A border based on gray-600 for visibility */
    width: 100% !important; /* Ensure it takes full width of its container */
}

/* Placeholder styling for the main search input */
.legacy-browser input#id_q.bg-gray-700::placeholder {
    color: #d1d5db !important; /* Tailwind's gray-300 */
    opacity: 1 !important; /* Ensure placeholder is visible */
}
/* Fallbacks for older browsers that might not support ::placeholder */
.legacy-browser input#id_q.bg-gray-700:-ms-input-placeholder {
    color: #d1d5db !important;
}
.legacy-browser input#id_q.bg-gray-700::-ms-input-placeholder {
    color: #d1d5db !important;
}

/* Specific fixes for login and register buttons */
.legacy-browser form[action*="login"] button[type="submit"],
.legacy-browser form[action*="register"] button[type="submit"] {
    display: inline-block !important;
    position: relative !important;
    background-color: #4A5568 !important; /* Default button background */
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    border: 1px solid transparent !important;
    text-align: center !important;
    cursor: pointer !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    z-index: 10 !important;
    pointer-events: auto !important;
}

.legacy-browser form[action*="login"] button[type="submit"]:hover,
.legacy-browser form[action*="register"] button[type="submit"]:hover {
    background-color: #2D3748 !important; /* Darker on hover */
}

/* Fix for login/register input fields */
.legacy-browser form[action*="login"] input:not([type="checkbox"]),
.legacy-browser form[action*="register"] input:not([type="checkbox"]) {
    display: block !important;
    width: 100% !important;
    padding: 8px !important;
    border: 1px solid #CBD5E0 !important;
    border-radius: 4px !important;
    margin-bottom: 12px !important;
}

/* Ensure form labels are visible */
.legacy-browser form[action*="login"] label,
.legacy-browser form[action*="register"] label {
    display: block !important;
    margin-bottom: 4px !important;
    font-weight: 500 !important;
}

/* Fix for "I've forgotten my password" link */
.legacy-browser a[href*="password-reset"] {
    color: #3182CE !important; /* Blue color for link */
    text-decoration: none !important;
    display: inline-block !important;
    margin-top: 8px !important;
    pointer-events: auto !important;
}

.legacy-browser a[href*="password-reset"]:hover {
    text-decoration: underline !important;
}

/* General Customer Account Button Fixes */
.legacy-browser .btn-primary,
.legacy-browser .btn-secondary,
.legacy-browser .btn-danger {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.5rem 1rem !important; /* Corresponds to px-4 py-2 */
    font-weight: 500 !important; /* Corresponds to font-medium */
    border-radius: 0.375rem !important; /* Corresponds to rounded-md */
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important;
    text-decoration: none !important; /* Ensure links styled as buttons don't have underlines */
    cursor: pointer !important;
    pointer-events: auto !important; /* Ensure clickability */
    z-index: 10 !important; /* Ensure buttons are on top */
    position: relative !important; /* For z-index and pseudo-elements if needed */
    overflow: visible !important; /* Ensure all parts of the button are visible */
}

/* Primary Button Specifics */
.legacy-browser .btn-primary {
    background-color: #4A5568 !important; /* Tailwind's gray-700 */
    color: #ffffff !important; /* White text */
}
.legacy-browser .btn-primary:hover {
    background-color: #2C5282 !important; /* Tailwind's blue-700 */
}

/* Secondary Button Specifics */
.legacy-browser .btn-secondary {
    background-color: #E2E8F0 !important; /* Tailwind's gray-200 */
    color: #4A5568 !important; /* Tailwind's gray-700 */
}
.legacy-browser .btn-secondary:hover {
    background-color: #CBD5E0 !important; /* Tailwind's gray-300 */
}

/* Danger Button Specifics */
.legacy-browser .btn-danger {
    background-color: #E53E3E !important; /* Tailwind's red-600 */
    color: #ffffff !important; /* White text */
}
.legacy-browser .btn-danger:hover {
    background-color: #C53030 !important; /* Tailwind's red-700 */
}

/* Fix for input elements styled as buttons (if any) */
.legacy-browser input[type="submit"].btn-primary,
.legacy-browser input[type="submit"].btn-secondary,
.legacy-browser input[type="submit"].btn-danger,
.legacy-browser input[type="button"].btn-primary,
.legacy-browser input[type="button"].btn-secondary,
.legacy-browser input[type="button"].btn-danger {
    -webkit-appearance: none !important; /* Remove default browser styling for inputs */
    -moz-appearance: none !important;
    appearance: none !important;
    border: none !important; /* Remove default borders */
}

/* Ensure date inputs in order filter are somewhat usable */
.legacy-browser form[action="."] input[type="text"][placeholder="YYYY-MM-DD"] {
    padding: 0.5rem 0.75rem !important;
    border: 1px solid #D2D6DC !important; /* gray-300 */
    border-radius: 0.375rem !important; /* rounded-md */
    min-width: 150px !important;
}

/* Address list page button-like links fixes */
.legacy-browser .flex-1.text-center.bg-gray-200,
.legacy-browser .flex-1.text-center.bg-red-800,
.legacy-browser .flex-1.text-center.bg-gray-700 {
    display: inline-block !important;
    text-align: center !important;
    padding: 0.375rem 0.75rem !important; /* px-3 py-1.5 */
    font-weight: 500 !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important; /* text-sm */
    line-height: 1.5 !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform !important;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    transition-duration: 150ms !important;
    border: none !important;
    text-decoration: none !important;
    z-index: 10 !important;
    position: relative !important;
    overflow: visible !important;
}
.legacy-browser .flex-1.text-center.bg-gray-200 {
    background-color: #E2E8F0 !important; /* Tailwind gray-200 */
    color: #4A5568 !important; /* Tailwind gray-700 */
}
.legacy-browser .flex-1.text-center.bg-gray-200:hover {
    background-color: #CBD5E0 !important; /* Tailwind gray-300 */
}
.legacy-browser .flex-1.text-center.bg-red-800 {
    background-color: #2D3748 !important; /* Tailwind red-800 fallback to gray-800 for contrast */
    color: #fff !important;
}
.legacy-browser .flex-1.text-center.bg-red-800:hover {
    background-color: #1A202C !important; /* Tailwind gray-900 */
}
.legacy-browser .flex-1.text-center.bg-gray-700 {
    background-color: #4A5568 !important; /* Tailwind gray-700 */
    color: #fff !important;
}
.legacy-browser .flex-1.text-center.bg-gray-700:hover {
    background-color: #2C5282 !important; /* Tailwind blue-700 */
}

/* For small "Set as default" buttons */
.legacy-browser .flex-1.text-center.text-xs.bg-gray-700 {
    font-size: 0.75rem !important; /* text-xs */
    padding: 0.5rem 0.5rem !important; /* py-2 px-2 */
}

/* Ensure all .flex-1.text-center links are clickable */
.legacy-browser .flex-1.text-center {
    cursor: pointer !important;
    pointer-events: auto !important;
} 