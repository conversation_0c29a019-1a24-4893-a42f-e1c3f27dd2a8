@import "tailwindcss";
@plugin "@tailwindcss/typography";

@theme {
  --font-sans: 'Montserrat', 'Montserrat Fallback', 'Segoe UI', 'Roboto', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
  --font-montserrat: 'Montserrat', 'Montserrat Fallback', 'Segoe UI', 'Roboto', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
}

@layer base {
  html {
    font-family: 'Montserrat', 'Montserrat Fallback', 'Segoe UI', 'Roboto', 'Open Sans', 'Helvetica Neue', Arial, sans-serif;
  }
}

@layer components {
  /* Primary button - blue */
  .btn-primary {
    @apply inline-flex items-center justify-center px-4 py-2 bg-gray-700 hover:bg-blue-700 text-white font-medium rounded-md transition-colors;
  }

  /* Secondary button - gray outline */
  .btn-secondary {
    @apply inline-flex items-center justify-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-md transition-colors;
  }

  /* Danger button - red */
  .btn-danger {
    @apply inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition-colors;
  }

  /* Style all form inputs directly */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="date"],
  select,
  textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  /* Required form field indicator */
  .required::after {
    content: "*";
    @apply text-red-500 ml-1;
  }

  /* Border utility for left border accent */
  .border-l-12 {
    border-left-width: 12px;
  }

  /* Global checkbox style */
  input[type="checkbox"] {
    @apply accent-gray-500 text-gray-500 h-4 w-4 rounded border-gray-500 ring-0 focus:ring-0 focus:outline-none flex-shrink-0 mr-2;
  }
}