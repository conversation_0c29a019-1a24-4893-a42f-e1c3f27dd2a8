/* Minimal polyfills for older browsers */
(function(global) {
  'use strict';

  // CustomEvent polyfill
  if (typeof window.CustomEvent !== 'function') {
    function CustomEvent(event, params) {
      params = params || { bubbles: false, cancelable: false, detail: null };
      var evt = document.createEvent('CustomEvent');
      evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
      return evt;
    }
    CustomEvent.prototype = window.Event.prototype;
    window.CustomEvent = CustomEvent;
  }

  // Object.assign
  if (typeof Object.assign !== 'function') {
    Object.defineProperty(Object, 'assign', {
      value: function assign(target) {
        if (target == null) {
          throw new TypeError('Cannot convert undefined or null to object');
        }
        var to = Object(target);
        for (var i = 1; i < arguments.length; i++) {
          var nextSource = arguments[i];
          if (nextSource != null) {
            for (var nextKey in nextSource) {
              if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                to[nextKey] = nextSource[nextKey];
              }
            }
          }
        }
        return to;
      },
      writable: true,
      configurable: true
    });
  }

  // Object.entries
  if (!Object.entries) {
    Object.entries = function(obj) {
      var ownProps = Object.keys(obj),
          i = ownProps.length,
          resArray = new Array(i);
      while (i--)
        resArray[i] = [ownProps[i], obj[ownProps[i]]];
      return resArray;
    };
  }

  // Object.fromEntries
  if (!Object.fromEntries) {
    Object.fromEntries = function(entries) {
      return Array.from(entries).reduce(function(obj, entry) {
        obj[entry[0]] = entry[1];
        return obj;
      }, {});
    };
  }

  // Array.from
  if (!Array.from) {
    Array.from = (function() {
      var toStr = Object.prototype.toString;
      var isCallable = function(fn) {
        return typeof fn === 'function' || toStr.call(fn) === '[object Function]';
      };
      var toInteger = function(value) {
        var number = Number(value);
        if (isNaN(number)) { return 0; }
        if (number === 0 || !isFinite(number)) { return number; }
        return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));
      };
      var maxSafeInteger = Math.pow(2, 53) - 1;
      var toLength = function(value) {
        var len = toInteger(value);
        return Math.min(Math.max(len, 0), maxSafeInteger);
      };

      return function from(arrayLike) {
        var C = this;
        var items = Object(arrayLike);
        if (arrayLike == null) {
          throw new TypeError('Array.from requires an array-like object - not null or undefined');
        }
        var mapFn = arguments.length > 1 ? arguments[1] : void undefined;
        var T;
        if (typeof mapFn !== 'undefined') {
          if (!isCallable(mapFn)) {
            throw new TypeError('Array.from: when provided, the second argument must be a function');
          }
          if (arguments.length > 2) {
            T = arguments[2];
          }
        }
        var len = toLength(items.length);
        var A = isCallable(C) ? Object(new C(len)) : new Array(len);
        var k = 0;
        var kValue;
        while (k < len) {
          kValue = items[k];
          if (mapFn) {
            A[k] = typeof T === 'undefined' ? mapFn(kValue, k) : mapFn.call(T, kValue, k);
          } else {
            A[k] = kValue;
          }
          k += 1;
        }
        A.length = len;
        return A;
      };
    }());
  }

  // Array.includes
  if (!Array.prototype.includes) {
    Object.defineProperty(Array.prototype, 'includes', {
      value: function(searchElement, fromIndex) {
        if (this == null) {
          throw new TypeError('"this" is null or not defined');
        }

        var o = Object(this);
        var len = o.length >>> 0;

        if (len === 0) {
          return false;
        }

        var n = fromIndex | 0;
        var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);

        function sameValueZero(x, y) {
          return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
        }

        while (k < len) {
          if (sameValueZero(o[k], searchElement)) {
            return true;
          }
          k++;
        }

        return false;
      },
      configurable: true,
      writable: true
    });
  }

  // ResizeObserver polyfill stub (minimal version)
  if (!window.ResizeObserver) {
    window.ResizeObserver = function(callback) {
      this.observe = function() {};
      this.unobserve = function() {};
      this.disconnect = function() {};
    };
  }
  
  // IntersectionObserver fallback (minimal version)
  if (!window.IntersectionObserver) {
    window.IntersectionObserver = function(callback, options) {
      this.observe = function() {};
      this.unobserve = function() {};
      this.disconnect = function() {};
      this.takeRecords = function() { return []; };
    };
    window.IntersectionObserverEntry = function() {};
  }
    
  // Add to global object
  global.core = global.core || {};
  global.core.polyfills = global.core.polyfills || {
    customEvent: true,
    resizeObserver: true,
    intersectionObserver: true
  };
  
  // Fix for button click events in older Chrome browsers
  (function() {
    // Check if this is a legacy Chrome browser that needs the fix
    var ua = navigator.userAgent;
    var isLegacyChromeWithButtonIssue = ua.includes('Chrome/') && 
                                         parseInt(ua.split('Chrome/')[1]) < 110;
                                         
    if (!isLegacyChromeWithButtonIssue) return;
    
    // Fix for button clicks not firing in older Chrome
    function fixButtonClick(event) {
      // Use mousedown as a backup for click events in legacy Chrome
      if (event.type === 'mousedown') {
        var target = event.target;
        
        // Find closest button or clickable element
        while (target && target !== document) {
          if (target.tagName === 'BUTTON' || 
              target.tagName === 'A' || 
              target.tagName === 'INPUT' && (target.type === 'button' || target.type === 'submit') ||
              target.getAttribute('role') === 'button') {
            
            // If this element has a click handler, trigger it
            if (typeof target.onclick === 'function') {
              // Prevent default mousedown
              event.preventDefault();
              // Call the click handler
              target.onclick(event);
              return;
            }
            
            // If no direct onclick handler but element is a link or submit button
            if (target.tagName === 'A' && target.href) {
              event.preventDefault();
              window.location.href = target.href;
              return;
            }
            
            if ((target.tagName === 'BUTTON' || 
                (target.tagName === 'INPUT' && target.type === 'submit')) && 
                target.form) {
              event.preventDefault();
              target.form.submit();
              return;
            }
          }
          
          target = target.parentNode;
        }
      }
    }
    
    // Add global event listener for mousedown events
    document.addEventListener('mousedown', fixButtonClick, true);
  })();
})(typeof window !== 'undefined' ? window : this); 