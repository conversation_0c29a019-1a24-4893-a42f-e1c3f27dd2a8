/**
 * Legacy Browser Fixes
 * 
 * This file contains all fixes needed for legacy browsers like old Chrome,
 * old Firefox, IE, etc. It's loaded only when a legacy browser is detected.
 */

// Load required polyfills
(function() {
  var polyfillsToLoad = [
    '/static/js/polyfills/minimal-polyfills.js',
    '/static/js/polyfills/es6-promise.min.js',
    '/static/js/polyfills/fetch.umd.js'
  ];
  
  function loadPolyfill(url) {
    var script = document.createElement('script');
    script.src = url;
    script.async = false; // Keep execution order
    document.head.appendChild(script);
  }
  
  polyfillsToLoad.forEach(loadPolyfill);
})();

// Cookie helper functions
function setCookie(name, value, days) {
  var expires = "";
  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

function getCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for(var i=0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

// Function to fix button click handlers in legacy Chrome
function fixLegacyButtons() {
  // Add mousedown handlers to all buttons to ensure they work in legacy Chrome
  var allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"], .btn, [role="button"]');
  
  allButtons.forEach(function(button) {
    // Skip if button already has our fix applied
    if (button.hasAttribute('data-legacy-fix')) return;
    
    // Mark as fixed
    button.setAttribute('data-legacy-fix', 'true');
    
    // Get the original onclick handler if it exists
    var originalOnClick = button.onclick;
    
    // Add mousedown handler as a backup for click
    button.addEventListener('mousedown', function(e) {
      // If button has an onclick, trigger it
      if (originalOnClick && typeof originalOnClick === 'function') {
        originalOnClick.call(this, e);
      }
    });
  });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Show legacy browser warning banner if not dismissed
  var warningBanner = document.getElementById('legacy-browser-warning');
  var closeButton = document.getElementById('close-legacy-warning');
  
  // Show the warning banner if it exists and hasn't been dismissed
  if (warningBanner && !getCookie('legacy_warning_dismissed')) {
    warningBanner.classList.remove('hidden');
    warningBanner.style.display = 'block';
    
    // Handle close button click
    if (closeButton) {
      // Add both click and mousedown handlers for better cross-browser compatibility
      closeButton.addEventListener('click', handleCloseWarning);
      closeButton.addEventListener('mousedown', handleCloseWarning);
      
      function handleCloseWarning(e) {
        e.preventDefault();
        e.stopPropagation();
        warningBanner.style.display = 'none';
        // Set cookie to remember the dismissal for 1 day
        setCookie('legacy_warning_dismissed', 'true', 1);
        return false;
      }
    }
  }
  
  // Apply button fixes
  fixLegacyButtons();
  
  // Force desktop view on screens wider than 1024px
  if (window.innerWidth >= 1024) {
    setTimeout(function() {
      // Make sure filters are expanded on desktop in legacy browsers
      if (typeof Alpine !== 'undefined' && Alpine.store && Alpine.store('filters')) {
        try {
          // First approach - manipulating Alpine's internal state
          var filtersStore = Alpine.store('filters');
          
          // Force all mobile filters to open state on desktop
          if (filtersStore.mobileFiltersOpen) {
            filtersStore.mobileFiltersOpen = {
              categories: true,
              extraAttributes: true,
              rangeFilters: true,
              vehicleFilter: true
            };
          }
          
          // Force all panels to be visible using DOM manipulation as backup
          document.querySelectorAll('.lg\\:block').forEach(function(el) {
            if (getComputedStyle(el).display === 'none') {
              el.style.display = 'block';
            }
          });
          
        } catch (e) {
          console.warn('Legacy browser fix error:', e);
        }
      }
      
      // Apply button fixes again after Alpine.js initialization
      fixLegacyButtons();
    }, 800); // Increased delay to ensure Alpine is fully initialized
  }
});

// Handle window resize to maintain proper layout
var resizeTimeout;
window.addEventListener('resize', function() {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(function() {
    var isDesktopView = window.innerWidth >= 1024;
    
    // Re-apply fixes when window is resized
    if (isDesktopView) {
      document.querySelectorAll('[x-show="mobileFiltersOpen.categories"], [x-show="mobileFiltersOpen.extraAttributes"], [x-show="mobileFiltersOpen.rangeFilters"], [x-show="mobileFiltersOpen.vehicleFilter"]')
        .forEach(function(el) {
          el.style.display = 'block';
        });
    }
    
    // Apply button fixes after resize
    fixLegacyButtons();
  }, 250);
});

// Check for DOM changes and fix new buttons
if (typeof MutationObserver !== 'undefined') {
  var observer = new MutationObserver(function(mutations) {
    fixLegacyButtons();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
} else {
  // Fallback for browsers without MutationObserver
  setInterval(fixLegacyButtons, 2000);
} 