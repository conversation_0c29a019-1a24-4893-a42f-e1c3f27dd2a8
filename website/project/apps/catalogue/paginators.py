import hashlib
import json
import time
import logging
from django.core.paginator import <PERSON><PERSON><PERSON>
from django.core.cache import cache

logger = logging.getLogger('apps.catalogue')

class CachedPaginator(Paginator):
    """
    Custom paginator that caches the count for storefront catalog pages
    with per-session caching and cache invalidation support.
    """
    
    def __init__(self, object_list, per_page, request=None, filter_context=None, **kwargs):
        """
        Initialize the paginator with request for session-based caching.
        
        Args:
            object_list: The queryset to paginate
            per_page: Number of items per page
            request: The current request object (needed for session ID)
            filter_context: Additional filtering context not in request params
        """
        super().__init__(object_list, per_page, **kwargs)
        self.request = request
        self.filter_context = filter_context or {}
        self.cache_timeout = kwargs.get('cache_timeout', 1800)  # 30 minutes default
        
        # Get or create a timestamp for this session's browsing session
        if request and not request.session.get('browsing_timestamp'):
            request.session['browsing_timestamp'] = str(int(time.time()))
            
        self.cache_key = self._generate_cache_key()
        
    def _generate_cache_key(self):
        """Generate a unique cache key based on session and current filters"""
        if not self.request:
            return 'product_count_default'
            
        # Get session ID and timestamp
        session_id = self.request.session.session_key or 'anonymous'
        timestamp = self.request.session.get('browsing_timestamp', '0')
        
        # Create a hash of the current URL query parameters (filters)
        params = self.request.GET.copy()
        if 'page' in params:
            del params['page']  # Remove page from cache key
            
        filter_string = params.urlencode()
        
        # Add additional filter context to hash if provided
        if self.filter_context:
            filter_context_str = json.dumps(self.filter_context, sort_keys=True)
            filter_hash = hashlib.md5(f"{filter_string}_{filter_context_str}".encode()).hexdigest()
        else:
            filter_hash = hashlib.md5(filter_string.encode()).hexdigest()
        
        return f"product_count_{session_id}_{filter_hash}_{timestamp}"
        
    @property
    def count(self):
        """Get count from cache or calculate and store it"""
        count = cache.get(self.cache_key)
        if count is None:
            count = super().count
            cache.set(self.cache_key, count, self.cache_timeout)
        return count
    
    @classmethod
    def clear_session_cache(cls, request):
        """
        Clear all product count caches for the current session by updating the timestamp.
        This effectively invalidates all existing caches for this session.
        """
        if not request or not hasattr(request, 'session'):
            return
            
        # Update the browsing timestamp to invalidate all existing caches
        request.session['browsing_timestamp'] = str(int(time.time()))