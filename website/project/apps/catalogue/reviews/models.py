from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Sum, Count
from django.utils.translation import gettext_lazy as _

from oscar.apps.catalogue.reviews.abstract_models import AbstractProductReview
from oscar.core.compat import AUTH_USER_MODEL
from oscar.core import validators


class ApprovedReviewsManager(models.Manager):
    def get_queryset(self):
        queryset = super(ApprovedReviewsManager, self).get_queryset()
        return queryset.all().filter(status=1)


class OrderReview(models.Model):
    order = models.ForeignKey(
        'order.Order', verbose_name=_('Order'), related_name='reviews', null=True, on_delete=models.SET_NULL
    )
    # Scores are between 0 and 5
    SCORE_CHOICES = tuple([(x, x) for x in range(0, 11)])
    score = models.SmallIntegerField(_("Score"), choices=SCORE_CHOICES)

    title = models.CharField(max_length=255, verbose_name=_("Review title"), validators=[validators.non_whitespace])
    body = models.TextField(_("Body"))

    # User information.
    user = models.ForeignKey(
        AUTH_USER_MODEL, related_name='order_reviews', null=True, blank=True, on_delete=models.CASCADE
    )

    # Fields to be completed if user is anonymous
    name = models.CharField(_("Name"), max_length=255, null=True, blank=True)
    email = models.EmailField(_("Email"), null=True, blank=True)
    homepage = models.URLField(_("URL"), null=True, blank=True)

    FOR_MODERATION, APPROVED, REJECTED = list(range(0, 3))
    STATUS_CHOICES = (
        (FOR_MODERATION, _("Requires moderation")),
        (APPROVED, _("Approved")),
        (REJECTED, _("Rejected")),
    )
    default_status = APPROVED
    if settings.OSCAR_MODERATE_REVIEWS:
        default_status = FOR_MODERATION
    status = models.SmallIntegerField(_("Status"), choices=STATUS_CHOICES, default=default_status)

    date_created = models.DateTimeField(auto_now_add=True, db_index=True)

    # Managers
    objects = models.Manager()
    approved = ApprovedReviewsManager()

    class Meta:
        unique_together = (('order', 'user'),)
        verbose_name = _('Order review')
        verbose_name_plural = _('Order reviews')

    def __str__(self):
        return self.title

    def clean(self):
        self.title = self.title.strip()
        self.body = self.body.strip()
        if not self.user:
            if not (self.name and self.email):
                raise ValidationError(_("Anonymous reviews must include a name and an email"))
            if self.email.lower() != self.order.guest_email.lower():
                raise ValidationError(_("Only owner of an order can leave a review. Check your email address."))

    # Properties
    @property
    def is_anonymous(self):
        return self.user is None

    @property
    def pending_moderation(self):
        return self.status == self.FOR_MODERATION

    @property
    def is_approved(self):
        return self.status == self.APPROVED

    @property
    def is_rejected(self):
        return self.status == self.REJECTED

    @property
    def country(self):
        return self.order.shipping_address.country.iso_3166_1_a2


from oscar.apps.catalogue.reviews.models import *
