# -*- coding: utf-8 -*-


from django.db import migrations, models
import oscar.apps.catalogue.reviews.utils


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0003_auto_20220802_1350'),
    ]

    operations = [
        migrations.AlterField(
            model_name='productreview',
            name='email',
            field=models.EmailField(max_length=254, verbose_name='Email', blank=True),
        ),
        migrations.AlterField(
            model_name='productreview',
            name='status',
            field=models.SmallIntegerField(default=oscar.apps.catalogue.reviews.utils.get_default_review_status, verbose_name='Status', choices=[(0, 'Requires moderation'), (1, 'Approved'), (2, 'Rejected')]),
        ),
    ]
