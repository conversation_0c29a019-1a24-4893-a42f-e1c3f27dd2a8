# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
from django.conf import settings
import oscar.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0002_auto_20220713_1737'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('reviews', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderReview',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                (
                    'score',
                    models.SmallIntegerField(
                        verbose_name='Score',
                        choices=[
                            (0, 0),
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                            (10, 10),
                        ],
                    ),
                ),
                (
                    'title',
                    models.CharField(
                        max_length=255, verbose_name='Review title', validators=[oscar.core.validators.non_whitespace]
                    ),
                ),
                ('body', models.TextField(verbose_name='Body')),
                ('name', models.CharField(max_length=255, null=True, verbose_name='Name', blank=True)),
                ('email', models.EmailField(max_length=75, null=True, verbose_name='Email', blank=True)),
                ('homepage', models.URLField(null=True, verbose_name='URL', blank=True)),
                (
                    'status',
                    models.SmallIntegerField(
                        default=0,
                        verbose_name='Status',
                        choices=[(0, 'Requires moderation'), (1, 'Approved'), (2, 'Rejected')],
                    ),
                ),
                ('date_created', models.DateTimeField(auto_now_add=True, db_index=True)),
                (
                    'order',
                    models.ForeignKey(
                        related_name='reviews',
                        on_delete=django.db.models.deletion.SET_NULL,
                        verbose_name='Order',
                        to='order.Order',
                        null=True,
                    ),
                ),
                (
                    'user',
                    models.ForeignKey(
                        related_name='order_reviews',
                        blank=True,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                        on_delete=models.CASCADE,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Order review',
                'verbose_name_plural': 'Order reviews',
            },
            bases=(models.Model,),
        ),
        migrations.AlterUniqueTogether(
            name='orderreview',
            unique_together=set([('order', 'user')]),
        ),
        migrations.AlterField(
            model_name='productreview',
            name='status',
            field=models.SmallIntegerField(
                default=0,
                verbose_name='Status',
                choices=[(0, 'Requires moderation'), (1, 'Approved'), (2, 'Rejected')],
            ),
            preserve_default=True,
        ),
    ]
