from django.urls import path

from oscar.apps.catalogue.reviews import apps
from oscar.core.loading import get_class


class CatalogueReviewsConfig(apps.CatalogueReviewsConfig):
    name = 'project.apps.catalogue.reviews'

    def ready(self):
        super().ready()
        self.list_view = get_class('catalogue.reviews.views', 'OrderReviewList')
        self.create_view = get_class('catalogue.reviews.views', 'CreateOrderReview')

    def get_urls(self):
        urls = [
            path('<order_number>/add/', self.create_view.as_view(), name='reviews-add'),
            path('<order_pk>/', self.list_view.as_view(), name='reviews-list'),
        ]
        return self.post_process_urls(urls)
