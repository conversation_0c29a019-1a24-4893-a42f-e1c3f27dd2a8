from django import forms
from django.apps import apps
from django.utils.translation import gettext_lazy as _

from project.apps.catalogue.reviews.models import OrderReview


class OrderReviewForm(forms.ModelForm):
    name = forms.CharField(label=_('Your name'), required=True)
    email = forms.EmailField(label=_('Email'), required=True)

    def __init__(self, order, user=None, *args, **kwargs):
        super(OrderReviewForm, self).__init__(*args, **kwargs)
        self.instance.order = order
        if user and user.is_authenticated:
            self.instance.user = user
            del self.fields['name']
            del self.fields['email']

        for field_name, field in list(self.fields.items()):
            self.fields[field_name].widget.attrs['class'] = 'form-control'

    class Meta:
        model = OrderReview
        fields = ('title', 'score', 'body', 'name', 'email')
