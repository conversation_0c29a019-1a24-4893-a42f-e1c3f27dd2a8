from django.apps import apps
from django.contrib import messages
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, DetailView, CreateView, View

from oscar.core.loading import get_classes
from oscar.apps.catalogue.reviews.signals import review_added

from project.apps.catalogue.reviews.forms import OrderReviewForm


class CreateOrderReview(CreateView):
    template_name = "oscar/catalogue/reviews/review_form.html"
    model = apps.get_model('reviews', 'OrderReview')
    order_model = apps.get_model('order', 'Order')
    form_class = OrderReviewForm
    view_signal = review_added

    def dispatch(self, request, *args, **kwargs):
        self.order = get_object_or_404(self.order_model, number=kwargs['order_number'])
        if self.order.has_review_by(request.user):
            messages.warning(self.request, _("You have already reviewed this order!"))
            return HttpResponseRedirect(self.get_success_url())

        return super(CreateOrderReview, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(CreateOrderReview, self).get_context_data(**kwargs)
        context['order'] = self.order
        return context

    def get_form_kwargs(self):
        kwargs = super(CreateOrderReview, self).get_form_kwargs()
        kwargs['order'] = self.order
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        response = super(CreateOrderReview, self).form_valid(form)
        messages.success(self.request, _("Thank you for reviewing this order"))
        self.send_signal(self.request, response, self.object)
        self.order.message_owner(
            'Review for order #{} waiting for moderation'.format(self.order.number),
            'New order review "{}" added and waiting for moderation. '
            'Go to the dashboard and moderate review.'.format(self.object.title),
        )
        return response

    def get_success_url(self):
        if self.request.user.is_authenticated:
            return reverse('customer:order', args=[self.order.number])
        else:
            return reverse('customer:anon-order', args=[self.order.number, self.order.verification_hash()])

    def send_signal(self, request, response, review):
        self.view_signal.send(sender=self, review=review, user=request.user, request=request, response=response)


class OrderReviewList(ListView):
    template_name = 'oscar/catalogue/reviews/review_list.html'
    context_object_name = "reviews"
    model = apps.get_model('reviews', 'OrderReview')
    order_model = apps.get_model('order', 'Order')
    paginate_by = 20

    def get_queryset(self):
        qs = self.model.approved.filter(order=self.kwargs['order_pk'])
        if 'sort_by' in self.request.GET and self.request.GET['sort_by'] == 'score':
            return qs.order_by('-score')
        return qs.order_by('-date_created')

    def get_context_data(self, **kwargs):
        context = super(OrderReviewList, self).get_context_data(**kwargs)
        context['order'] = get_object_or_404(self.order_model, pk=self.kwargs['order_pk'])
        return context
