from django import forms
from django.utils.translation import gettext_lazy as _

from captcha.fields import CaptchaField

from project.apps.catalogue.models import TecPapAttributeGroup
from project.apps.tecpap.models import Manufacturer, Model, Fuel, Body


class ContactVendorForm(forms.Form):
    name = forms.CharField(label=_('Your name'))
    email = forms.EmailField(label=_('Email'))
    message = forms.CharField(label=_('Message'), widget=forms.Textarea)
    captcha = CaptchaField()


class PriceOfferForm(forms.Form):
    name = forms.CharField(label=_('Your name'))
    email = forms.EmailField(label=_('Email'))
    price = forms.DecimalField(label=_('Your price'), max_digits=6, decimal_places=2)
    comment = forms.CharField(label=_('Comment'), widget=forms.Textarea, required=False)
    captcha = CaptchaField()


class CarPartsFilterForm(forms.Form):
    def __init__(self, *args, **kwargs):
        if 'products' in kwargs:
            self.products = kwargs['products']
            del kwargs['products']
        else:
            self.products = None
        if 'request' in kwargs:
            self.request = kwargs['request']
            del kwargs['request']
        else:
            self.request = None

        super(CarPartsFilterForm, self).__init__(*args, **kwargs)

        year_choices = [('', '------')] + TecPapAttributeGroup.YEAR_CHOICES
        manuf_choices = [('0', '------')] + list(Manufacturer.objects.all().values_list('id','brand'))
        model_choices = [('0', '------')] + Manufacturer.get_models(self.request)
        type_choices = [('0', '------')] + Model.get_types(self.request)
        body_choices = [('0', '------')] + list(Body.objects.values_list('id','name'))
        fuel_choices = [('0', '------')] + list(Fuel.objects.values_list('id','name'))

        self.fields['year'] = forms.ChoiceField(
            label=_('Year'), choices=year_choices, required=False)
        self.fields['manufacturers'] = forms.ChoiceField(
            label=_('Make'),
            choices=manuf_choices, required=False)
        self.fields['models'] = forms.ChoiceField(
            label=_('Model'),
            choices=model_choices, required=False)
        self.fields['types'] = forms.ChoiceField(
            label=_('Type'), 
            choices=type_choices, required=False)
        self.fields['fuel'] = forms.ChoiceField(
            label=_('Fuel'), choices=fuel_choices, required=False)
        self.fields['bodies'] = forms.ChoiceField(
            label=_('Body'), choices=body_choices, required=False)

        for field_name, field in list(self.fields.items()):
            self.fields[field_name].widget.attrs['class'] = 'wide_select nofocus'
