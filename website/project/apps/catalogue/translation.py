from modeltranslation.translator import translator, TranslationOptions

from project.apps.catalogue.models import (
    Product,
    ProductClass,
    ProductExtraAttribute,
    ProductDescriptionTemplate,
    Category,
)


class ProductTranslationOptions(TranslationOptions):
    fields = ('title', 'description')


class ProductClassTranslationOptions(TranslationOptions):
    fields = ('name',)


class ProductExtraAttributeTranslationOptions(TranslationOptions):
    fields = ('name',)


class ProductDescriptionTemplateTranslationOptions(TranslationOptions):
    fields = ('description',)


class CategoryTranslationOptions(TranslationOptions):
    fields = ('name', 'description')


translator.register(Product, ProductTranslationOptions)
translator.register(ProductClass, ProductClassTranslationOptions)
translator.register(ProductExtraAttribute, ProductExtraAttributeTranslationOptions)
translator.register(ProductDescriptionTemplate, ProductDescriptionTemplateTranslationOptions)
translator.register(Category, CategoryTranslationOptions)
