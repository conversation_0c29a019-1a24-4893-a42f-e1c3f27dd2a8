import json
from django.core.cache import cache
from django.conf import settings
from django.utils import translation
from project.apps.catalogue.models import Category, Product
from project.apps.tecpap.models import Manufacturer, Model, Type
from django.db.models import Q
from project.apps.partner.models import Stock<PERSON><PERSON><PERSON>

def build_category_tree_json(language=None):
    """
    Build the category tree structure as a JSON-serializable dictionary.
    Returns the tree structure for storage in cache or database.
    
    Args:
        language: Optional language code to use for category names
    """
    # Set language temporarily if specified
    current_language = translation.get_language()
    if language:
        translation.activate(language)
    
    try:
        # Get categories with path length >= 2*steplen (depth >= 2)
        all_categories = list(Category.objects.filter(path__regex=f'.{{{Category.steplen * 2},}}')
                            .only('id', 'path', 'name', 'slug')
                            .order_by('path'))
        all_categories_dict = {cat.path: cat for cat in all_categories}

        # Build the tree structure
        for cat in all_categories:
            if len(cat.path) > Category.steplen * 2:
                parent_path = cat.path[:-Category.steplen]
                if not hasattr(all_categories_dict[parent_path], 'subcategories'):
                    all_categories_dict[parent_path].subcategories = []
                all_categories_dict[parent_path].subcategories.append(cat)

        # Get top-level categories (path length = 2*steplen)
        category_tree = sorted(
            [cat for path, cat in all_categories_dict.items() if len(path) == Category.steplen * 2],
            key=lambda item: item.name
        )

        # Convert to JSON-serializable format (strip out Django model instances)
        def category_to_dict(cat):
            cat_dict = {
                'id': cat.id,
                'name': cat.name,  # This will use translated name with current active language
                'slug': cat.slug,
                'path': cat.path,
                'subcategories': []
            }
            if hasattr(cat, 'subcategories'):
                cat_dict['subcategories'] = [category_to_dict(subcat) for subcat in cat.subcategories]
            return cat_dict

        tree_json = [category_to_dict(cat) for cat in category_tree]
        return tree_json
    finally:
        # Restore original language
        if language:
            translation.activate(current_language)

def update_category_tree_cache():
    """
    Precompute and store the category tree in Redis or cache for all configured languages.
    This ensures the category names are cached in all supported languages.
    """
    # Get all languages used in the site
    all_languages = []
    
    # Add main languages from settings
    for lang_code, lang_name in settings.LANGUAGES:
        if lang_code not in all_languages:
            all_languages.append(lang_code)
    
    # Add languages from branch settings
    for branch_code, branch_settings in getattr(settings, 'BRANCH_SETTINGS', {}).items():
        branch_languages = branch_settings.get('languages', [])
        for lang_code, lang_name in branch_languages:
            if lang_code not in all_languages:
                all_languages.append(lang_code)
    
    # Generate and cache category tree for each language
    results = {}
    for language in all_languages:
        tree_json = build_category_tree_json(language)
        cache_key = f'full_category_tree_{language}'
        cache.set(cache_key, json.dumps(tree_json), timeout=None)
        results[language] = tree_json
    
    # Also store a default tree for backward compatibility
    default_tree = build_category_tree_json()
    cache.set('full_category_tree', json.dumps(default_tree), timeout=None)
    
    return default_tree

# Also update get_category_tree in views to use language-specific cache
def get_category_tree(language_code=None):
    """
    Get the category tree for the specified language from cache.
    Falls back to generating a new one if not in cache.
    
    Args:
        language_code: Language code to retrieve. If None, uses current language.
    """
    if language_code is None:
        language_code = translation.get_language()
    
    # Try to get language-specific cache
    cache_key = f'full_category_tree_{language_code}'
    tree_data = cache.get(cache_key)
    
    if tree_data:
        return json.loads(tree_data)
    else:
        # Fallback - regenerate cache for this language
        tree_json = build_category_tree_json(language_code)
        cache.set(cache_key, json.dumps(tree_json), timeout=None)
        return tree_json
