from django import forms
from django.contrib import admin
from django.contrib import messages
from django.contrib.auth import get_user_model
from treebeard.admin import TreeAdmin
from treebeard.forms import movenodeform_factory

from .models import (
    Category,
    Product,
    ProductExtraAttribute,
    ProductExtraAttributeCategory,
    ProductImage,
)
from project.rrr.models import RrrCategory

from oscar.apps.catalogue import admin as catalogue_admin
from project.apps.catalogue.utils import update_category_tree_cache


class CustomCategoryAdmin(TreeAdmin):
    form = movenodeform_factory(Category)
    search_fields = ["name"]
    list_display = ("name", "id", "slug", "path", "numchild")
    list_per_page = 1200
    list_max_show_all = 1200
    filter_vertical = ["rrr_categories", "recar_categories", "ebay_categories"]

    actions = ['update_category_tree_cache_action']

    def update_category_tree_cache_action(self, request, queryset):
        """
        Custom admin action to update the category tree cache.
        Note: queryset is not used, as we update the entire tree.
        """
        try:
            tree_json = update_category_tree_cache()
            self.message_user(
                request,
                f"Category tree cache updated successfully with {len(tree_json)} top-level categories.",
                level=messages.SUCCESS
            )
        except Exception as e:
            self.message_user(
                request,
                f"Failed to update category tree cache: {str(e)}",
                level=messages.ERROR
            )
    update_category_tree_cache_action.short_description = "Update Category Tree Cache"


User = get_user_model()

class CustomProductAdmin(admin.ModelAdmin):
    list_display = ("title", "warehouse_code", "date_created", "owner", "subowner")
    readonly_fields = ("date_created","owner")
    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'date_created', 'owner', 'subowner')
        }),
    )
    list_filter = ('owner', 'subowner')
    search_fields = ('title', 'warehouse_code')

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('owner', 'subowner')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "owner":
            kwargs["queryset"] = User.objects.filter(product__isnull=False).distinct()
        if db_field.name == "subowner":
            kwargs["queryset"] = User.objects.filter(manager_products__isnull=False).distinct()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_list_filter(self, request):
        return (
            ('owner', admin.RelatedOnlyFieldListFilter),
            ('subowner', admin.RelatedOnlyFieldListFilter),
        )
    def has_delete_permission(self, request, obj=None):
        # Maybe to allow delete for superuser? But I don't know how to implement it (some attribute error)
        # if request.user.is_superuser:         
        #     return True
        return False
    

class ProductExtraAttributeAdmin(admin.ModelAdmin):
    list_display = ["name"]
    search_fields = ["name"]


class ProductExtraAttributeCategoryAdmin(admin.ModelAdmin):
    list_display = [
        "product_extra_attribute",
        "category",
        "rrr_category",
        "recar_category",
    ]
    search_fields = ["product_extra_attribute__name"]
    autocomplete_fields = ["category", "rrr_category", "recar_category"]


# import oscar.apps.catalogue.admin


admin.site.unregister(Category)
admin.site.register(Category, CustomCategoryAdmin)

admin.site.unregister(Product)
admin.site.register(Product, CustomProductAdmin)

admin.site.register(ProductExtraAttribute, ProductExtraAttributeAdmin)
admin.site.register(ProductExtraAttributeCategory, ProductExtraAttributeCategoryAdmin)

admin.site.unregister(ProductImage)
