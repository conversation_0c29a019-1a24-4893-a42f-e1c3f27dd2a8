# -*- coding: UTF-8 -*-
import datetime
import random
import os
import re
from decimal import Decimal as D
import logging
import time

from django.db import models, connection
from django.db.models import Su<PERSON>, Prefetch, Count, OuterRef, Exists
from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from django.core.mail import EmailMessage
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _, get_language

from oscar.apps.catalogue import abstract_models
from oscar.apps.catalogue.managers import ProductQuerySet
from oscar.core.utils import slugify
from PIL import Image, ImageDraw, ImageFont, ImageEnhance

from project.accounts import get_branch
from project.accounts.models import Account, Branch
from project.apps.tecpap.models import Manufacturer, Model, Type
from project.apps.shipping.models import ShippingZone, OrderAndItemCharges
from project.apps.order.models import PaymentMethod
from project.ebay.models import EbayCategory
from project.rrr.models import RrrCategory
from project.recar.models import RecarPartCategory
from project.utils import dictfetchall
from django.core.cache import cache

logger = logging.getLogger('performance')

class Category(abstract_models.AbstractCategory):
    steplen = 6

    sort = models.IntegerField(_("Sort"), default=100)
    weight = models.IntegerField(_("Weight"), default=1)
    rrr_categories = models.ManyToManyField(
        RrrCategory,
        limit_choices_to={"depth__gt": 2},
        verbose_name=_("RRR categories"),
        blank=True,
    )
    recar_categories = models.ManyToManyField(
        RecarPartCategory,
        verbose_name=_("Recar categories"),
        blank=True,
    )
    ebay_categories = models.ManyToManyField(
        EbayCategory,
        verbose_name=_("Ebay categories"),
        blank=True,
    )

    def get_name(self):
        return self.name

    def set_ancestors_are_public(self):
        pass

    @classmethod
    def get_categories_with_product_count(cls, products=None):
        cat_paths = products.values_list("categories__path", flat=True)
        cat_dict = {}
        for cat_path in cat_paths:
            if cat_path:
                if cat_path in cat_dict:
                    cat_dict[cat_path] += 1
                else:
                    cat_dict[cat_path] = 1
                path = cat_path
                while len(path) > cls.steplen:
                    path = path[0 : -(cls.steplen)]
                    if path in cat_dict:
                        cat_dict[path] += 1
                    else:
                        cat_dict[path] = 1
        return cat_dict

    @property
    def image_url(self):
        try:
            return self.image.url
        except ValueError:
            return "#"


class FakeCategory(object):
    category = None
    manufacturer = None
    model = None

    def __init__(self, category=None, manufacturer=None, model=None):
        self.category = category
        self.manufacturer = manufacturer
        self.model = model

    def __str__(self):
        return self.name

    @property
    def name(self):
        if self.manufacturer:
            if self.model:
                return "{} - {}".format(self.manufacturer.brand, self.model.name)
            else:
                return "{}".format(self.manufacturer.brand)
        else:
            return ""

    def get_parent(self):
        if self.manufacturer:
            if self.model:
                return self.__class__(None, self.manufacturer, self.model)
            else:
                return self.__class__(None, self.manufacturer, None)
        return None

    def get_child_categories(self):
        categories = []
        main_categories = Category.objects.filter(depth=2)
        for main_category in main_categories.all():
            for child in list(main_category.get_descendants()):
                if self.manufacturer:
                    if self.model:
                        categories.append(self.__class__(child, self.manufacturer, self.model))
                    else:
                        categories.append(self.__class__(child, self.manufacturer, None))
        return categories

    def child_categories_as_string(self):
        children = self.get_child_categories()
        return ", ".join([c.category.name for c in children])

    def get_url(self):
        url = ""
        if self.manufacturer:
            if self.model:
                kwargs = {
                    "manufacturer_slug": self.manufacturer.slug,
                    "model_slug": self.model.slug,
                }
                url = reverse("model-catalogue", kwargs=kwargs)
                if self.category:
                    kwargs.update({"category_slug": self.category.slug, "pk": self.category.id})
                    url = reverse("model-category", kwargs=kwargs)
            else:
                kwargs = {"manufacturer_slug": self.manufacturer.slug}
                url = reverse("manufacturer-catalogue", kwargs=kwargs)
                if self.category:
                    kwargs.update({"category_slug": self.category.slug, "pk": self.category.id})
                    url = reverse("manufacturer-category", kwargs=kwargs)
        return url

    def get_manufacturer_url(self):
        return self.__class__(manufacturer=self.manufacturer).get_url()

    def get_model_url(self):
        return self.__class__(manufacturer=self.manufacturer, model=self.model).get_url()

    def get_ajax_url(self):
        url = ""
        if self.manufacturer:
            if self.model:
                kwargs = {
                    "manufacturer_slug": self.manufacturer.slug,
                    "model_slug": self.model.slug,
                }
                url = reverse("model-ajax-catalogue", kwargs=kwargs)
                if self.category:
                    kwargs.update({"category_slug": self.category.slug, "pk": self.category.id})
                    url = reverse("model-ajax-category", kwargs=kwargs)
            else:
                kwargs = {"manufacturer_slug": self.manufacturer.slug}
                url = reverse("manufacturer-ajax-catalogue", kwargs=kwargs)
                if self.category:
                    kwargs.update({"category_slug": self.category.slug, "pk": self.category.id})
                    url = reverse("manufacturer-ajax-category", kwargs=kwargs)
        return url


class CustomProductQuerySet(ProductQuerySet):
    def browsable(self):
        return self.filter(parent=None, active=True)

    def visible(self):
        return self.browsable().filter(is_available=True)


class ProductExtraAttribute(models.Model):
    name = models.CharField(_("Name"), max_length=128, unique=True)
    categories = models.ManyToManyField(Category, verbose_name=_("Category"), through="ProductExtraAttributeCategory")

    class Meta:
        verbose_name = _("Product extra attribute")
        verbose_name_plural = _("Product extra attributes")

    def __str__(self):
        return self.name


class ProductExtraAttributeCategory(models.Model):
    product_extra_attribute = models.ForeignKey(
        ProductExtraAttribute, related_name="category_map", on_delete=models.CASCADE
    )
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    weight = models.IntegerField(_("Weight"), default=1)
    rrr_category = models.ForeignKey(RrrCategory, null=True, on_delete=models.SET_NULL)
    recar_category = models.ForeignKey(RecarPartCategory, null=True, on_delete=models.SET_NULL)

    class Meta:
        verbose_name = _("Product extra attribute category")
        verbose_name_plural = _("Product extra attribute categories")
        ordering = ["product_extra_attribute__name"]


class ProductDescriptionTemplate(models.Model):
    code = models.SlugField(_("Code"), max_length=128, unique=True)
    name = models.CharField(_("Name"), max_length=128, unique=True)
    owner = models.ForeignKey(
        User,
        verbose_name=_("Owner"),
        editable=False,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    description = models.TextField(_("Description"))

    class Meta:
        verbose_name = _("Product description template")
        verbose_name_plural = _("Product description templates")

    def __str__(self):
        return self.name


class FakeProductAttributesContainer(object):
    def __setstate__(self, state):
        self.__dict__ = state
        self.initialised = False

    def __init__(self, product):
        self.product = product
        self.initialised = False

    def __getattr__(self, name):
        return None

    def validate_attributes(self):
        pass

    def get_values(self):
        return []

    def get_value_by_attribute(self, attribute):
        return ""

    def get_all_attributes(self):
        return []

    def get_attribute_by_code(self, code):
        return None

    def __iter__(self):
        return []

    def save(self):
        pass


class Product(abstract_models.AbstractProduct):
    CONDITION_NEW = "new"
    CONDITION_USED = "used"
    CONDITION_BROKEN = "broken"

    CONDITION_CHOICES = (
        (CONDITION_NEW, _("New product")),
        (CONDITION_USED, _("Used product")),
        (CONDITION_BROKEN, _("Broken product")),
    )

    DPOSITION_LHD = "lhd"
    DPOSITION_RHD = "rhd"
    DPOSITION_UHD = "uhd"

    DPOSITION_CHOICES = (
        (DPOSITION_LHD, _("LHD")),
        (DPOSITION_RHD, _("RHD")),
        (DPOSITION_UHD, _("Universal")),
    )

    pap_product_id = models.IntegerField(
        editable=False, blank=True, null=True, db_index=True, unique=True
    )  # PapProject product id
    original_code = models.CharField(
        _("Original part. code(s)"),
        max_length=255,
        blank=True,
        default="",
        help_text=_("You can enter multiple codes separated by comma"),
    )
    original_code_cleaned = models.CharField(editable=False, max_length=255, blank=True, default="")
    warehouse_code = models.CharField(_("Warehouse code"), max_length=255, blank=True, default="")
    condition = models.CharField(_("Condition"), max_length=50, choices=CONDITION_CHOICES, default=CONDITION_USED)
    drive_position = models.CharField(
        _("Drive position"),
        max_length=20,
        choices=DPOSITION_CHOICES,
        blank=True,
        default="",
    )
    color = models.ForeignKey(
        "tecpap.Color",
        verbose_name=_("Color"),
        blank=True,
        null=True,
        default=None,
        on_delete=models.SET_NULL,
    )
    extra_code = models.CharField(_("Extra part. code"), max_length=255, blank=True, default="")
    extra_manufacturer = models.CharField(_("Extra manufacturer"), max_length=100, blank=True, default="")
    extra_attributes = models.ManyToManyField(ProductExtraAttribute, verbose_name=_("Extra attributes"), blank=True)
    package_weight = models.DecimalField(_("Package weight (kg.)"), max_digits=12, decimal_places=2, default=1)
    package_height = models.PositiveIntegerField(_("Package height (cm)"), default=0)
    package_width = models.PositiveIntegerField(_("Package width (cm)"), default=0)
    package_length = models.PositiveIntegerField(_("Package length (cm)"), default=0)
    shipping_methods = models.ManyToManyField(
        "shipping.OrderAndItemCharges", verbose_name=_("Shipping charges"), blank=True
    )
    description_template = models.ForeignKey(
        ProductDescriptionTemplate,
        verbose_name=_("Description template"),
        blank=True,
        null=True,
        default=None,
        on_delete=models.PROTECT,
    )
    meta_keywords = models.CharField(
        _("Meta keywords"),
        max_length=500,
        editable=False,
        help_text=_("Keywords used by search engines"),
        blank=True,
    )
    meta_description = models.CharField(
        _("Meta description"),
        max_length=1000,
        # editable=False,
        help_text=_("Description used by search engines"),
        blank=True,
    )
    owner = models.ForeignKey(User, verbose_name=_("Owner"), null=True, blank=True, on_delete=models.PROTECT)
    subowner = models.ForeignKey(
        User,
        related_name="manager_products",
        verbose_name=_("Subowner (manager)"),
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    active = models.BooleanField(_("Active"), default=True, db_index=True)
    summary = models.TextField(editable=False)
    commercial_id = models.CharField(max_length=20, editable=False, db_index=True)
    top_item = models.BooleanField(_("Top item"), default=False, db_index=True)
    is_available = models.BooleanField(editable=False, default=True, db_index=True)
    price_factor = models.DecimalField(_("Price factor"), decimal_places=2, max_digits=12, default=1)
    discount_perc = models.IntegerField(_("Discount, perc"), default=0)

    objects = CustomProductQuerySet.as_manager()

    def __init__(self, *args, **kwargs):
        super(abstract_models.AbstractProduct, self).__init__(*args, **kwargs)
        self.attr = FakeProductAttributesContainer(product=self)

    @classmethod
    def get_filter_form_products_count(cls, products):
        num_products = {
            "products": {},
            "num_products_for_category": {},
            "num_products_for_manufacturer": {},
            "num_products_for_model": {},
            "num_products_for_type": {},
            "num_products_for_attribute": {},
        }

        items = list(
            products.values(
                "id",
                "categories__id",
                "tecpap_attributes__manufacturer__id",
                "tecpap_attributes__model__id",
                "tecpap_attributes__typ__id",
                "extra_attributes__id",
            )
        )

        for item in items:
            if item["id"] not in num_products["products"]:
                if item["categories__id"]:
                    if item["categories__id"] not in num_products["num_products_for_category"]:
                        num_products["num_products_for_category"][item["categories__id"]] = 0
                    num_products["num_products_for_category"][item["categories__id"]] += 1

                if item["tecpap_attributes__manufacturer__id"]:
                    if (
                        item["tecpap_attributes__manufacturer__id"]
                        not in num_products["num_products_for_manufacturer"]
                    ):
                        num_products["num_products_for_manufacturer"][
                            int(item["tecpap_attributes__manufacturer__id"])
                        ] = 0
                    num_products["num_products_for_manufacturer"][
                        int(item["tecpap_attributes__manufacturer__id"])
                    ] += 1

                if item["tecpap_attributes__model__id"]:
                    if item["tecpap_attributes__model__id"] not in num_products["num_products_for_model"]:
                        num_products["num_products_for_model"][item["tecpap_attributes__model__id"]] = 0
                    num_products["num_products_for_model"][item["tecpap_attributes__model__id"]] += 1

                if item["extra_attributes__id"]:
                    if item["extra_attributes__id"] not in num_products["num_products_for_attribute"]:
                        num_products["num_products_for_attribute"][item["extra_attributes__id"]] = 0
                    num_products["num_products_for_attribute"][item["extra_attributes__id"]] += 1

            if item["tecpap_attributes__typ__id"]:
                if item["tecpap_attributes__typ__id"] not in num_products["num_products_for_type"]:
                    num_products["num_products_for_type"][item["tecpap_attributes__typ__id"]] = 0
                num_products["num_products_for_type"][item["tecpap_attributes__typ__id"]] += 1

            num_products["products"][item["id"]] = item["id"]
        return num_products

    @classmethod
    def check_and_fix_missing_shipping_methods(cls, products_to_be_fixed=None):
        from project.accounts.models import Account
        
        if not products_to_be_fixed:
            products_to_be_fixed = cls.objects.filter(active=True, shipping_methods__isnull=True)
        for product in products_to_be_fixed:
            # Use Account class method to get shipping templates
            shipping_templates = Account.get_shipping_templates_for_weight(product.package_weight)
            product.shipping_methods.set(shipping_templates)
            product.save()

        products_to_be_fixed = cls.objects.filter(active=True, shipping_methods__isnull=True)
        if products_to_be_fixed.count() > 0:
            site = Site.objects.get(domain__icontains="eu")
            products = "".join(
                [
                    '<li><a href="https://{}{}">{}</a></li>'.format(
                        site.domain,
                        p.get_absolute_url().replace("en-us", "en"),
                        p.commercial_id,
                    )
                    for p in products_to_be_fixed
                ]
            )

            subject = "Products without shipping methods"
            message = """
                <p>System detected following products without shipping methods:</p>
                <ul>
                {}
                </ul>
                <p>Please check product attributes (weight, height, width, length) and add proper values.</p>
            """.format(
                products
            )

            if settings.DEPLOYMENT == settings.DEPLOYMENT_LIVE:
                message_to = [settings.MANAGERS[0][1]]
                msg = EmailMessage(
                    subject,
                    message,
                    settings.OSCAR_FROM_EMAIL,
                    message_to,
                )
                msg.content_subtype = "html"
                msg.send()

    @classmethod
    def check_and_fix_missing_shipping_methods_for_main_countries(cls):
        query = """
            SELECT
                A.id,
                A.iso_3166_1_a2
            FROM (
                SELECT pr.id, c.iso_3166_1_a2, count(sc.country_id) AS shipping_methods
                FROM catalogue_product AS pr
                JOIN address_country AS c
                LEFT JOIN catalogue_product_shipping_methods AS sm ON sm.product_id = pr.id
                LEFT JOIN shipping_orderanditemcharges AS sch ON sch.id = sm.orderanditemcharges_id
                LEFT JOIN shipping_orderanditemcharges_countries AS sc ON sc.orderanditemcharges_id = sch.id AND sc.country_id = c.iso_3166_1_a2
                WHERE c.iso_3166_1_a2 in ('LT', 'DE', 'PL', 'ES', 'FR') AND pr.active = 1
                GROUP BY pr.id, c.iso_3166_1_a2
            ) AS A
            WHERE A.shipping_methods = 0
            ORDER BY A.id, A.iso_3166_1_a2
        """
        products = []
        cursor = connection.cursor()
        cursor.execute(query)
        products = dictfetchall(cursor)
        product_ids = [p["id"] for p in products]
        products_to_be_fixed = cls.objects.filter(id__in=product_ids)

        for product in products_to_be_fixed:
            product.shipping_methods.set(
                product.owner.account.get_shipping_templates(
                    product.package_weight,
                    product.package_height,
                    product.package_width,
                    product.package_length,
                )
            )
            product.save()

        cursor.execute(query)
        products = dictfetchall(cursor)
        product_ids = [p["id"] for p in products]
        products_to_be_fixed = cls.objects.filter(id__in=product_ids)

        if products_to_be_fixed.count() > 0:
            site = Site.objects.get(domain__icontains="eu")
            products = "".join(
                [
                    '<li><a href="https://{}{}">{}</a></li>'.format(
                        site.domain,
                        p.get_absolute_url().replace("en-us", "en"),
                        p.commercial_id,
                    )
                    for p in products_to_be_fixed
                ]
            )

            subject = "Products without shipping methods for main branch countries"
            message = """
                <p>System detected following products without shipping methods:</p>
                <ul>
                {}
                </ul>
                <p>Please check product attributes (weight, height, width, length) and add proper values.</p>
            """.format(
                products
            )

            if settings.DEPLOYMENT == settings.DEPLOYMENT_LIVE:
                message_to = [settings.MANAGERS[0][1]]
                msg = EmailMessage(
                    subject,
                    message,
                    settings.OSCAR_FROM_EMAIL,
                    message_to,
                )
                msg.content_subtype = "html"
                msg.send()

    def get_price(self):
        return self.stockrecords.all()[0].price

    def get_dpd_cash_countries(self):
        dpd_cash_countries = []
        try:
            dpd_cash_payment_method = PaymentMethod.objects.get(code="dpd_cash")
        except PaymentMethod.DoesNotExist:
            dpd_cash_payment_method = None

        if dpd_cash_payment_method:
            try:
                dpd_cash_account = Account.objects.get(user=self.owner, payment_methods=dpd_cash_payment_method)
            except Account.DoesNotExist:
                pass
            else:
                dpd_cash_countries = ["LT", "LV", "EE"]

            dpd_cash_branch_countries = list(
                Branch.objects.filter(payment_methods=dpd_cash_payment_method).values_list("country", flat=True)
            )
            dpd_cash_countries += dpd_cash_branch_countries
        return dpd_cash_countries

    def regenerate_title(self):
        self.set_commercial_id()
        self.original_code_cleaned = self.get_cleaned_original_code()
        self.generate_title_and_description()
        self.save()

    def set_attribute_summary(self):
        manufacturer = self.get_manufacturer()
        model = self.get_model()
        typ = self.get_type()
        self.summary = "; ".join([manufacturer, model, typ])

    def set_commercial_id(self):
        self.commercial_id = str(self.id).zfill(8)

    def get_cleaned_original_code(self):
        codes = self.original_code
        if codes.find(",") > -1:
            codes = codes.split(",")
        elif codes.find(";") > -1:
            codes = codes.split(";")
        else:
            codes = [codes]
        cleaned_codes = []
        for code in codes:
            if code:
                code = code.strip()
                code = re.sub("[^A-Za-z0-9]+", "", code)
                cleaned_codes.append(code)
        return ", ".join(cleaned_codes)

    def get_extra_attributes_line(self):
        return ", ".join([eattr.name for eattr in self.extra_attributes.all()])

    def generate_title_and_description(self):
        try:
            category = self.categories.all()[0]
        except IndexError:
            category = None

        try:
            attributes = self.tecpap_attributes.all()[0]
        except IndexError:
            attributes = None

        title_for_slug = ""
        if category and attributes:
            for lang, lang_name in settings.LANGUAGES:
                if self.original_code == self.original_code_cleaned:
                    code = self.original_code
                else:
                    code = "%s / %s" % (self.original_code, self.original_code_cleaned)

                try:
                    main_model = attributes.model.all()[0]
                except IndexError:
                    main_model = None

                model_name = None
                if main_model:
                    model_name = getattr(main_model, "name_%s" % lang)
                    model_name = model_name if model_name else main_model.name_en
                category_name = getattr(category, "name_%s" % lang)

                _brand = getattr(attributes.manufacturer, "brand")
                _attributes = " ".join(
                    [
                        getattr(extra_attribute, "name_%s" % lang)
                        for extra_attribute in self.extra_attributes.all()
                        if getattr(extra_attribute, "name_%s" % lang)
                    ]
                )
                _model = model_name if model_name else ""
                _year = attributes.year
                _category = category_name if category_name else category.name_en

                slug_parts = [
                    _brand,
                    self.original_code_cleaned,
                    _model,
                    _year,
                    _category,
                    _attributes,
                ]

                title_parts = [
                    _brand,
                    code,
                    _model,
                    _year,
                    _category,
                    _attributes,
                    self.extra_code,
                    self.extra_manufacturer,
                ]

                title_parts = [i for i in title_parts if i]
                title = " ".join(title_parts)

                if lang == "en":
                    slug_parts = [i for i in slug_parts if i]
                    title_for_slug = " ".join(slug_parts)
                description = title

                prefix = ""

                setattr(self, "title_%s" % lang, title[:255])
                setattr(self, "description_%s" % lang, description)

            self.slug = slugify(title_for_slug)[:255]

    def get_main_year(self):
        years = self.tecpap_attributes.all().values_list("year", flat=True)
        try:
            year = years[0]
        except IndexError:
            year = ""
        return year

    def get_main_manufacturer(self):
        manufacturers = self.tecpap_attributes.all().values_list("manufacturer__brand", flat=True)
        try:
            manufacturer = manufacturers[0]
        except IndexError:
            manufacturer = ""
        return manufacturer

    def get_main_model(self):
        models = self.tecpap_attributes.all().values_list("model__name", flat=True)
        try:
            model = models[0]
        except IndexError:
            model = ""
        return model

    def get_main_model_cleaned(self):
        models = self.tecpap_attributes.all().values_list("model__name", flat=True)
        try:
            model = models[0].partition('(')[0].strip()
        except (IndexError, AttributeError):
            model = ""
        return model

    def get_main_type(self):
        types = self.tecpap_attributes.all().values_list("typ__name", flat=True)
        if all(types):
            typ = types[0]
        else:
            typ = ""
        return typ

    def get_main_category(self):
        try:
            category = self.categories.all()[0].name
        except IndexError:
            category = ""
        return category.replace("\n", " ").replace("\r", "")

    def get_year(self):
        years = self.tecpap_attributes.all().values_list("year", flat=True)
        years = set(list(years))
        return format_html(", ".join(years))

    def get_manufacturer(self):
        manufacturers = self.tecpap_attributes.all().values_list("manufacturer__brand", flat=True)
        manufacturers = set(list(manufacturers))
        return format_html(", ".join(manufacturers))

    def get_model(self):
        models = self.tecpap_attributes.all().values_list("model__name", flat=True)
        models = set(list(models))
        return format_html(", ".join(models))

    def get_type(self):
        types = self.tecpap_attributes.all().values_list("typ__name", flat=True)
        if all(types):
            types = set(list(types))
        else:
            types = []
        return format_html(", ".join(types))

    def get_attributes(self):
        attributes = []
        attribute_groups = self.tecpap_attributes.all()
        for attribute_group in attribute_groups:
            manufacturer = attribute_group.manufacturer.brand
            models = attribute_group.model.all().values_list("name", flat=True)
            types = (
                attribute_group.typ.all().values_list("name", flat=True)
                if attribute_group.typ.all().count() > 0
                else []
            )
            group = ", ".join([i for i in [manufacturer, ", ".join(models), ", ".join(types)] if i])
            attributes.append(group)
        return attributes

    def get_title(self):
        return self.title.replace("\n", " ").replace("\r", "")

    def get_description(self):
        return self.description

    def get_meta_keywords(self):
        return self.get_title()

    def get_meta_description(self):
        branch = get_branch()
        site = Site.objects.get(domain__icontains=".{}".format(branch))
        category = self.get_main_category()

        description_template = random.choice(
            [
                # _(
                #     "Buy %(title)s %(code)s for %(brand)s %(model)s at best price."
                #     " We provide tracked worldwide shipping. We accept paypal, credit card or bank transfer payments."
                # ),
                #            _(
                #                'Get %(title)s %(code)s for %(brand)s %(model)s at best price.'
                #                ' We provide tracked worldwide shipping. We accept paypal, credit card or bank transfer payments.'
                #            ),
                #            _(
                #                'Looking for %(title)s %(code)s? Go to %(domain)s and get %(title)s for %(brand)s %(model)s at best price.'
                #                ' We provide tracked worldwide shipping. We accept paypal, credit card or bank transfer payments.'
                #            ),
                #            _(
                #                '%(title)s %(code)s for %(brand)s %(model)s is now available on %(domain)s.'
                #                ' We provide tracked worldwide shipping. We accept paypal, credit card or bank transfer payments.'
                #            ),
                #            _(
                #                '%(domain)s provides the best offers on %(title)s %(code)s for %(brand)s %(model)s %(year)s.'
                #                ' You can pay money by Paypal or any other transaction methods.'
                #            )
                _('%(title)s %(code)s for %(brand)s %(model)s %(year)s. Best price, fast shipping.')
            ]
        )

        return str(
            description_template
            % {
                "year": self.get_main_year(),
                "brand": self.get_main_manufacturer(),
                "model": self.get_main_model_cleaned(),
                "code": "%s / %s" % (self.original_code, self.original_code_cleaned),
                "title": "%s %s %s"
                % (
                    category,
                    " ".join([eat.name for eat in self.extra_attributes.all()]),
                    self.extra_code,
                ),
                "domain": site.domain,
            }
        )

    def get_shipping_preform(self, country):
        # Return None if country is not provided
        if not country or not hasattr(country, 'iso_3166_1_a2'):
            return None

        # Get all shipping methods and their countries in one query
        shipping_methods = self.shipping_methods.prefetch_related('countries').all()

        # Filter methods by country
        matching_methods = []
        for shipping_method in shipping_methods:
            if country and country.iso_3166_1_a2 in [c.iso_3166_1_a2 for c in shipping_method.countries.all()]:
                matching_methods.append(shipping_method)
        
        # If no matching methods, return None
        if not matching_methods:
            return None
            
        # Sort by price and return the cheapest one
        return sorted(matching_methods, key=lambda x: x.price_per_item)[0]

    def get_shipping_price(self, basket_class, shipping_method, country):
        shipping_preform = self.get_shipping_preform(country)
        if shipping_method == basket_class.SHIPPING_LOCAL_PICK_UP:
            if country.iso_3166_1_a2 == "LT":
                return D("0.00")
            else:
                return None
        if shipping_preform:
            if shipping_method == basket_class.SHIPPING_ECONOMY:
                return shipping_preform.price_per_item
            if shipping_method == basket_class.SHIPPING_EXPRESS:
                return shipping_preform.price_per_item_express
        return None

    def get_shipping_delay(self, shipping_country_code):
        from project.apps.pap.models import VehiclePart

        delay = 0

        if self.pap_product_id:
            try:
                product_country = VehiclePart.objects.get(id=self.pap_product_id).country
            except (VehiclePart.DoesNotExist, AttributeError):
                product_country = None
            else:
                try:
                    product_location_shipping_zone = ShippingZone.objects.get(countries__iso_3166_1_a2=product_country)
                except ShippingZone.DoesNotExist:
                    product_location_shipping_zone = None
                except ShippingZone.MultipleObjectsReturned:
                    product_location_shipping_zone = None

                try:
                    shipping_zone = ShippingZone.objects.get(countries__iso_3166_1_a2=shipping_country_code)
                except ShippingZone.DoesNotExist:
                    shipping_zone = None
                except ShippingZone.MultipleObjectsReturned:
                    shipping_zone = None

                if product_location_shipping_zone:
                    if shipping_zone:
                        if product_location_shipping_zone.id != shipping_zone.id:
                            delay = shipping_zone.shipping_delay
                    else:
                        delay = settings.DEFAULT_SHIPPING_DELAY_IN_DAYS

        return delay

    def is_full_car(self):
        return False

    def explain_condition_new(self):
        return str(
            _(
                "New product - has no defects and is ready to use according to its purpose. The seller provides a 2-year warranty"
            )
        )

    def explain_condition_used(self):
        return str(
            _(
                "Used product - may have external defects (dents, scratches); however, it is ready to use according to its purpose."
                " A warranty for a used item is provided; however, a period of this warranty is not defined. "
                "Item can be returned with compliance to terms and conditions stated in the ONLINE BUY-SELL AGREEMENT. "
            )
        )

    @property
    def multilingual_title(self):
        all_titles = [getattr(self, "title_%s" % lang[0]) for lang in settings.LANGUAGES]
        return " ".join(all_titles)

    @classmethod
    def check_availability(cls):
        from project.apps.partner.models import StockRecord

        def check(record):
            if record["num_in_stock"] is None:
                return 0
            if record["num_allocated"] is None:
                return record["num_in_stock"]
            return record["num_in_stock"] - record["num_allocated"]

        unavailable_product_ids = []
        stock_records = StockRecord.objects.all().values("product_id", "num_in_stock", "num_allocated")
        for stock_record in stock_records:
            if check(stock_record) <= 0:
                unavailable_product_ids.append(stock_record["product_id"])

        disabled = Product.objects.browsable().filter(id__in=unavailable_product_ids).update(is_available=False)
        enabled = Product.objects.browsable().exclude(id__in=unavailable_product_ids).update(is_available=True)

        return (disabled, enabled)

    def get_absolute_main_image_url(self, domain=None):
        try:
            image_url = self.primary_image().original.url
        except AttributeError:
            image_url = "/{}/image_not_found.jpg".format(settings.MEDIA_URL)
        if not domain:
            branch = get_branch()
            domain = Site.objects.get(domain__icontains=".{}".format(branch)).domain
        url = "https://{}/{}".format(domain, image_url)
        return url

    @classmethod
    def compare_stock(cls, triggered_by_admin=False):
        from project.apps.partner.models import StockRecord
        from project.apps.pap.models import (
            VehiclePartLocation,
            VehiclePartLocationAllocation,
        )

        # reservation_items = list(
        #     VehiclePartLocationAllocation.objects.filter(expires_at__isnull=False, num_allocated__gt=0).values()
        # )
        # reservations = {}

        # for reservation_item in reservation_items:
        #     location_id = reservation_item['vehicle_part_location_id']
        #     num_allocated = reservation_item['num_allocated']
        #     if location_id not in reservations:
        #         reservations[location_id] = 0
        #     reservations[location_id] += num_allocated

        pap_stock_items = list(VehiclePartLocation.objects.all().values())
        pap_stock = {}

        for pap_stock_item in pap_stock_items:
            if pap_stock_item["vehicle_part_id"] not in pap_stock:
                pap_stock[pap_stock_item["vehicle_part_id"]] = {
                    "num_in_stock": 0,
                    "num_allocated": 0,
                }

            pap_stock[pap_stock_item["vehicle_part_id"]]["num_in_stock"] += pap_stock_item["num_in_location"]
            pap_stock[pap_stock_item["vehicle_part_id"]]["num_allocated"] += pap_stock_item[
                "num_allocated"
            ]  # - reservations.get(pap_stock_item["id"], 0)

        partan_stock = StockRecord.objects.raw(
            """
            SELECT
                s.id,
                s.product_id,
                c.commercial_id,
                c.pap_product_id,
                c.warehouse_code,
                c.original_code,
                if(s.num_in_stock, s.num_in_stock, 0) AS num_in_stock,
                if(s.num_allocated, s.num_allocated, 0) AS num_allocated,
                if(s.num_in_stock, s.num_in_stock, 0) - if(s.num_allocated, s.num_allocated, 0) AS net_level,
                if(u.id, concat(u.first_name, u.last_name, ' <', u.email, '>'), '') AS manager
            FROM
                partner_stockrecord AS s
            INNER JOIN
                catalogue_product AS c ON c.id = s.product_id
            LEFT JOIN
                auth_user AS u ON u.id = c.subowner_id
            WHERE
                c.pap_product_id is not NULL
        """
        )

        invalid_products = []
        for stock_record in partan_stock:
            pap_stock_record = pap_stock.get(stock_record.pap_product_id)
            if pap_stock_record:
                if (
                    pap_stock_record["num_in_stock"] != stock_record.num_in_stock
                    or pap_stock_record["num_allocated"] != stock_record.num_allocated
                ):
                    deactivated = False
                    if pap_stock_record["num_in_stock"] < stock_record.num_in_stock:
                        cls.objects.filter(id=stock_record.product_id).update(active=False)
                        deactivated = True

                    rr = [
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.product_id
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.pap_product_id
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.num_in_stock
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.num_allocated
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            pap_stock_record["num_in_stock"]
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            pap_stock_record["num_allocated"]
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.warehouse_code
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.original_code
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            stock_record.manager
                        ),
                        '<td style="border: 1px solid black; border-collapse: collapse; padding:5px">{}</td>'.format(
                            "DISABLED" if deactivated else "ACTIVE"
                        ),
                    ]
                    invalid_products.append("<tr>{}</tr>".format("".join(rr)))

        if invalid_products:
            subject = "Inconsistent products"
            message = """
                <p>System detected and automatically deactivated some products with invalid stock</p>
                <p>List of products to be fixed:</p>
                <table style="border: 1px solid black; border-collapse: collapse;">
                    <tr>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Partan product ID</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Vehicle part ID</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Partan<br>stock available</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Partan<br>stock allocated</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Warehouse<br>stock available</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Warehouse<br>stock allocated</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Warehouse</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Code</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Manager</th>
                        <th style="border: 1px solid black; border-collapse: collapse; padding:5px">Status</th>
                    </tr>
                    {}
                </table>
                <p>
                Please check the stock for disabled products and update the stock on the main PAP warehouse
                or contact a system administrator in case the problem is still not solved.
                </p>
                <p>
                You can trigger this consistency check from admin: navigate "Partner"->"Stock records",
                select at least one stock record, chose action "Check stock consistency"
                from actions dropdown and pres GO.
                </p>
            """.format(
                "".join(invalid_products)
            )

            if settings.DEPLOYMENT == settings.DEPLOYMENT_LIVE and not triggered_by_admin:
                message_to = [settings.MANAGERS[0][1], settings.ADMINS[0][1]]
                msg = EmailMessage(
                    subject,
                    message,
                    settings.OSCAR_FROM_EMAIL,
                    message_to,
                )
                msg.content_subtype = "html"
                msg.send()

            return message

    def get_shipping_info(self, country=None):
        """Get shipping information for product"""
        # Direct call to parent method
        shipping_info = super().get_shipping_info(country)
        
        return shipping_info

    @classmethod
    def get_shipping_info_batch(cls, products, country=None):
        """Get shipping information for multiple products in batch"""

        # Process all products directly 
        results = {}
        
        # Process each product directly
        for product in products:
            results[product.id] = product.get_shipping_info(country)
            
        return results
    
class ImageResizeException(Exception):
    pass

class AbstractImageModification(models.Model):
    resized_at = models.DateTimeField(editable=False, null=True, blank=True)
    resize_failed_at = models.DateTimeField(editable=False, null=True, blank=True)
    resize_failure_reason = models.TextField(editable=False, null=True, blank=True)

    class Meta:
        abstract = True

    def save_without_extra_actions(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def resize_image(self):
        try:
            if hasattr(self, 'image'):
                img = Image.open(self.image)
                path = self.image.path
            elif hasattr(self, 'original'):
                img = Image.open(self.original)
                path = self.original.path
            else:
                raise Exception("The subclass does not have `image` nor `original` image fields!")

            width, height = img.size

            width_scale = 1280 / width
            height_scale = 720 / height

            scale_factor = min(width_scale, height_scale)

            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)

            img.thumbnail((new_width, new_height))
            img.save(path)
        except Exception as e:
            self.resize_failed_at = timezone.now()
            self.resize_failure_reason = e
            self.save_without_extra_actions()
            raise ImageResizeException(e)
        else:
            self.resized_at = timezone.now()
            self.save()
            return width, height, new_width, new_height, path


class ProductImage(abstract_models.AbstractProductImage, AbstractImageModification):
    def save(self, *args, **kwargs):
        self.add_watermark()
        super(ProductImage, self).save(*args, **kwargs)

    def add_watermark(self, text=None, angle=0):
        text = settings.OSCAR_PRODUCT_WATERMARK_TEXT
        opacity = 0.25
        open_sans_fonts = os.path.join(settings.PROJECT_MODULE, "apps", "order", "open_sans_fonts")
        font = os.path.join(open_sans_fonts, "OpenSans-Bold.ttf")
        img = Image.open(self.original.path).convert("RGB")
        watermark = Image.new("RGBA", img.size, (0, 0, 0, 0))
        size = 2
        n_font = ImageFont.truetype(font, size)

        bbox = n_font.getbbox(text)
        n_width, n_height = bbox[2] - bbox[0], bbox[3] - bbox[1]

        while n_width + n_height < watermark.size[0]:
            size += 2
            n_font = ImageFont.truetype(font, size)
            bbox = n_font.getbbox(text)
            n_width, n_height = bbox[2] - bbox[0], bbox[3] - bbox[1]

        size = int(size / 4)
        n_font = ImageFont.truetype(font, size)
        bbox = n_font.getbbox(text)
        n_width, n_height = bbox[2] - bbox[0], bbox[3] - bbox[1]

        draw = ImageDraw.Draw(watermark, "RGBA")
        #        draw.text(((watermark.size[0] - n_width) / 2,
        #                  (watermark.size[1] - n_height) / 2),
        #                  text, font=n_font)
        draw.text(
            ((watermark.size[0] - n_width - 10), (watermark.size[1] - n_height - 5)),
            text,
            font=n_font,
        )
        watermark = watermark.rotate(angle, Image.BICUBIC)
        alpha = watermark.split()[3]
        alpha = ImageEnhance.Brightness(alpha).enhance(opacity)
        watermark.putalpha(alpha)
        Image.composite(watermark, img, watermark).save(self.original.path, "JPEG")


class TecPapAttributeGroup(models.Model):
    YEAR_CHOICES = [(str(i), str(i)) for i in range(1920, int(datetime.date.today().year) + 1)]

    product = models.ForeignKey(Product, related_name="tecpap_attributes", on_delete=models.CASCADE)
    year = models.CharField(max_length=20, choices=YEAR_CHOICES, default="2015")
    manufacturer = models.ForeignKey(Manufacturer, null=True, on_delete=models.SET_NULL)
    model = models.ManyToManyField(Model)
    typ = models.ManyToManyField(Type, blank=True)


class ProductClass(abstract_models.AbstractProductClass):
    def get_name(self):
        return self.name

    def __str__(self):
        return self.name
    
# Efficient product count by category path
from django.db.models import Count

def get_product_counts_by_category_path(base_qs):
    """
    Returns a dict mapping category path to product count for all categories
    under the root of the current base_qs. Counts are summed up the tree so that
    parent categories include the sum of their children's counts.
    """
    from .models import Category
    from collections import defaultdict
    from django.db.models import Count, OuterRef, Exists
    
    # Optimized solution - use Exists instead of direct 'product__in' condition
    category_counts = (
        Category.objects
        .filter(
            Exists(
                base_qs.filter(
                    id=OuterRef('productcategory__product__id')
                )
            )
        )
        .values('path')
        .annotate(count=Count('productcategory__product', distinct=True))
    )
    
    # First, build a dict of direct counts
    direct_counts = {item['path']: item['count'] for item in category_counts}
    # Now, sum up counts for parent categories
    summed_counts = defaultdict(int)
    steplen = Category.steplen
    for path, count in direct_counts.items():
        # Add count to this category and all its ancestors
        for i in range(steplen, len(path)+1, steplen):
            parent_path = path[:i]
            summed_counts[parent_path] += count
    return dict(summed_counts)

def get_extra_attribute_counts(base_qs):
    """
    Returns a dict mapping extra attribute id to product count for the current base_qs.
    Uses ManyToMany through table, so it's efficient even with large data amounts.
    Returns only attributes that have at least one product.
    """
    Product = base_qs.model
    through = Product.extra_attributes.through
    from django.db.models import Count, OuterRef, Exists

    # Optimized solution - use Exists instead of direct 'product_id__in=product_ids' condition
    counts = (
        through.objects
        .filter(
            Exists(
                base_qs.filter(id=OuterRef('product_id'))
            )
        )
        .values('productextraattribute_id')
        .annotate(count=Count('product_id', distinct=True))
    )
    return {item['productextraattribute_id']: item['count'] for item in counts}

from oscar.apps.catalogue.models import *
