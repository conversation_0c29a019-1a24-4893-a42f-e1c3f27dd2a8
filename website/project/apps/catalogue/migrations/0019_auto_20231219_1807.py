# Generated by Django 3.2.18 on 2023-12-19 16:07

from django.db import migrations


def map_partan_categories_with_recar(apps, schema_editor):
    Category = apps.get_model("catalogue", "Category")

    data = [
        [233, ["bendrinė kategorija"]],
        [255, ["5567"]],
        [257, ["2804, 2824,"]],
        [361, ["42704, 42705"]],
        [359, ["nėra "]],
        [360, ["2804, 2824"]],
        [358, ["2772"]],
        [254, ["41998"]],
        [252, ["42550, 3093"]],
        [256, ["atributai"]],
        [356, ["atributai"]],
        [357, ["atributai "]],
        [355, ["atributai"]],
        [258, ["bendrinė kategorija"]],
        [364, ["41972"]],
        [366, ["nėra "]],
        [362, ["5525"]],
        [363, ["20470"]],
        [365, ["5538"]],
        [253, ["atributai"]],
        [251, ["bendrinė kategorija"]],
        [352, ["20484"]],
        [353, ["20484"]],
        [354, ["20484"]],
        [351, ["atributai"]],
        [249, ["2733"]],
        [348, ["nėra"]],
        [347, ["2773"]],
        [350, ["20498"]],
        [349, ["nėra"]],
        [346, ["2733"]],
        [345, ["2772"]],
        [250, ["41963"]],
        [4, ["bendrinė kategorija"]],
        [24, ["3082"]],
        [26, ["45741"]],
        [167, ["4224"]],
        [166, ["2695, 2698, 2699"]],
        [168, ["42799"]],
        [25, ["4224"]],
        [21, ["2460, 2696, 2697"]],
        [154, ["42002"]],
        [155, ["42593"]],
        [153, ["4224"]],
        [27, ["bendrinė kategorija"]],
        [171, ["3077"]],
        [170, ["42567"]],
        [177, ["41973"]],
        [172, ["3077"]],
        [175, ["nera "]],
        [174, ["nera "]],
        [169, ["3077"]],
        [176, ["nera "]],
        [173, ["3077"]],
        [178, ["2702"]],
        [23, ["bendrinė kategorija"]],
        [163, ["4219"]],
        [165, ["42308"]],
        [161, ["2702"]],
        [164, ["2702"]],
        [162, ["45741"]],
        [28, ["nera"]],
        [22, ["bendrinė kategorija"]],
        [156, ["2458"]],
        [158, ["4219"]],
        [159, ["42771"]],
        [160, ["2694"]],
        [157, ["2694"]],
        [231, ["bendrinė kategorija"]],
        [242, ["nera"]],
        [311, ["2861"]],
        [312, ["45741"]],
        [313, ["2861"]],
        [314, ["2861"]],
        [310, ["2858"]],
        [309, ["2926"]],
        [308, ["42751"]],
        [238, ["bendrinė kategorija"]],
        [284, ["3430"]],
        [283, ["2691"]],
        [280, ["2453"]],
        [285, ["nėra"]],
        [282, ["2915"]],
        [281, ["2915"]],
        [945, ["42888"]],
        [241, ["bendrinė kategorija"]],
        [301, ["3076"]],
        [294, ["2704"]],
        [295, ["2705"]],
        [299, ["42756"]],
        [302, ["42757"]],
        [305, ["45741"]],
        [297, ["2687"]],
        [307, ["2691"]],
        [300, ["3076"]],
        [298, ["42561"]],
        [304, ["42560"]],
        [296, ["2703"]],
        [293, ["2462"]],
        [303, ["5566"]],
        [306, ["nėra"]],
        [244, ["bendrinė kategorija"]],
        [331, ["3092"]],
        [330, ["nėra"]],
        [325, ["42613"]],
        [324, ["2878"]],
        [327, ["atributai"]],
        [326, ["45741"]],
        [328, ["nėra"]],
        [329, ["nėra"]],
        [239, ["Bendrinė kategorija"]],
        [287, ["44478, 3430"]],
        [289, ["nėra"]],
        [290, ["nera"]],
        [286, ["2453"]],
        [288, ["2858"]],
        [243, ["bendrinė kategorija"]],
        [318, ["nėra"]],
        [320, ["42621"]],
        [315, ["2690"]],
        [317, ["2810"]],
        [316, ["2811"]],
        [319, ["nėra"]],
        [322, ["3272"]],
        [323, ["20487"]],
        [321, ["42621"]],
        [240, ["bendrine kategorija"]],
        [291, ["atributai"]],
        [292, ["nera"]],
        [6, ["2467"]],
        [39, ["bendrine kategorija"]],
        [38, ["2467"]],
        [207, ["41146"]],
        [204, ["3083"]],
        [205, ["42867"]],
        [206, ["2454"]],
        [225, ["42666"]],
        [226, ["42565"]],
        [224, ["42520"]],
        [223, ["2454"]],
        [210, ["42867"]],
        [279, ["2465"]],
        [208, ["2467"]],
        [229, ["4219"]],
        [228, ["42484"]],
        [230, ["45642"]],
        [227, ["2467"]],
        [211, ["41978"]],
        [209, ["42617"]],
        [202, ["2490"]],
        [977, ["41955"]],
        [217, ["2490"]],
        [220, ["42024"]],
        [438, ["45342,  2910"]],
        [218, ["2864, 2865; 2866"]],
        [222, ["42900"]],
        [221, ["42025"]],
        [219, ["5533, 3455"]],
        [203, ["3473"]],
        [944, ["2744"]],
        [37, ["Bendrinė kategorija "]],
        [200, ["3181"]],
        [199, ["3181"]],
        [201, ["42861"]],
        [3, ["2459"]],
        [20, ["42709"]],
        [151, ["4219"]],
        [152, ["nėra"]],
        [150, ["42709"]],
        [18, ["Bendrinė kategorija "]],
        [138, ["2701"]],
        [135, ["2459"]],
        [139, ["42559"]],
        [136, ["nėra"]],
        [141, ["4219"]],
        [140, ["nėra"]],
        [137, ["4211"]],
        [142, ["42594"]],
        [237, ["Bendrinė kategorija "]],
        [277, ["2550"]],
        [278, ["42407"]],
        [19, ["Bendrinė kategorija "]],
        [145, ["45498"]],
        [146, ["42440"]],
        [147, ["42015"]],
        [144, ["2771"]],
        [148, ["2771"]],
        [149, ["42479"]],
        [143, ["4218"]],
        [768, ["Bendrinė kategorija "]],
        [794, ["nėra"]],
        [792, ["2463"]],
        [793, ["41146"]],
        [235, ["41146"]],
        [268, ["Bendrinė kategorija "]],
        [396, ["2455"]],
        [399, ["42630"]],
        [397, ["3428"]],
        [398, ["2549"]],
        [400, ["2548"]],
        [402, ["42574"]],
        [401, ["4218"]],
        [864, ["45664"]],
        [270, ["Bendrinė kategorija "]],
        [415, ["2835"]],
        [414, ["45664"]],
        [413, ["3272"]],
        [269, ["Bendrinė kategorija "]],
        [407, ["3356"]],
        [404, ["45741"]],
        [411, ["3458"]],
        [406, ["3355"]],
        [412, ["41146"]],
        [403, ["42770"]],
        [405, ["2548"]],
        [409, ["4218"]],
        [408, ["42496"]],
        [410, ["42594"]],
        [867, ["2731"]],
        [271, ["Bendrinė kategorija "]],
        [419, ["42799"]],
        [418, ["41991"]],
        [416, ["42015"]],
        [417, ["44459"]],
        [272, ["Bendrinė kategorija "]],
        [423, ["44459"]],
        [420, ["42632"]],
        [421, ["4227"]],
        [422, ["42308"]],
        [424, ["41952"]],
        [232, ["Bendrinė kategorija  "]],
        [248, ["Bendrinė kategorija "]],
        [343, ["2693"]],
        [344, ["45741"]],
        [339, ["2457"]],
        [342, ["2927"]],
        [340, ["2457"]],
        [341, ["2485, 2796"]],
        [863, ["42626"]],
        [245, ["2854"]],
        [246, ["Bendrinė kategorija "]],
        [334, ["2485"]],
        [333, ["20499"]],
        [332, ["2794"]],
        [247, ["Bendrinė kategorija "]],
        [337, ["atributai"]],
        [964, ["45746"]],
        [338, ["nėra"]],
        [336, ["45746"]],
        [335, ["2794"]],
        [5, ["Bendrinė kategorija "]],
        [35, ["nėra"]],
        [29, ["2557"]],
        [33, ["Bendrinė kategorija "]],
        [189, ["2881"]],
        [190, ["kažkas negerai"]],
        [188, ["3399"]],
        [32, ["Bendrinė kategorija "]],
        [187, ["42774"]],
        [186, ["20494"]],
        [30, ["Bendrinė kategorija "]],
        [180, ["3973"]],
        [181, ["45648"]],
        [179, ["2548"]],
        [182, ["41146"]],
        [36, ["Bendrinė kategorija "]],
        [195, ["42771"]],
        [198, ["2927"]],
        [197, ["2927"]],
        [194, ["2927"]],
        [196, ["2927"]],
        [34, ["Bendrinė kategorija "]],
        [193, ["42400"]],
        [192, ["42891"]],
        [191, ["42400"]],
        [31, ["Bendrinė kategorija "]],
        [185, ["42799"]],
        [184, ["42474"]],
        [183, ["42863"]],
        [440, ["Bendrinė kategorija "]],
        [448, ["40705"]],
        [465, ["4219"]],
        [456, ["42852"]],
        [449, ["2461"]],
        [452, ["2833"]],
        [453, ["15969"]],
        [451, ["3085"]],
        [450, ["2456"]],
        [455, ["3181"]],
        [462, ["Bendrinė kategorija "]],
        [600, ["41014"]],
        [592, ["3272"]],
        [696, ["2739"]],
        [697, ["42580"]],
        [698, ["2520"]],
        [699, ["3455"]],
        [700, ["3272"]],
        [701, ["3272"]],
        [702, ["2701"]],
        [703, ["3272"]],
        [704, ["3272"]],
        [593, ["Bendrinė kategorija "]],
        [705, ["2740"]],
        [706, ["41980"]],
        [707, ["41165"]],
        [708, ["3272"]],
        [709, ["3272"]],
        [710, ["42635"]],
        [711, ["2743"]],
        [712, ["42635"]],
        [713, ["41944"]],
        [714, ["42635"]],
        [715, ["2742"]],
        [716, ["2743"]],
        [717, ["3270"]],
        [718, ["2743"]],
        [719, ["3272"]],
        [720, ["3272"]],
        [594, ["Bendrinė kategorija "]],
        [724, ["3272"]],
        [725, ["3272"]],
        [721, ["2915"]],
        [722, ["2915"]],
        [723, ["2690"]],
        [726, ["2915"]],
        [595, ["Bendrinė kategorija "]],
        [727, ["2730"]],
        [728, ["5538"]],
        [598, ["Bendrinė kategorija "]],
        [754, ["atributai"]],
        [755, ["3272"]],
        [756, ["3272"]],
        [757, ["3272"]],
        [758, ["5543"]],
        [759, ["5543"]],
        [760, ["3272"]],
        [596, ["Bendrinė kategorija "]],
        [733, ["3272"]],
        [729, ["42775"]],
        [730, ["42853"]],
        [731, ["5558"]],
        [732, ["3272"]],
        [597, ["Bendrinė kategorija "]],
        [747, ["2743"]],
        [748, ["3270"]],
        [749, ["2743"]],
        [750, ["5534"]],
        [734, ["2741"]],
        [735, ["3272"]],
        [736, ["3272"]],
        [737, ["atributai"]],
        [738, ["42893"]],
        [739, ["42578"]],
        [740, ["3272"]],
        [741, ["41025"]],
        [742, ["42521, 42578"]],
        [743, ["45734, 3272"]],
        [744, ["3272"]],
        [745, ["3272"]],
        [746, ["42003"]],
        [963, ["42505"]],
        [752, ["5546"]],
        [751, ["2496"]],
        [753, ["3272"]],
        [870, ["42578"]],
        [871, ["45664"]],
        [946, ["2742"]],
        [947, ["2742"]],
        [599, ["41944"]],
        [764, ["42701"]],
        [763, ["41968"]],
        [762, ["41945"]],
        [761, ["3272"]],
        [936, ["42003"]],
        [935, ["3272"]],
        [937, ["42003"]],
        [466, ["45741"]],
        [464, ["Bendrinė kategorija "]],
        [636, ["5551"]],
        [634, ["42542"]],
        [635, ["5536, 5537"]],
        [638, ["45631"]],
        [765, ["5513, 5514, 5515, 5516"]],
        [637, ["5521"]],
        [965, ["5521"]],
        [463, ["Bendrinė kategorija "]],
        [601, ["42574"]],
        [602, ["42616"]],
        [604, ["41146"]],
        [605, ["2543"]],
        [606, ["45522"]],
        [607, ["2724, 2711  "]],
        [941, ["42799"]],
        [608, ["42561"]],
        [609, ["3272"]],
        [610, ["2543"]],
        [611, ["20498"]],
        [940, ["41991"]],
        [612, ["3272"]],
        [938, ["42015"]],
        [613, ["atributai"]],
        [614, ["3272"]],
        [615, ["4224"]],
        [616, ["41958"]],
        [603, ["42015"]],
        [617, ["42438"]],
        [618, ["42474"]],
        [939, ["44459"]],
        [619, ["42438"]],
        [620, ["20484"]],
        [631, ["42438"]],
        [621, ["atributai"]],
        [622, ["4211"]],
        [623, ["41945"]],
        [624, ["42863"]],
        [625, ["42799"]],
        [632, ["42730"]],
        [633, ["45697"]],
        [628, ["42799"]],
        [629, ["42438"]],
        [630, ["atributai"]],
        [626, ["41939"]],
        [627, ["41980"]],
        [457, ["Bendrinė kategorija "]],
        [537, ["41978"]],
        [536, ["42852"]],
        [538, ["42769"]],
        [461, ["Bendrinė kategorija "]],
        [577, ["20470"]],
        [555, ["42797"]],
        [556, ["42377, 5540"]],
        [557, ["40685"]],
        [558, ["42785"]],
        [559, ["42784"]],
        [560, ["40685"]],
        [561, ["atributai "]],
        [562, ["3013"]],
        [563, ["42700"]],
        [564, ["42700"]],
        [565, ["2855"]],
        [566, ["2855"]],
        [567, ["42606"]],
        [568, ["3013"]],
        [569, ["42699"]],
        [570, ["42391"]],
        [571, ["40685"]],
        [572, ["40685"]],
        [573, ["40685"]],
        [574, ["atributai "]],
        [575, ["40685"]],
        [576, ["42521, 42418"]],
        [578, ["45358"]],
        [579, ["41946"]],
        [580, ["42789, 2711, 2724"]],
        [581, ["3452"]],
        [582, ["40685"]],
        [583, ["40685"]],
        [584, ["40685"]],
        [585, ["42651"]],
        [586, ["42615"]],
        [587, ["40685"]],
        [588, ["42881"]],
        [589, ["3463"]],
        [590, ["40685"]],
        [591, ["42791"]],
        [459, ["Bendrinė kategorija "]],
        [547, ["42694"]],
        [546, ["3355"]],
        [458, ["Bendrinė kategorija "]],
        [539, ["42709"]],
        [543, ["42787"]],
        [540, ["2706, 2454 "]],
        [541, ["2457"]],
        [542, ["4227"]],
        [544, ["45656"]],
        [545, ["2498"]],
        [460, ["Bendrinė kategorija "]],
        [552, ["2465"]],
        [550, ["41146"]],
        [554, ["2465"]],
        [548, ["2465"]],
        [551, ["42003"]],
        [553, ["42545"]],
        [549, ["2737"]],
        [454, ["Bendrinė kategorija "]],
        [530, ["42302"]],
        [533, ["42794"]],
        [532, ["2924, 2925"]],
        [535, ["2485, 2796"]],
        [531, ["42594"]],
        [528, ["atributai"]],
        [529, ["45494"]],
        [534, ["atributai"]],
        [862, ["42903"]],
        [972, ["2520"]],
        [234, ["42791"]],
        [267, ["Bendrinė kategorija "]],
        [394, ["41146"]],
        [392, ["41945"]],
        [395, ["41146"]],
        [393, ["5557"]],
        [265, ["3272"]],
        [259, ["3272"]],
        [368, ["42791"]],
        [367, ["42701"]],
        [262, ["Bendrinė kategorija "]],
        [380, ["41946"]],
        [379, ["2838"]],
        [378, ["2485"]],
        [381, ["42947"]],
        [261, ["Bendrinė kategorija "]],
        [374, ["2546"]],
        [375, ["2600"]],
        [376, ["41975"]],
        [373, ["atributai"]],
        [377, ["atributai"]],
        [260, ["Bendrinė kategorija "]],
        [370, ["atributai"]],
        [369, ["atributai"]],
        [372, ["41982"]],
        [371, ["45751"]],
        [263, ["Bendrinė kategorija "]],
        [384, ["3054"]],
        [385, ["3267"]],
        [383, ["41146"]],
        [382, ["41146"]],
        [264, ["41974"]],
        [266, ["Bendrinė kategorija "]],
        [391, ["2512"]],
        [389, ["3452"]],
        [390, ["41146"]],
        [388, ["41969"]],
        [387, ["41968"]],
        [386, ["nėra"]],
        [861, ["3054"]],
        [872, ["20499"]],
        [878, ["Bendrinė kategorija "]],
        [879, ["atributai"]],
        [874, ["Bendrinė kategorija "]],
        [900, ["atributai"]],
        [899, ["atributai"]],
        [898, ["3258, 3260"]],
        [897, ["2832, 2831 "]],
        [896, ["42750"]],
        [895, ["42750"]],
        [873, ["Bendrinė kategorija "]],
        [901, ["42750"]],
        [889, ["42750"]],
        [924, ["atributai"]],
        [926, ["42450"]],
        [925, ["45718"]],
        [894, ["atributai"]],
        [893, ["42750"]],
        [892, ["42750"]],
        [902, ["Bendrinė kategorija "]],
        [928, ["42750"]],
        [929, ["42750"]],
        [930, ["42750"]],
        [931, ["42750"]],
        [932, ["42750"]],
        [927, ["42750"]],
        [891, ["2480, 2806"]],
        [890, ["42750"]],
        [875, ["2479, 2480"]],
        [876, ["42750"]],
        [877, ["42750"]],
        [880, ["Bendrinė kategorija "]],
        [905, ["nėra"]],
        [903, ["2795,  2809"]],
        [904, ["nėra"]],
        [906, ["42749"]],
        [907, ["42872"]],
        [881, ["Bendrinė kategorija "]],
        [913, ["41146"]],
        [909, ["atributai "]],
        [911, ["41146"]],
        [912, ["42750"]],
        [914, ["42775"]],
        [915, ["42853"]],
        [916, ["3272"]],
        [908, ["atributai "]],
        [910, ["42750"]],
        [882, ["42688"]],
        [918, ["42687"]],
        [923, ["atributai "]],
        [922, ["5558"]],
        [917, ["atirubutai "]],
        [919, ["2498"]],
        [920, ["3973"]],
        [921, ["3973"]],
        [767, ["2421"]],
        [773, ["Bendrinė kategorija "]],
        [816, ["5570"]],
        [812, ["2427"]],
        [815, ["2585,  2586, 2602, 2603"]],
        [814, ["2588"]],
        [813, ["2933, 2934"]],
        [785, ["Bendrinė kategorija "]],
        [856, ["2589"]],
        [853, ["atributai"]],
        [852, ["atributai"]],
        [857, ["atributai"]],
        [851, ["atributai"]],
        [854, ["atributai"]],
        [855, ["atributai"]],
        [780, ["2606"]],
        [777, ["Bendrinė kategorija "]],
        [837, ["2514"]],
        [838, ["45494"]],
        [774, ["Bendrinė kategorija "]],
        [817, ["atributai"]],
        [818, ["atributai"]],
        [819, ["41983, 41984"]],
        [779, ["Bendrinė kategorija "]],
        [845, ["2440"]],
        [847, ["atributai"]],
        [848, ["atributai"]],
        [849, ["2439"]],
        [846, ["2440"]],
        [850, ["atributai"]],
        [775, ["Bendrinė kategorija "]],
        [824, ["atributai"]],
        [823, ["atributai"]],
        [820, ["2425"]],
        [822, ["45759"]],
        [821, ["atributai"]],
        [825, ["atributai"]],
        [826, ["atributai"]],
        [827, ["atributai"]],
        [884, ["atributai"]],
        [885, ["atributai"]],
        [886, ["atributai"]],
        [887, ["atributai"]],
        [770, ["Bendrinė kategorija "]],
        [798, ["2421"]],
        [801, ["45643"]],
        [800, ["2489"]],
        [799, ["atributai"]],
        [778, ["Bendrinė kategorija "]],
        [843, [" 45347, 45348"]],
        [839, ["atributai"]],
        [840, ["atributai"]],
        [841, ["42764, 42765"]],
        [842, [" 45347, 45348"]],
        [844, ["45494"]],
        [771, ["Bendrinė kategorija "]],
        [802, ["atributai"]],
        [804, ["2606"]],
        [803, ["42401"]],
        [805, ["5559"]],
        [806, ["atributai"]],
        [962, ["2445,  2587"]],
        [772, ["Bendrinė kategorija "]],
        [809, ["2546"]],
        [808, ["nėra"]],
        [967, ["2581"]],
        [807, ["2470"]],
        [811, ["2544"]],
        [810, ["41977"]],
        [970, ["2574, 2575"]],
        [776, ["Bendrinė kategorija "]],
        [831, ["45485, 45486"]],
        [835, ["3461"]],
        [836, ["42005"]],
        [834, ["42433"]],
        [828, ["2438"]],
        [829, ["2933, 2934"]],
        [830, ["42569"]],
        [833, ["45735"]],
        [832, ["42652"]],
        [781, ["3973"]],
        [973, ["3055"]],
        [783, ["atributai"]],
        [782, ["atributai"]],
        [784, ["2910"]],
        [859, ["41976"]],
        [954, ["2497"]],
        [966, ["2580"]],
        [975, ["Nėra"]],
        [955, ["Bendrinė kategorija "]],
        [980, ["15968"]],
        [979, ["42449"]],
        [956, ["atributai"]],
        [957, ["atributai"]],
        [958, ["atributai"]],
        [959, ["atributai"]],
        [439, ["Bendrinė kategorija "]],
        [446, ["Nėra"]],
        [520, ["2724, 2711  "]],
        [521, ["41989"]],
        [523, ["41990"]],
        [522, ["2743"]],
        [524, ["2743"]],
        [447, ["Bendrinė kategorija "]],
        [527, ["41980"]],
        [961, ["42437"]],
        [525, ["41939"]],
        [526, ["41980"]],
        [443, ["Bendrinė kategorija "]],
        [493, ["atributai"]],
        [495, ["atributai"]],
        [494, ["atributai"]],
        [444, ["Bendrinė kategorija "]],
        [502, ["42663"]],
        [497, ["atributai"]],
        [501, ["41946"]],
        [504, ["2740"]],
        [496, ["2856"]],
        [500, ["atributai"]],
        [498, ["2837"]],
        [499, ["atributai"]],
        [505, ["2740"]],
        [507, ["41944"]],
        [506, ["41947"]],
        [503, ["41948"]],
        [508, ["41146"]],
        [509, ["41146"]],
        [445, ["Bendrinė kategorija "]],
        [510, ["atributai"]],
        [511, ["41165"]],
        [514, ["3272"]],
        [516, ["3272"]],
        [512, ["42635"]],
        [513, ["2743"]],
        [519, ["42635"]],
        [515, ["42635"]],
        [517, ["2913"]],
        [518, ["2914"]],
        [2, ["Bendrinė kategorija "]],
        [17, ["nera"]],
        [978, ["2735"]],
        [134, ["3272"]],
        [212, ["45522"]],
        [215, ["42438"]],
        [216, ["45732"]],
        [213, ["42730"]],
        [214, ["nera"]],
        [133, ["5521"]],
        [950, ["5551"]],
        [948, ["42542"]],
        [949, ["5536, 5537"]],
        [952, ["45631"]],
        [953, ["5513, 5514, 5515, 5516"]],
        [951, ["5521"]],
        [8, ["Bendrinė kategorija "]],
        [44, ["nera"]],
        [41, ["42551"]],
        [43, ["nėra"]],
        [42, ["42495"]],
        [40, ["40731"]],
        [45, ["40966, 2469"]],
        [12, ["2573"]],
        [16, ["Bendrinė kategorija "]],
        [132, ["nėra"]],
        [130, ["2468"]],
        [129, ["41943"]],
        [131, ["2468"]],
        [15, ["Bendrinė kategorija "]],
        [125, ["41146"]],
        [117, ["42734"]],
        [116, ["42776"]],
        [122, ["40968, 40969"]],
        [123, ["2468"]],
        [118, ["2468"]],
        [124, ["40975"]],
        [119, ["2468"]],
        [121, ["42554"]],
        [126, ["2468"]],
        [127, ["41146"]],
        [120, ["2468"]],
        [128, ["3399"]],
        [14, ["Bendrinė kategorija "]],
        [108, ["nėra"]],
        [111, ["nėra"]],
        [112, ["nėra"]],
        [110, ["nėra"]],
        [113, ["nėra"]],
        [105, ["nėra"]],
        [114, ["nėra"]],
        [115, ["nėra"]],
        [107, ["nėra"]],
        [106, ["nėra"]],
        [109, ["nėra"]],
        [13, ["Bendrinė kategorija "]],
        [104, ["10928"]],
        [103, ["10928"]],
        [102, ["10928"]],
        [101, ["10928"]],
        [9, ["Bendrinė kategorija "]],
        [53, ["41146"]],
        [50, ["nėra"]],
        [47, ["42607"]],
        [59, ["20494"]],
        [46, ["42870"]],
        [48, ["40726"]],
        [55, ["3215"]],
        [49, ["40972"]],
        [57, ["45522"]],
        [56, ["45499"]],
        [60, ["10928"]],
        [52, ["40975"]],
        [54, ["3430"]],
        [51, ["40974"]],
        [58, ["42730"]],
        [7, ["41938"]],
        [11, ["Bendrinė kategorija "]],
        [90, ["45721"]],
        [94, ["45721"]],
        [98, ["42550, 3093"]],
        [86, ["42438"]],
        [89, ["42594"]],
        [92, ["45721"]],
        [85, ["45721"]],
        [96, ["45721"]],
        [91, ["45721"]],
        [93, ["45721"]],
        [84, ["45721"]],
        [95, ["45721"]],
        [81, ["2707"]],
        [82, ["5561"]],
        [87, ["40857, 40860"]],
        [97, ["45721"]],
        [88, ["2468"]],
        [99, ["42494"]],
        [83, ["42608"]],
        [100, ["40857, 40860"]],
        [786, ["40858, 40861"]],
        [787, ["42438"]],
        [788, ["nėra"]],
        [789, ["42500"]],
        [790, ["nėra"]],
        [791, ["42662"]],
        [10, ["Bendrinė kategorija "]],
        [73, ["42778"]],
        [61, ["2708"]],
        [71, ["10928"]],
        [79, ["2703"]],
        [62, ["3420"]],
        [78, ["45522"]],
        [76, ["42400"]],
        [64, ["10928"]],
        [69, ["3422"]],
        [80, ["10928"]],
        [68, ["2468"]],
        [65, ["10928"]],
        [63, ["10928"]],
        [72, ["3413, 3414"]],
        [276, ["10928"]],
        [74, ["42821"]],
        [70, ["10928"]],
        [66, ["10928"]],
        [67, ["10928"]],
        [75, ["10928"]],
        [77, ["40971"]],
        [236, ["Bendrinė kategorija "]],
        [275, ["Bendrinė kategorija "]],
        [434, ["atributai"]],
        [435, ["3393,  4216 , 45706"]],
        [436, ["nėra"]],
        [433, ["42302"]],
        [437, ["41146"]],
        [274, ["41146"]],
        [432, ["41928"]],
        [431, ["42004"]],
        [430, ["42787"]],
        [429, ["45680, 2529, 2530"]],
        [273, ["45655"]],
        [425, ["2734"]],
        [427, ["41928"]],
        [426, ["41940"]],
        [428, ["45656"]],
        [442, ["Bendrinė kategorija "]],
        [492, ["Bendrinė kategorija "]],
        [695, ["3179"]],
        [693, ["3179"]],
        [692, ["3179"]],
        [694, ["3179"]],
        [691, ["3179"]],
        [489, ["atributai"]],
        [480, ["Bendrinė kategorija "]],
        [657, ["45734"]],
        [654, ["2848"]],
        [655, ["2847"]],
        [656, ["2849"]],
        [866, ["45734"]],
        [883, ["45734"]],
        [960, ["2482"]],
        [483, ["3459"]],
        [484, ["3459"]],
        [485, ["3462"]],
        [486, ["2513"]],
        [487, ["atributai"]],
        [488, ["nėra"]],
        [769, ["atributai"]],
        [796, ["atributai"]],
        [797, ["42463"]],
        [795, ["atributai"]],
        [869, ["atributai"]],
        [490, ["Bendrinė kategorija "]],
        [685, ["3451"]],
        [690, ["42540"]],
        [687, ["45672"]],
        [688, ["3254"]],
        [679, ["5568"]],
        [682, ["42463"]],
        [674, ["3462"]],
        [675, ["Nėra"]],
        [680, ["2838, 42786"]],
        [678, ["2838"]],
        [681, ["42370, 42470"]],
        [683, ["2859"]],
        [684, ["2860"]],
        [677, ["3462"]],
        [676, ["2481"]],
        [686, ["3462"]],
        [766, ["3462"]],
        [689, ["42392"]],
        [868, ["42370"]],
        [888, ["42370"]],
        [943, ["5526"]],
        [942, ["3450"]],
        [491, ["45651"]],
        [482, ["Bendrinė kategorija "]],
        [673, ["atributai"]],
        [669, ["atributai"]],
        [670, ["atributai"]],
        [671, ["atributai"]],
        [672, ["atributai"]],
        [481, ["Bendrinė kategorija "]],
        [658, ["atributai"]],
        [659, ["3460"]],
        [660, ["42549"]],
        [661, ["atributai"]],
        [662, ["5555"]],
        [663, ["42433"]],
        [664, ["4228"]],
        [665, ["45671"]],
        [666, ["nėra"]],
        [667, ["2484"]],
        [668, ["atributai"]],
        [865, ["2484"]],
        [858, ["2722"]],
        [860, ["3973"]],
        [441, ["5547, 5548, 3224 "]],
        [468, ["3229"]],
        [479, ["42370"]],
        [471, ["3224"]],
        [642, ["atributai"]],
        [645, ["41005"]],
        [644, ["4220, 4221"]],
        [643, ["atributai"]],
        [473, ["2834"]],
        [472, ["2834"]],
        [469, ["3395"]],
        [470, ["3395"]],
        [476, ["42598"]],
        [475, ["45633"]],
        [474, ["2916"]],
        [467, ["Bendrinė kategorija "]],
        [639, ["2512"]],
        [641, ["nėra"]],
        [640, ["3395"]],
        [477, ["3270"]],
        [646, ["2834"]],
        [649, ["2836,  41025"]],
        [647, ["2834"]],
        [650, ["41025"]],
        [648, ["2839,  3270"]],
        [478, ["Bendrinė kategorija "]],
        [651, ["2855"]],
        [652, ["42881"]],
        [653, ["2839"]],
        [933, ["5556"]],
        [934, ["nėra"]],
    ]
    for partan_cat_id, recar_cat_ids in data:
        try:
            _recar_category_id = int(recar_cat_ids[0].split(",")[0])
        except ValueError:
            _recar_category_id = None
        else:
            try:
                Category.objects.get(id=partan_cat_id).recar_categories.add(
                    _recar_category_id
                )
            except Category.DoesNotExist:
                pass


class Migration(migrations.Migration):
    dependencies = [
        ("catalogue", "0018_auto_20231215_1006"),
    ]

    operations = [migrations.RunPython(map_partan_categories_with_recar)]
