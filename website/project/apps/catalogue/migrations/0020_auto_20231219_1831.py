# Generated by Django 3.2.18 on 2023-12-19 16:31

from django.db import migrations


def map_partan_attributes_with_recar_categories(apps, schema_editor):
    ProductExtraAttributeCategory = apps.get_model(
        "catalogue", "ProductExtraAttributeCategory"
    )

    data = [
        [670, 14747, "5581, 5582"],
        [671, 14747, "5582, 5581"],
        [672, 14747, "5582, 5581"],
        [669, 14747, "5581, 5582"],
        [663, 14747, "42433"],
        [679, 14747, "5568"],
        [102, 14747, "10928"],
        [879, 31262, "2825"],
        [818, 31262, "2442"],
        [923, 31262, "3447"],
        [850, 31262, "42841"],
        [909, 31262, "2808"],
        [799, 31262, "2503"],
        [917, 31262, "42689"],
        [908, 31262, "45439"],
        [821, 31262, "5531"],
        [754, 31262, "5520"],
        [613, 31262, "3447"],
        [621, 31262, "2824"],
        [561, 31262, "2664"],
        [574, 31262, "42425"],
        [924, 31262, "2830"],
        [909, 31262, "2808"],
        [642, 31262, "4223"],
        [737, 31262, "42844"],
        [390, 31262, "41146"],
        [781, 31262, "3973"],
        [528, 31262, "2665"],
        [534, 31262, "41068"],
        [806, 31262, "2571"],
        [820, 31262, "2619"],
        [823, 31262, "2656"],
        [824, 31262, "3398"],
        [826, 31262, "42539, 2657"],
        [827, 31262, "41068"],
        [327, 31262, "2818"],
        [847, 31262, "2655"],
        [848, 31262, "2679"],
        [351, 31262, "2824"],
        [355, 31262, "2816"],
        [487, 31262, "3439"],
        [493, 31262, "2845"],
        [369, 31262, "3398"],
        [370, 31262, "2656"],
        [884, 31262, "5531"],
        [373, 31262, "2654"],
        [377, 31262, "42292"],
        [253, 31262, "42556, 42945"],
        [338, 64301, "nėra"],
        [337, 64301, "2797"],
        [499, 64301, "2852"],
        [500, 64301, "2506"],
        [658, 64301, "5574"],
        [661, 64301, "5555"],
        [668, 64301, "45735"],
        [102, 64301, "10928"],
        [820, 64301, "2619"],
        [839, 64301, "2628"],
        [842, 64301, "45347"],
        [840, 64301, "2634"],
        [884, 64301, "5528, 5530"],
        [885, 64301, "5579"],
        [886, 64301, " 41964 , 41966"],
        [888, 64301, "42370"],
        [887, 64301, "41966, 41964"],
        [370, 64301, "2626, 2668"],
        [823, 64301, "2626, 2668"],
        [825, 64301, "42370"],
        [795, 64301, "2636, 2676"],
        [673, 64301, "42412, 42396"],
        [782, 64301, "42396,  42412"],
        [854, 64301, "2532"],
        [855, 64301, "2608"],
        [851, 64301, "2431"],
        [857, 64301, "45713"],
        [852, 64301, "2521"],
        [856, 64301, "2590"],
        [853, 64301, "2436"],
        [630, 64301, " 3175, 3173"],
        [643, 64301, "3429, 42468"],
        [338, 64302, "nėra"],
        [337, 64302, "2798"],
        [499, 64302, "2850"],
        [500, 64302, "2507"],
        [658, 64302, "5575"],
        [661, 64302, "5555"],
        [668, 64302, "45735"],
        [102, 64302, "10928"],
        [820, 64302, "2619"],
        [839, 64302, "2643"],
        [842, 64302, "45348"],
        [840, 64302, "2652"],
        [884, 64302, "5529 ,5531"],
        [885, 64302, "5580"],
        [886, 64302, "41965, 41967"],
        [888, 64302, "42370"],
        [887, 64302, "41965, 41967"],
        [370, 64302, "2656, 2641"],
        [823, 64302, "2656, 2641"],
        [825, 64302, "42370"],
        [795, 64302, "2662, 2649"],
        [673, 64302, "42396, 42395"],
        [782, 64302, "42464, 42465"],
        [854, 64302, "2533"],
        [855, 64302, "2608"],
        [851, 64302, "2432"],
        [857, 64302, "3973"],
        [852, 64302, "2522"],
        [856, 64302, "2591"],
        [853, 64302, "2437"],
        [630, 64302, " 3174, 3176"],
        [643, 64302, "3429, 42469"],
        [879, 81103, "2828"],
        [818, 81103, "2441"],
        [923, 81103, "3446"],
        [850, 81103, "42840"],
        [909, 81103, "2799"],
        [799, 81103, "2502"],
        [917, 81103, "42690"],
        [908, 81103, "45439"],
        [821, 81103, "5530"],
        [754, 81103, "5519"],
        [613, 81103, "3446"],
        [621, 81103, "2827"],
        [561, 81103, "2667"],
        [574, 81103, "42648"],
        [924, 81103, "2829"],
        [909, 81103, "2799"],
        [642, 81103, "4222"],
        [737, 81103, "42844"],
        [390, 81103, "41146"],
        [781, 81103, "3973"],
        [528, 81103, "2672"],
        [534, 81103, "41067"],
        [806, 81103, "2572"],
        [820, 81103, "2616"],
        [823, 81103, "2668"],
        [824, 81103, "3397"],
        [826, 81103, "42538, 2669"],
        [827, 81103, "41067"],
        [327, 81103, "2801"],
        [847, 81103, "2666"],
        [848, 81103, "2679"],
        [351, 81103, "2827"],
        [355, 81103, "2803"],
        [487, 81103, "3439"],
        [493, 81103, "2844"],
        [369, 81103, "3397"],
        [370, 81103, "2668"],
        [884, 81103, "5530"],
        [373, 81103, "2654"],
        [377, 81103, "42292"],
        [253, 81103, "42556, 42945"],
        [879, 97887, "2792"],
        [818, 97887, "2443"],
        [923, 97887, "3444"],
        [850, 97887, "42842"],
        [909, 97887, "2781"],
        [799, 97887, "2500"],
        [917, 97887, "42692"],
        [908, 97887, "42949"],
        [821, 97887, "5528"],
        [754, 97887, "5517"],
        [613, 97887, "3444"],
        [621, 97887, "2791"],
        [561, 97887, "2638"],
        [574, 97887, "42648"],
        [924, 97887, "45343"],
        [909, 97887, "2781"],
        [642, 97887, "4220"],
        [737, 97887, "5523"],
        [390, 97887, "41146"],
        [781, 97887, "3973"],
        [528, 97887, "2633"],
        [534, 97887, "41065"],
        [806, 97887, "2569"],
        [820, 97887, "2616"],
        [823, 97887, "2626"],
        [824, 97887, "3251"],
        [826, 97887, "2627"],
        [827, 97887, "41065"],
        [327, 97887, "2785"],
        [847, 97887, "2625"],
        [848, 97887, "2683"],
        [351, 97887, "2791"],
        [355, 97887, "2784"],
        [487, 97887, "3437"],
        [493, 97887, "2842"],
        [494, 97887, "2853"],
        [495, 97887, "2483"],
        [369, 97887, "3251"],
        [370, 97887, "2626"],
        [884, 97887, "5528"],
        [373, 97887, "2624"],
        [377, 97887, "42292"],
        [253, 97887, "42556, 42945"],
        [879, 97888, "2793"],
        [818, 97888, "2444"],
        [850, 97888, "42843"],
        [909, 97888, "2775"],
        [799, 97888, "2501"],
        [917, 97888, "42691"],
        [908, 97888, "42949"],
        [821, 97888, "5529"],
        [754, 97888, "5518"],
        [613, 97888, "3445"],
        [621, 97888, "2790"],
        [923, 97888, "3445"],
        [561, 97888, "2651"],
        [574, 97888, "42425"],
        [924, 97888, "45344"],
        [909, 97888, "2775"],
        [642, 97888, "4221"],
        [737, 97888, "5522"],
        [390, 97888, "41146"],
        [781, 97888, "3973"],
        [528, 97888, "2650"],
        [534, 97888, "41066"],
        [806, 97888, "2570"],
        [820, 97888, "2617"],
        [823, 97888, "2641"],
        [824, 97888, "3252"],
        [826, 97888, "2642"],
        [827, 97888, "41066"],
        [327, 97888, "2776"],
        [847, 97888, "2639"],
        [848, 97888, "2683"],
        [351, 97888, "2790"],
        [355, 97888, "2778"],
        [487, 97888, "3438"],
        [493, 97888, "2843"],
        [494, 97888, "2851"],
        [495, 97888, "2483"],
        [369, 97888, "3252"],
        [370, 97888, "2641"],
        [884, 97888, "5529"],
        [373, 97888, "2640"],
        [377, 97888, "42292"],
        [253, 97888, "42556, 42945"],
        [670, 114189, "5578, 5577"],
        [671, 114189, "5582, 5581"],
        [672, 114189, "5582, 5581"],
        [669, 114189, "5578, 5577"],
        [663, 114189, "5555"],
        [679, 114189, "5568"],
        [102, 114189, "10928"],
        [356, 130747, "42705"],
        [357, 130747, "2802 , 2804"],
        [324, 130747, "2878"],
        [494, 130747, "3441, 3442, 3443"],
        [483, 130747, "3459"],
        [102, 130747, "10928"],
        [802, 130747, "2435"],
        [884, 130747, "5530, 5531"],
        [886, 130747, "41966, 41967"],
        [370, 130747, "2656, 2668"],
        [823, 130747, "2656, 2668"],
        [825, 130747, "42370"],
        [795, 130747, "2676, 2662"],
        [673, 130747, "42396, 42397"],
        [782, 130747, "42464, 42467"],
        [530, 130747, "3394"],
        [433, 130747, "3394"],
        [510, 130747, "2607"],
        [630, 130747, "3175, 3176"],
        [356, 130748, "42704"],
        [357, 130748, "2777, 2783"],
        [324, 130748, "2878"],
        [483, 130748, "3459"],
        [102, 130748, "10928"],
        [630, 130748, "3173, 3174"],
        [802, 130748, "2426"],
        [884, 130748, "5529, 5528"],
        [886, 130748, "41964, 41965"],
        [370, 130748, "2626, 2641"],
        [823, 130748, "2626, 2641"],
        [825, 130748, "42370"],
        [795, 130748, "2636, 2649"],
        [673, 130748, "42412, 42395"],
        [782, 130748, "42465, 42466"],
        [530, 130748, "2768"],
        [433, 130748, "2768"],
        [510, 130748, "2913"],
        [680, 990241, "2838,"],
        [479, 990241, "42370"],
        [677, 990241, "42370"],
        [680, 990242, "42786"],
        [332, 990289, "2794"],
        [839, 990289, " 2628, 2643"],
        [332, 990290, "2794"],
        [839, 990290, " 2628, 2643"],
        [332, 990291, "2794"],
        [377, 990292, "42652"],
        [434, 990293, "2738"],
        [434, 990294, "42618"],
        [680, 990295, "2838,"],
        [489, 990297, "2873, 2874"],
        [489, 990298, "2873, 2874"],
        [672, 990299, "nėra"],
        [673, 990299, "3229"],
        [955, 990299, "45765, 3220"],
        [957, 990299, "45765"],
        [959, 990299, "2491"],
        [487, 990299, "45491"],
        [489, 990299, "45356"],
        [782, 990299, "42464"],
        [783, 990299, "42464"],
        [958, 990299, "42601"],
        [471, 990299, "3224"],
        [607, 990299, "2595, 2596"],
        [956, 990299, "2499"],
        [669, 990299, "5576"],
        [670, 990299, "5576"],
        [671, 990299, "5576"],
        [672, 990300, "5582"],
        [669, 990300, "5578, 5582"],
        [866, 990300, "45734"],
        [899, 990300, "2786"],
        [900, 990300, "2780"],
        [869, 990300, "42918"],
        [433, 990300, "2768"],
        [489, 990300, "2874"],
        [783, 990300, "42459"],
        [497, 990300, "2840"],
        [530, 990300, "2768"],
        [883, 990300, "45734"],
        [894, 990300, "2786"],
        [479, 990300, "42370"],
        [671, 990300, "5582"],
        [892, 990300, "42750"],
        [817, 990300, "2430, 42658"],
        [670, 990300, "5578, 5582"],
        [677, 990300, "42370"],
        [672, 990301, "5581"],
        [669, 990301, "5577, 5581"],
        [866, 990301, "45734"],
        [899, 990301, "2786"],
        [900, 990301, "2787"],
        [869, 990301, "42917"],
        [433, 990301, "2768"],
        [489, 990301, "2873"],
        [783, 990301, "42460"],
        [497, 990301, "2840"],
        [530, 990301, "2768"],
        [883, 990301, "45734"],
        [894, 990301, "2786"],
        [479, 990301, "42370"],
        [671, 990301, "5581"],
        [892, 990301, "42750"],
        [817, 990301, "2428, 42421"],
        [670, 990301, "5578, 5582"],
        [677, 990301, "42370"],
        [479, 990341, "42370"],
        [677, 990341, "42370"],
        [479, 990342, "42370"],
        [677, 990342, "42370"],
        [479, 990343, "42370"],
        [677, 990343, "42370"],
        [479, 990345, "42370"],
        [677, 990345, "42370"],
        [434, 990346, "2738, 42618"],
        [452, 990387, "2833"],
        [452, 990388, "2833"],
        [310, 990389, "2858"],
        [309, 990389, "2926"],
        [452, 990389, "2833"],
        [310, 990390, "2858"],
        [309, 990390, "2926"],
        [452, 990390, "2833"],
        [839, 990394, "2635,  2653"],
        [839, 990395, " 2628, 2643"],
        [839, 990396, " 2628, 2643"],
        [839, 990397, " 2628, 2643"],
        [839, 990398, " 2628, 2643"],
        [839, 990399, " 2628, 2643"],
        [839, 990400, " 2628, 2643"],
        [280, 990401, "2453"],
        [286, 990401, "2453"],
        [280, 990402, "2453"],
        [286, 990402, "2453"],
        [280, 990403, "2453"],
        [286, 990403, "2453"],
        [956, 990404, "2867"],
        [957, 990404, "2867"],
        [956, 990405, "2867"],
        [957, 990405, "2867"],
        [956, 990406, "2867"],
        [957, 990406, "2867"],
        [956, 990407, "2867"],
        [957, 990407, "2867"],
        [956, 990408, "2867"],
        [957, 990408, "2867"],
        [956, 990409, "2867"],
        [957, 990409, "2867"],
        [956, 990410, "2867"],
        [957, 990410, "2867"],
        [959, 990410, "2491"],
        [956, 990411, "2867"],
        [957, 990411, "2867"],
        [956, 990412, "2867"],
        [957, 990412, "2867"],
        [959, 990412, "2491"],
        [956, 990413, "2867"],
        [957, 990413, "2867"],
        [959, 990413, "2491"],
        [956, 990414, "2867"],
        [957, 990414, "2867"],
        [956, 990415, "2867"],
        [957, 990415, "2867"],
        [959, 990415, "2491"],
        [956, 990416, "2867"],
        [957, 990416, "2867"],
        [956, 990417, "2867"],
        [957, 990417, "2867"],
        [958, 990417, "3220"],
        [959, 990417, "2491"],
        [956, 990418, "2867"],
        [957, 990418, "2867"],
        [958, 990418, "3220"],
        [959, 990418, "2491"],
        [956, 990419, "2867"],
        [957, 990419, "2867"],
        [958, 990419, "3220"],
        [959, 990419, "2491"],
        [956, 990420, "2867"],
        [957, 990420, "2867"],
        [958, 990420, "3220"],
        [959, 990420, "2491"],
        [956, 990421, "2867"],
        [957, 990421, "2867"],
        [958, 990421, "3220"],
        [959, 990421, "2491"],
        [956, 990422, "2867"],
        [957, 990422, "2867"],
        [958, 990422, "3220"],
        [959, 990422, "2491"],
        [956, 990423, "2867"],
        [957, 990423, "2867"],
        [958, 990423, "3220"],
        [959, 990423, "2491"],
        [956, 990424, "2867"],
        [957, 990424, "2867"],
        [958, 990424, "3220"],
        [959, 990424, "2491"],
        [956, 990425, "2867"],
        [957, 990425, "2867"],
        [958, 990425, "3220"],
        [959, 990425, "2491"],
        [956, 990426, "2867"],
        [957, 990426, "2867"],
        [958, 990426, "3220"],
        [959, 990426, "2491"],
        [956, 990427, "2867"],
        [957, 990427, "2867"],
        [958, 990427, "3220"],
        [959, 990427, "2491"],
        [956, 990428, "2867"],
        [957, 990428, "2867"],
        [958, 990428, "3220"],
        [959, 990428, "2491"],
        [956, 990429, "2867"],
        [957, 990429, "2867"],
        [956, 990430, "2867"],
        [957, 990430, "2867"],
        [956, 990431, "2867"],
        [957, 990431, "2867"],
        [956, 990432, "2867"],
        [957, 990432, "2867"],
        [956, 990433, "2867"],
        [957, 990433, "2867"],
        [956, 990434, "2867"],
        [957, 990434, "2867"],
        [956, 990435, "2867"],
        [957, 990435, "2867"],
        [956, 990436, "2867"],
        [957, 990436, "2867"],
        [956, 990437, "2867"],
        [957, 990437, "2867"],
        [956, 990438, "2867"],
        [957, 990438, "2867"],
        [956, 990439, "2867"],
        [957, 990439, "2867"],
        [956, 990440, "2867"],
        [957, 990440, "2867"],
        [956, 990441, "2867"],
        [957, 990441, "2867"],
        [956, 990442, "2867"],
        [957, 990442, "2867"],
        [956, 990443, "2867"],
        [957, 990443, "2867"],
        [956, 990444, "2867"],
        [957, 990444, "2867"],
        [956, 990445, "2867"],
        [957, 990445, "2867"],
        [956, 990446, "2867"],
        [957, 990446, "2867"],
        [956, 990447, "2867"],
        [957, 990447, "2867"],
        [956, 990448, "2867"],
        [957, 990448, "2867"],
        [956, 990449, "2867"],
        [957, 990449, "2867"],
        [956, 990450, "2867"],
        [957, 990450, "2867"],
        [956, 990451, "2867"],
        [957, 990451, "2867"],
        [956, 990452, "2867"],
        [957, 990452, "2867"],
        [956, 990453, "2867"],
        [957, 990453, "2867"],
        [956, 990454, "2867"],
        [957, 990454, "2867"],
        [956, 990455, "2867"],
        [957, 990455, "2867"],
        [956, 990456, "2867"],
        [957, 990456, "2867"],
        [956, 990457, "2867"],
        [957, 990457, "2867"],
        [956, 990458, "2867"],
        [957, 990458, "2867"],
        [956, 990459, "2867"],
        [957, 990459, "2867"],
        [956, 990460, "2867"],
        [957, 990460, "2867"],
        [956, 990461, "2867"],
        [957, 990461, "2867"],
        [956, 990462, "2867"],
        [957, 990462, "2867"],
        [956, 990463, "2867"],
        [957, 990463, "2867"],
        [956, 990464, "2867"],
        [957, 990464, "2867"],
        [956, 990465, "2867"],
        [957, 990465, "2867"],
        [956, 990466, "2867"],
        [957, 990466, "2867"],
        [956, 990467, "2867"],
        [957, 990467, "2867"],
        [956, 990468, "2867"],
        [957, 990468, "2867"],
        [956, 990469, "2867"],
        [957, 990469, "2867"],
        [956, 990470, "2867"],
        [957, 990470, "2867"],
        [956, 990471, "2867"],
        [957, 990471, "2867"],
        [956, 990472, "2867"],
        [957, 990472, "2867"],
        [288, 990473, "2858"],
        [281, 990473, "2915"],
        [956, 990474, "2867"],
        [957, 990474, "2867"],
        [956, 990475, "2867"],
        [957, 990475, "2867"],
        [956, 990476, "2867"],
        [957, 990476, "2867"],
        [956, 990477, "2867"],
        [957, 990477, "2867"],
        [956, 990478, "2867"],
        [957, 990478, "2867"],
        [956, 990479, "2867"],
        [957, 990479, "2867"],
        [956, 990480, "2867"],
        [957, 990480, "2867"],
        [956, 990481, "2867"],
        [957, 990481, "2867"],
        [956, 990482, "2867"],
        [957, 990482, "2867"],
        [956, 990483, "2867"],
        [957, 990483, "2867"],
        [956, 990484, "2867"],
        [957, 990484, "2867"],
        [179, 990485, "2548"],
        [179, 990486, "2557"],
        [179, 990487, "2548"],
        [956, 990488, "2867"],
        [957, 990488, "2867"],
        [956, 990489, "2867"],
        [957, 990489, "2867"],
        [956, 990490, "2867"],
        [957, 990490, "2867"],
        [956, 990491, "2867"],
        [957, 990491, "2867"],
        [956, 990492, "2867"],
        [957, 990492, "2867"],
        [956, 990493, "2867"],
        [957, 990493, "2867"],
        [956, 990494, "2867"],
        [957, 990494, "2867"],
        [956, 990495, "2867"],
        [957, 990495, "2867"],
        [956, 990496, "2867"],
        [957, 990496, "2867"],
        [956, 990497, "2867"],
        [957, 990497, "2867"],
        [956, 990498, "2867"],
        [957, 990498, "2867"],
        [956, 990499, "2867"],
        [957, 990499, "2867"],
        [956, 990500, "2867"],
        [957, 990500, "2867"],
        [245, 990501, "2856"],
        [851, 990502, "2431, 2432"],
        [853, 990502, "2437, 2436"],
        [845, 990503, "2543"],
        [956, 990504, "2867"],
        [957, 990504, "2867"],
        [958, 990505, "3220"],
        [958, 990506, "3220"],
        [958, 990507, "3220"],
        [958, 990508, "3220"],
        [958, 990509, "3220"],
        [958, 990510, "3220"],
        [958, 990511, "3220"],
        [958, 990512, "3220"],
        [958, 990513, "3220"],
        [958, 990514, "3220"],
        [958, 990515, "3220"],
        [958, 990516, "3220"],
        [958, 990517, "3220"],
        [958, 990518, "3220"],
        [958, 990519, "3220"],
        [958, 990520, "3220"],
        [958, 990521, "3220"],
        [958, 990522, "3220"],
        [958, 990523, "3220"],
        [958, 990524, "3220"],
        [958, 990525, "3220"],
        [958, 990526, "3220"],
        [958, 990527, "3220"],
        [955, 990528, "3220"],
        [958, 990529, "3220"],
        [958, 990530, "3220"],
        [958, 990531, "3220"],
        [958, 990532, "3220"],
        [958, 990533, "3220"],
        [958, 990534, "3220"],
        [958, 990535, "3220"],
        [958, 990536, "3220"],
        [958, 990537, "3220"],
        [958, 990538, "3220"],
        [958, 990539, "3220"],
        [956, 990540, "2867"],
        [957, 990540, "2867"],
        [959, 990540, "2491"],
        [956, 990541, "2867"],
        [957, 990541, "2867"],
        [959, 990541, "2491"],
        [960, 990542, "42406"],
        [960, 990543, "42406"],
        [953, 990546, "5513, 5514, 5515, 5516"],
    ]

    for partan_cat_id, partan_att_id, recar_cat_ids in data:
        try:
            _recar_category_id = int(recar_cat_ids.split(",")[0])
        except ValueError:
            _recar_category_id = None
        else:
            ProductExtraAttributeCategory.objects.filter(
                category_id=partan_cat_id, product_extra_attribute_id=partan_att_id
            ).update(recar_category_id=_recar_category_id)


class Migration(migrations.Migration):
    dependencies = [
        ("catalogue", "0019_auto_20231219_1807"),
    ]

    operations = [migrations.RunPython(map_partan_attributes_with_recar_categories)]
