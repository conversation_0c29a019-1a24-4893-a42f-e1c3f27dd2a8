# -*- coding: utf-8 -*-


from django.db import models, migrations
import oscar.core.validators
import django.db.models.deletion
from django.conf import settings
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('shipping', '0001_initial'),
        ('tecpap', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('rrr', '0001_initial'),
        ('catalogue', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDescriptionTemplate',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('code', models.SlugField(unique=True, max_length=128, verbose_name='Code')),
                ('name', models.CharField(unique=True, max_length=128, verbose_name='Name')),
                ('description', models.TextField(verbose_name='Description')),
                ('description_lt', models.TextField(null=True, verbose_name='Description')),
                ('description_en', models.TextField(null=True, verbose_name='Description')),
                ('description_de', models.TextField(null=True, verbose_name='Description')),
                ('description_ru', models.TextField(null=True, verbose_name='Description')),
                ('description_pl', models.TextField(null=True, verbose_name='Description')),
                ('description_es', models.TextField(null=True, verbose_name='Description')),
                ('description_fr', models.TextField(null=True, verbose_name='Description')),
                (
                    'owner',
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        to=settings.AUTH_USER_MODEL,
                        null=True,
                        verbose_name='Owner',
                        on_delete=models.PROTECT,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Product description template',
                'verbose_name_plural': 'Product description templates',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ProductExtraAttribute',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('name', models.CharField(unique=True, max_length=128, verbose_name='Name')),
                ('name_lt', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_en', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_de', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_ru', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_pl', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_es', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
                ('name_fr', models.CharField(max_length=128, unique=True, null=True, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Product extra attribute',
                'verbose_name_plural': 'Product extra attributes',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='ProductExtraAttributeCategory',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('category', models.ForeignKey(to='catalogue.Category', on_delete=models.CASCADE)),
                (
                    'product_extra_attribute',
                    models.ForeignKey(
                        related_name='category_map', to='catalogue.ProductExtraAttribute', on_delete=models.CASCADE
                    ),
                ),
                (
                    'rrr_category',
                    models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, to='rrr.RrrCategory', null=True),
                ),
            ],
            options={
                'ordering': ['product_extra_attribute__name'],
                'verbose_name': 'Product extra attribute category',
                'verbose_name_plural': 'Product extra attribute categories',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='TecPapAttributeGroup',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                (
                    'year',
                    models.CharField(
                        default=b'2005',
                        max_length=b'20',
                        choices=[
                            (b'1990', b'1990'),
                            (b'1991', b'1991'),
                            (b'1992', b'1992'),
                            (b'1993', b'1993'),
                            (b'1994', b'1994'),
                            (b'1995', b'1995'),
                            (b'1996', b'1996'),
                            (b'1997', b'1997'),
                            (b'1998', b'1998'),
                            (b'1999', b'1999'),
                            (b'2000', b'2000'),
                            (b'2001', b'2001'),
                            (b'2002', b'2002'),
                            (b'2003', b'2003'),
                            (b'2004', b'2004'),
                            (b'2005', b'2005'),
                            (b'2006', b'2006'),
                            (b'2007', b'2007'),
                            (b'2008', b'2008'),
                            (b'2009', b'2009'),
                            (b'2010', b'2010'),
                            (b'2011', b'2011'),
                            (b'2012', b'2012'),
                            (b'2013', b'2013'),
                            (b'2014', b'2014'),
                            (b'2015', b'2015'),
                            (b'2016', b'2016'),
                            (b'2017', b'2017'),
                            (b'2018', b'2018'),
                            (b'2019', b'2019'),
                            (b'2020', b'2020'),
                            (b'2021', b'2021'),
                            (b'2022', b'2022'),
                        ],
                    ),
                ),
                (
                    'manufacturer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.SET_NULL, to='tecpap.Manufacturer', null=True
                    ),
                ),
                ('model', models.ManyToManyField(to='tecpap.Model', null=True)),
                (
                    'product',
                    models.ForeignKey(
                        related_name='tecpap_attributes', to='catalogue.Product', on_delete=models.CASCADE
                    ),
                ),
                ('typ', models.ManyToManyField(to='tecpap.Type', null=True, blank=True)),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='productextraattribute',
            name='categories',
            field=models.ManyToManyField(
                to='catalogue.Category',
                null=True,
                verbose_name='Category',
                through='catalogue.ProductExtraAttributeCategory',
            ),
            preserve_default=True,
        ),
        migrations.AlterModelOptions(
            name='category',
            options={'ordering': ['path'], 'verbose_name': 'Category', 'verbose_name_plural': 'Categories'},
        ),
        migrations.RemoveField(
            model_name='category',
            name='full_name',
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name',
            field=models.CharField(default=b'', max_length=100, verbose_name='Alternative name', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_de',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_en',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_es',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_fr',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_lt',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_pl',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='alternative_name_ru',
            field=models.CharField(
                default=b'', max_length=100, null=True, verbose_name='Alternative name', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_de',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_en',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_es',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_fr',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_lt',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_pl',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='description_ru',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_de',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_es',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_fr',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_lt',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_pl',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='name_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Name', db_index=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='rrr_categories',
            field=models.ManyToManyField(to='rrr.RrrCategory', verbose_name='RRR categories', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='category',
            name='sort',
            field=models.IntegerField(default=100, verbose_name='Sort'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='active',
            field=models.BooleanField(default=True, db_index=True, verbose_name='Active'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='color',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                default=None,
                blank=True,
                to='tecpap.Color',
                null=True,
                verbose_name='Color',
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='commercial_id',
            field=models.CharField(default=1, max_length=20, editable=False, db_index=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='condition',
            field=models.CharField(
                default=b'used',
                max_length=50,
                verbose_name='Condition',
                choices=[(b'new', 'New product'), (b'used', 'Used product'), (b'broken', 'Broken product')],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_de',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_en',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_es',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_fr',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_lt',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_pl',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_ru',
            field=models.TextField(null=True, verbose_name='Description', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='description_template',
            field=models.ForeignKey(
                default=None,
                blank=True,
                to='catalogue.ProductDescriptionTemplate',
                null=True,
                verbose_name='Description template',
                on_delete=models.PROTECT,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='discount_perc',
            field=models.IntegerField(default=0, verbose_name='Discount, perc'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='drive_position',
            field=models.CharField(
                default=b'',
                max_length=20,
                verbose_name='Drive position',
                blank=True,
                choices=[(b'lhd', 'LHD'), (b'rhd', 'RHD'), (b'uhd', 'Universal')],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='extra_attributes',
            field=models.ManyToManyField(
                to='catalogue.ProductExtraAttribute', null=True, verbose_name='Extra attributes', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='extra_code',
            field=models.CharField(default=b'', max_length=255, verbose_name='Extra part. code', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='extra_manufacturer',
            field=models.CharField(default=b'', max_length=100, verbose_name='Extra manufacturer', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='is_available',
            field=models.BooleanField(default=True, db_index=True, editable=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='meta_description',
            field=models.CharField(
                help_text='Description used by search engines',
                verbose_name='Meta description',
                max_length=1000,
                editable=False,
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='meta_keywords',
            field=models.CharField(
                help_text='Keywords used by search engines',
                verbose_name='Meta keywords',
                max_length=500,
                editable=False,
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='original_code',
            field=models.CharField(
                default=b'',
                help_text='You can enter multiple codes separated by comma',
                max_length=255,
                verbose_name='Original part. code(s)',
                blank=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='original_code_cleaned',
            field=models.CharField(default=b'', max_length=255, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='owner',
            field=models.ForeignKey(
                verbose_name='Owner', blank=True, to=settings.AUTH_USER_MODEL, null=True, on_delete=models.PROTECT
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='package_height',
            field=models.PositiveIntegerField(default=0, verbose_name='Package height (cm)'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='package_length',
            field=models.PositiveIntegerField(default=0, verbose_name='Package length (cm)'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='package_weight',
            field=models.DecimalField(default=1, verbose_name='Package weight (kg.)', max_digits=12, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='package_width',
            field=models.PositiveIntegerField(default=0, verbose_name='Package width (cm)'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='pap_product_id',
            field=models.IntegerField(db_index=True, unique=True, null=True, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='price_factor',
            field=models.DecimalField(default=1, verbose_name='Price factor', max_digits=12, decimal_places=2),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='shipping_methods',
            field=models.ManyToManyField(
                to='shipping.OrderAndItemCharges', null=True, verbose_name='Shipping charges', blank=True
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='subowner',
            field=models.ForeignKey(
                related_name='manager_products',
                verbose_name='Subowner (manager)',
                blank=True,
                to=settings.AUTH_USER_MODEL,
                null=True,
                on_delete=models.PROTECT,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='summary',
            field=models.TextField(default='', editable=False),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='product',
            name='title_de',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_en',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_es',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_fr',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_lt',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_pl',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='title_ru',
            field=models.CharField(max_length=255, null=True, verbose_name='Title', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='top_item',
            field=models.BooleanField(default=False, db_index=True, verbose_name='Top item'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='product',
            name='warehouse_code',
            field=models.CharField(default=b'', max_length=255, verbose_name='Warehouse code', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_de',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_en',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_es',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_fr',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_lt',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_pl',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='productclass',
            name='name_ru',
            field=models.CharField(max_length=128, null=True, verbose_name='Name'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=models.SlugField(max_length=255, verbose_name='Slug'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='product',
            name='product_class',
            field=models.ForeignKey(
                related_name='products',
                on_delete=django.db.models.deletion.PROTECT,
                blank=True,
                to='catalogue.ProductClass',
                help_text='Choose what type of product this is',
                null=True,
                verbose_name='Product type',
            ),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='code',
            field=models.SlugField(
                max_length=128,
                verbose_name='Code',
                validators=[
                    django.core.validators.RegexValidator(
                        regex=b'^[a-zA-Z_][0-9a-zA-Z_]*$',
                        message="Code can only contain the letters a-z, A-Z, digits, and underscores, and can't start with a digit",
                    ),
                    oscar.core.validators.non_python_keyword,
                ],
            ),
            preserve_default=True,
        ),
    ]
