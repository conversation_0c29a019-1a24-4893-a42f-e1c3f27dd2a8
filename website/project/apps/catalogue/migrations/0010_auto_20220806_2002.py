# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-08-06 17:02
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models
import oscar.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0009_auto_20220803_1922'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='condition',
            field=models.CharField(choices=[('new', 'New product'), ('used', 'Used product'), ('broken', 'Broken product')], default='used', max_length=50, verbose_name='Condition'),
        ),
        migrations.AlterField(
            model_name='product',
            name='drive_position',
            field=models.CharField(blank=True, choices=[('lhd', 'LHD'), ('rhd', 'RHD'), ('uhd', 'Universal')], default='', max_length=20, verbose_name='Drive position'),
        ),
        migrations.AlterField(
            model_name='product',
            name='extra_code',
            field=models.Char<PERSON>ield(blank=True, default='', max_length=255, verbose_name='Extra part. code'),
        ),
        migrations.AlterField(
            model_name='product',
            name='extra_manufacturer',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='Extra manufacturer'),
        ),
        migrations.AlterField(
            model_name='product',
            name='original_code',
            field=models.CharField(blank=True, default='', help_text='You can enter multiple codes separated by comma', max_length=255, verbose_name='Original part. code(s)'),
        ),
        migrations.AlterField(
            model_name='product',
            name='original_code_cleaned',
            field=models.CharField(blank=True, default='', editable=False, max_length=255),
        ),
        migrations.AlterField(
            model_name='product',
            name='structure',
            field=models.CharField(choices=[('standalone', 'Stand-alone product'), ('parent', 'Parent product'), ('child', 'Child product')], default='standalone', max_length=10, verbose_name='Product structure'),
        ),
        migrations.AlterField(
            model_name='product',
            name='warehouse_code',
            field=models.CharField(blank=True, default='', max_length=255, verbose_name='Warehouse code'),
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='code',
            field=models.SlugField(max_length=128, validators=[django.core.validators.RegexValidator(message="Code can only contain the letters a-z, A-Z, digits, and underscores, and can't start with a digit.", regex='^[a-zA-Z_][0-9a-zA-Z_]*$'), oscar.core.validators.non_python_keyword], verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='type',
            field=models.CharField(choices=[('text', 'Text'), ('integer', 'Integer'), ('boolean', 'True / False'), ('float', 'Float'), ('richtext', 'Rich Text'), ('date', 'Date'), ('datetime', 'Datetime'), ('option', 'Option'), ('multi_option', 'Multi Option'), ('entity', 'Entity'), ('file', 'File'), ('image', 'Image')], default='text', max_length=20, verbose_name='Type'),
        ),
        migrations.AlterField(
            model_name='tecpapattributegroup',
            name='year',
            field=models.CharField(choices=[('1990', '1990'), ('1991', '1991'), ('1992', '1992'), ('1993', '1993'), ('1994', '1994'), ('1995', '1995'), ('1996', '1996'), ('1997', '1997'), ('1998', '1998'), ('1999', '1999'), ('2000', '2000'), ('2001', '2001'), ('2002', '2002'), ('2003', '2003'), ('2004', '2004'), ('2005', '2005'), ('2006', '2006'), ('2007', '2007'), ('2008', '2008'), ('2009', '2009'), ('2010', '2010'), ('2011', '2011'), ('2012', '2012'), ('2013', '2013'), ('2014', '2014'), ('2015', '2015'), ('2016', '2016'), ('2017', '2017'), ('2018', '2018'), ('2019', '2019'), ('2020', '2020'), ('2021', '2021'), ('2022', '2022')], default='2005', max_length=20),
        ),
    ]
