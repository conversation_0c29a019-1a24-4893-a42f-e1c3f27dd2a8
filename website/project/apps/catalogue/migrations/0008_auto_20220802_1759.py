# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0007_auto_20220802_1648'),
    ]

    operations = [
        migrations.AddField(
            model_name='productattributevalue',
            name='value_datetime',
            field=models.DateTimeField(null=True, verbose_name='DateTime', blank=True),
        ),
        migrations.AddField(
            model_name='productattributevalue',
            name='value_multi_option',
            field=models.ManyToManyField(
                related_name='multi_valued_attribute_values',
                verbose_name='Value multi option',
                to='catalogue.AttributeOption',
                blank=True,
            ),
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='option_group',
            field=models.ForeignKey(
                blank=True,
                to='catalogue.AttributeOptionGroup',
                help_text='Select an option group if using type "Option" or "Multi Option"',
                null=True,
                verbose_name='Option Group',
                on_delete=models.CASCADE,
            ),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='productattribute',
            name='type',
            field=models.CharField(
                default=b'text',
                max_length=20,
                verbose_name='Type',
                choices=[
                    (b'text', 'Text'),
                    (b'integer', 'Integer'),
                    (b'boolean', 'True / False'),
                    (b'float', 'Float'),
                    (b'richtext', 'Rich Text'),
                    (b'date', 'Date'),
                    (b'datetime', b'Datetime'),
                    (b'option', 'Option'),
                    (b'multi_option', 'Multi Option'),
                    (b'entity', 'Entity'),
                    (b'file', 'File'),
                    (b'image', 'Image'),
                ],
            ),
        ),
        migrations.AlterUniqueTogether(
            name='productimage',
            unique_together=set([]),
        ),
    ]
