# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0005_auto_20220720_1651'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='extra_attributes',
            field=models.ManyToManyField(to='catalogue.ProductExtraAttribute', verbose_name='Extra attributes', blank=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='shipping_methods',
            field=models.ManyToManyField(to='shipping.OrderAndItemCharges', verbose_name='Shipping charges', blank=True),
        ),
        migrations.AlterField(
            model_name='productextraattribute',
            name='categories',
            field=models.ManyToManyField(to='catalogue.Category', verbose_name='Category', through='catalogue.ProductExtraAttributeCategory'),
        ),
        migrations.AlterField(
            model_name='tecpapattributegroup',
            name='model',
            field=models.ManyToManyField(to='tecpap.Model'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='tecpapattributegroup',
            name='typ',
            field=models.ManyToManyField(to='tecpap.Type', blank=True),
        ),
    ]
