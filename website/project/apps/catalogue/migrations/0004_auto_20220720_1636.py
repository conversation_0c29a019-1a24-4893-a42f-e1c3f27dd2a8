# -*- coding: utf-8 -*-
from django.db import models, migrations

from oscar.core.loading import get_model

ORMCategory = get_model('catalogue', 'Category')


def remove_ancestor_slugs(apps, schema_editor):
    MigrationCategory = apps.get_model('catalogue', 'Category')
    for category in MigrationCategory.objects.all():
        category.slug = category.slug.split(ORMCategory._slug_separator)[-1]
        category.save()


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0003_product_structure'),
    ]

    operations = [migrations.RunPython(remove_ancestor_slugs)]
