# -*- coding: utf-8 -*-


from django.db import migrations, models
import django.core.validators
import oscar.core.validators
import oscar.models.fields.slugfield


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0006_auto_20220802_1350'),
    ]

    operations = [
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=oscar.models.fields.slugfield.SlugField(max_length=255, verbose_name='Slug'),
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='code',
            field=models.SlugField(max_length=128, verbose_name='Code', validators=[django.core.validators.RegexValidator(regex=b'^[a-zA-Z_][0-9a-zA-Z_]*$', message="Code can only contain the letters a-z, A-Z, digits, and underscores, and can't start with a digit."), oscar.core.validators.non_python_keyword]),
        ),
        migrations.AlterUniqueTogether(
            name='attributeoption',
            unique_together=set([('group', 'option')]),
        ),
    ]
