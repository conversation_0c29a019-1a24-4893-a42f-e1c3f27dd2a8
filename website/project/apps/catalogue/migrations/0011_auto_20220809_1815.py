# Generated by Django 2.2.28 on 2022-08-09 15:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0010_auto_20220806_2002'),
    ]

    operations = [
        migrations.AlterField(
            model_name='category',
            name='rrr_categories',
            field=models.ManyToManyField(blank=True, limit_choices_to={'depth__gt': 2}, to='rrr.RrrCategory', verbose_name='RRR categories'),
        ),
        migrations.AlterField(
            model_name='productattribute',
            name='option_group',
            field=models.ForeignKey(blank=True, help_text='Select an option group if using type "Option" or "Multi Option"', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_attributes', to='catalogue.AttributeOptionGroup', verbose_name='Option Group'),
        ),
    ]
