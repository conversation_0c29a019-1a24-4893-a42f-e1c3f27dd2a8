# Generated by Django 3.2.15 on 2022-09-01 14:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0013_auto_20220828_1939'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='option',
            options={'ordering': ['name'], 'verbose_name': 'Option', 'verbose_name_plural': 'Options'},
        ),
        migrations.AddField(
            model_name='category',
            name='meta_description',
            field=models.TextField(blank=True, null=True, verbose_name='Meta description'),
        ),
        migrations.AddField(
            model_name='category',
            name='meta_title',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Meta title'),
        ),
        migrations.AddField(
            model_name='option',
            name='required',
            field=models.BooleanField(default=False, verbose_name='Is this option required?'),
        ),
        migrations.AddField(
            model_name='product',
            name='meta_title',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Meta title'),
        ),
        migrations.AlterField(
            model_name='option',
            name='name',
            field=models.CharField(db_index=True, max_length=128, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='option',
            name='type',
            field=models.CharField(choices=[('text', 'Text'), ('integer', 'Integer'), ('boolean', 'True / False'), ('float', 'Float'), ('date', 'Date')], default='text', max_length=255, verbose_name='Type'),
        ),
        migrations.AlterField(
            model_name='product',
            name='meta_description',
            field=models.CharField(blank=True, help_text='Description used by search engines', max_length=1000, verbose_name='Meta description'),
        ),
        migrations.AlterField(
            model_name='productattributevalue',
            name='value_boolean',
            field=models.BooleanField(blank=True, db_index=True, null=True, verbose_name='Boolean'),
        ),
    ]
