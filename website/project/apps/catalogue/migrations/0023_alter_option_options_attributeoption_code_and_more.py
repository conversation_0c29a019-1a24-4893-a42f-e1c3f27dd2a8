# Generated by Django 4.2.9 on 2024-06-21 15:47

from django.db import migrations, models
import django.db.models.deletion
import oscar.models.fields
import oscar.models.fields.slugfield


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0022_auto_20240316_1156'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='option',
            options={'ordering': ['order', 'name'], 'verbose_name': 'Option', 'verbose_name_plural': 'Options'},
        ),
        migrations.AddField(
            model_name='attributeoption',
            name='code',
            field=oscar.models.fields.NullCharField(max_length=255, unique=True, verbose_name='Unique identifier'),
        ),
        migrations.AddField(
            model_name='attributeoptiongroup',
            name='code',
            field=oscar.models.fields.NullCharField(max_length=255, unique=True, verbose_name='Unique identifier'),
        ),
        migrations.AddField(
            model_name='category',
            name='code',
            field=oscar.models.fields.NullCharField(max_length=255, unique=True, verbose_name='Code'),
        ),
        migrations.AddField(
            model_name='option',
            name='help_text',
            field=models.CharField(blank=True, help_text='Help text shown to the user on the add to basket form', max_length=255, null=True, verbose_name='Help text'),
        ),
        migrations.AddField(
            model_name='option',
            name='option_group',
            field=models.ForeignKey(blank=True, help_text='Select an option group if using type "Option" or "Multi Option"', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_options', to='catalogue.attributeoptiongroup', verbose_name='Option Group'),
        ),
        migrations.AddField(
            model_name='option',
            name='order',
            field=models.IntegerField(blank=True, db_index=True, help_text='Controls the ordering of product options on product detail pages', null=True, verbose_name='Ordering'),
        ),
        migrations.AddField(
            model_name='productimage',
            name='code',
            field=oscar.models.fields.NullCharField(max_length=255, unique=True, verbose_name='Code'),
        ),
        migrations.AlterField(
            model_name='option',
            name='type',
            field=models.CharField(choices=[('text', 'Text'), ('integer', 'Integer'), ('boolean', 'True / False'), ('float', 'Float'), ('date', 'Date'), ('select', 'Select'), ('radio', 'Radio'), ('multi_select', 'Multi select'), ('checkbox', 'Checkbox')], default='text', max_length=255, verbose_name='Type'),
        ),
        migrations.AlterField(
            model_name='product',
            name='slug',
            field=oscar.models.fields.slugfield.SlugField(max_length=255, verbose_name='Slug'),
        ),
        migrations.AlterUniqueTogether(
            name='productattribute',
            unique_together={('code', 'product_class')},
        ),
    ]
