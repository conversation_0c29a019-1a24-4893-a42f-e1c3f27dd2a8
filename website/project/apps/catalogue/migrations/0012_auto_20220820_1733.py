# Generated by Django 2.2.28 on 2022-08-20 14:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('catalogue', '0011_auto_20220809_1815'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='is_public',
            field=models.BooleanField(default=True, help_text='Show this product in search results and catalogue listings.', verbose_name='Is public'),
        ),
        migrations.AlterField(
            model_name='product',
            name='date_created',
            field=models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='Date created'),
        ),
        migrations.AlterField(
            model_name='productattributevalue',
            name='value_boolean',
            field=models.NullBooleanField(db_index=True, verbose_name='Boolean'),
        ),
        migrations.AlterField(
            model_name='productattributevalue',
            name='value_date',
            field=models.DateField(blank=True, db_index=True, null=True, verbose_name='Date'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='productattributevalue',
            name='value_datetime',
            field=models.DateTimeField(blank=True, db_index=True, null=True, verbose_name='DateTime'),
        ),
        migrations.AlterField(
            model_name='productattributevalue',
            name='value_float',
            field=models.FloatField(blank=True, db_index=True, null=True, verbose_name='Float'),
        ),
        migrations.AlterField(
            model_name='productattributevalue',
            name='value_integer',
            field=models.IntegerField(blank=True, db_index=True, null=True, verbose_name='Integer'),
        ),
        migrations.AlterField(
            model_name='productimage',
            name='display_order',
            field=models.PositiveIntegerField(db_index=True, default=0, help_text='An image with a display order of zero will be the primary image for a product', verbose_name='Display order'),
        ),
        migrations.AlterField(
            model_name='productrecommendation',
            name='ranking',
            field=models.PositiveSmallIntegerField(db_index=True, default=0, help_text='Determines order of the products. A product with a higher value will appear before one with a lower ranking.', verbose_name='Ranking'),
        ),
    ]
