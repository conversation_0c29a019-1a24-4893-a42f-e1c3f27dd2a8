import os
import gzip
import logging
from django.conf import settings
from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from django.urls import reverse
from django.utils import translation
from project.apps.catalogue.models import Product, Category
from project.apps.tecpap.models import Manufacturer, Model

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Generates both Google and Bing sitemap XML files for all domains"
    
    # Number of URLs per sitemap file
    URLS_PER_FILE = 30000
    
    # Number of URLs to write in one chunk (for memory efficiency)
    WRITE_CHUNK_SIZE = 1000

    def write_sitemap_chunk(self, file_path, urls, use_gzip=True):
        """
        Write sitemap XML with buffered writing for better performance
        """
        open_func = gzip.open if use_gzip else open
        mode = 'wt' if use_gzip else 'w'
        
        with open_func(file_path, mode, encoding='utf-8') as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n')
            
            for i in range(0, len(urls), self.WRITE_CHUNK_SIZE):
                chunk = urls[i:i + self.WRITE_CHUNK_SIZE]
                xml_chunk = ''.join(
                    f'  <url>\n    <loc>{url["location"]}</loc>\n'
                    f'    <lastmod>{url["lastmod"].strftime("%Y-%m-%d") if url.get("lastmod") else ""}</lastmod>\n  </url>\n'
                    for url in chunk
                )
                f.write(xml_chunk)
                
            f.write('</urlset>')

    def write_sitemap_index(self, file_path, sitemap_urls, use_gzip=True):
        """
        Write sitemap index XML
        """
        open_func = gzip.open if use_gzip else open
        mode = 'wt' if use_gzip else 'w'
        
        with open_func(file_path, mode, encoding='utf-8') as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write('<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n')
            
            for url in sitemap_urls:
                f.write('  <sitemap>\n')
                f.write(f'    <loc>{url}</loc>\n')
                f.write('  </sitemap>\n')
                
            f.write('</sitemapindex>')

    def generate_urls_for_domain(self, products, domain, languages):
        """
        Generate product URLs for a domain in bulk for all languages
        """
        urls = []
        for product in products:
            for lang_code, _ in languages:
                urls.append({
                    'location': f'https://www.{domain}/{lang_code}/catalogue/{product.slug}_{product.id}/',
                    'lastmod': product.date_updated
                })
        return urls

    def generate_category_urls(self, domain, languages):
        """
        Generate category URLs for a domain
        """
        urls = []
        categories = Category.objects.filter(depth__gt=1)
        
        for category in categories:
            base_url = category.get_absolute_url()
            if not base_url.endswith('/'):
                base_url += '/'
            for lang_code, _ in languages:
                if lang_code == 'en':
                    url = base_url
                else:
                    url = base_url.replace('/en/', f'/{lang_code}/')
                urls.append({
                    'location': f'https://www.{domain}{url}',
                    'lastmod': None
                })
        return urls

    def generate_manufacturer_urls(self, domain, languages):
        """
        Generate manufacturer URLs for a domain
        """
        urls = []
        manufacturers = Manufacturer.objects.all()
        
        for manufacturer in manufacturers:
            for lang_code, _ in languages:
                urls.append({
                    'location': f'https://www.{domain}/{lang_code}/car/{manufacturer.slug}/',
                    'lastmod': None
                })
        return urls

    def generate_model_urls(self, domain, languages):
        """
        Generate model URLs for a domain
        """
        urls = []
        models = Model.objects.filter(active=True).select_related('manufacturer')
        
        for model in models:
            for lang_code, _ in languages:
                urls.append({
                    'location': f'https://www.{domain}/{lang_code}/car/{model.manufacturer.slug}/{model.slug}/',
                    'lastmod': None
                })
        return urls

    def generate_static_urls(self, domain, languages):
        """
        Generate static page URLs for a domain
        """
        urls = []
        static_pages = [
            'fp-about',
            'fp-contacts',
            'fp-payment',
            'fp-shipping',
            'fp-terms-and-conditions',
        ]
        
        for page in static_pages:
            for lang_code, _ in languages:
                with translation.override('en'):
                    base_url = reverse(page)
                    if base_url.startswith('/en/'):
                        base_url = base_url[3:]
                    if not base_url.endswith('/'):
                        base_url += '/'
                    urls.append({
                        'location': f'https://www.{domain}/{lang_code}{base_url}',
                        'lastmod': None
                    })
        return urls

    def generate_canonical_urls_for_domain(self, products, domain, primary_lang):
        """
        Generate canonical URLs for a domain (Bing version - only primary language)
        """
        base_url = f'https://www.{domain}/{primary_lang}/catalogue/'
        return [
            {
                'location': f'{base_url}{product.slug}_{product.id}/',
                'lastmod': product.date_updated
            }
            for product in products
        ]

    def handle(self, *args, **options):
        try:
            # Get all active products in one query with minimal fields
            logger.info("Fetching all active products...")
            all_products = Product.objects.filter(
                active=True,
                is_available=True,
                stockrecords__num_in_stock__gt=0
            ).only(
                'id', 'slug', 'date_updated'
            ).distinct()
            
            total_products = len(all_products)
            logger.info(f"Found {total_products} active products")

            # Get all domains
            domains = [settings.MAIN_DOMAIN]
            domains.extend(branch['domain'] for branch in settings.BRANCH_SETTINGS.values())
            
            for domain in domains:
                logger.info(f"Processing domain: {domain}")
                
                # Get languages for this domain
                if domain == settings.MAIN_DOMAIN:
                    primary_lang = 'en'
                    languages = settings.LANGUAGES
                else:
                    # Find branch settings for this domain
                    for branch_code, config in settings.BRANCH_SETTINGS.items():
                        if config['domain'] == domain:
                            languages = config['languages']
                            primary_lang = languages[0][0]
                            break
                
                # Create directories
                google_dir = os.path.join(settings.STATIC_ROOT, 'sitemaps', domain)
                bing_dir = os.path.join(settings.STATIC_ROOT, 'sitemaps', domain, 'bing')
                os.makedirs(google_dir, exist_ok=True)
                os.makedirs(bing_dir, exist_ok=True)
                
                # Generate Google sitemaps
                logger.info(f"Generating Google sitemaps for {domain}")
                google_sitemap_urls = []

                # Products sitemap
                logger.info(f"Generating product URLs with languages {[lang[0] for lang in languages]}")
                product_urls = self.generate_urls_for_domain(all_products, domain, languages)
                
                for i, start_idx in enumerate(range(0, len(product_urls), self.URLS_PER_FILE)):
                    chunk = product_urls[start_idx:start_idx + self.URLS_PER_FILE]
                    
                    if i == 0:
                        filename = f'sitemap-products.xml.gz'
                        sitemap_url = f'https://www.{domain}/sitemap-products.xml'
                    else:
                        filename = f'sitemap-products.{i+1}.xml.gz'
                        sitemap_url = f'https://www.{domain}/sitemap-products.xml?p={i+1}'
                    
                    sitemap_path = os.path.join(google_dir, filename)
                    logger.info(f"Writing Google products sitemap chunk {i+1} to {filename}")
                    self.write_sitemap_chunk(sitemap_path, chunk, use_gzip=True)
                    google_sitemap_urls.append(sitemap_url)

                # # Categories sitemap
                # logger.info("Generating category URLs")
                # category_urls = self.generate_category_urls(domain, languages)
                # sitemap_path = os.path.join(google_dir, 'sitemap-categories.xml.gz')
                # self.write_sitemap_chunk(sitemap_path, category_urls, use_gzip=True)
                # google_sitemap_urls.append(f'https://www.{domain}/sitemap-categories.xml')

                # # Manufacturers sitemap
                # logger.info("Generating manufacturer URLs")
                # manufacturer_urls = self.generate_manufacturer_urls(domain, languages)
                # sitemap_path = os.path.join(google_dir, 'sitemap-manufacturers.xml.gz')
                # self.write_sitemap_chunk(sitemap_path, manufacturer_urls, use_gzip=True)
                # google_sitemap_urls.append(f'https://www.{domain}/sitemap-manufacturers.xml')

                # # Models sitemap
                # logger.info("Generating model URLs")
                # model_urls = self.generate_model_urls(domain, languages)
                # sitemap_path = os.path.join(google_dir, 'sitemap-models.xml.gz')
                # self.write_sitemap_chunk(sitemap_path, model_urls, use_gzip=True)
                # google_sitemap_urls.append(f'https://www.{domain}/sitemap-models.xml')

                # # Static pages sitemap
                # logger.info("Generating static page URLs")
                # static_urls = self.generate_static_urls(domain, languages)
                # sitemap_path = os.path.join(google_dir, 'sitemap-static.xml.gz')
                # self.write_sitemap_chunk(sitemap_path, static_urls, use_gzip=True)
                # google_sitemap_urls.append(f'https://www.{domain}/sitemap-static.xml')
                
                # Write Google sitemap index
                logger.info("Writing Google sitemap index")
                index_path = os.path.join(google_dir, 'sitemap.xml.gz')
                self.write_sitemap_index(index_path, google_sitemap_urls, use_gzip=True)
                
                # Generate Bing sitemaps (only primary language)
                logger.info(f"Generating Bing sitemaps for {domain} with language {primary_lang}")
                canonical_urls = self.generate_canonical_urls_for_domain(all_products, domain, primary_lang)
                
                # Split into chunks and write Bing sitemaps
                for i, start_idx in enumerate(range(0, len(canonical_urls), self.URLS_PER_FILE)):
                    chunk = canonical_urls[start_idx:start_idx + self.URLS_PER_FILE]
                    
                    filename = f'sitemap-products-{i+1}.xml'
                    sitemap_path = os.path.join(bing_dir, filename)
                    
                    logger.info(f"Writing Bing sitemap chunk {i+1} to {filename}")
                    self.write_sitemap_chunk(sitemap_path, chunk, use_gzip=False)

            logger.info(f"Successfully generated all sitemaps for all domains")
        except Exception as e:
            logger.error(f"Error generating sitemaps: {str(e)}")
            raise