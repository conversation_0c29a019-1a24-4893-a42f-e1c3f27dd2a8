from django.core.management.base import BaseCommand
from project.apps.catalogue.models import Category

class Command(BaseCommand):
    help = 'Recalculates numchild for all categories'

    def handle(self, *args, **options):
        for category in Category.get_tree():
            # Treebeard should handle numchild automatically, but in case of inconsistencies:
            expected_numchild = category.get_children().count()
            if category.numchild != expected_numchild:
                category.numchild = expected_numchild
                category.save()
                self.stdout.write(
                    self.style.WARNING(
                        f'Updated numchild for category "{category.name}" from {category.numchild} to {expected_numchild}'
                    )
                )
        self.stdout.write(self.style.SUCCESS('Successfully recalculated numchild for all categories'))