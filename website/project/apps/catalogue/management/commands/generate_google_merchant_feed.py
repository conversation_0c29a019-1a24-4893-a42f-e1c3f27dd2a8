import os
import gzip
import logging
from decimal import Decimal as D
from django.conf import settings
from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from django.db import connection
from project.apps.shipping.models import OrderAndItemCharges
from oscar.core.loading import get_model
from oscar.apps.partner.strategy import Selector

logger = logging.getLogger(__name__)

Product = get_model('catalogue', 'Product')

class Command(BaseCommand):
    help = "Generates Google Merchant Feed XML files for DE and saves to MEDIA_ROOT/merchant_feeds"

    def get_shipping_cost(self, weight, country_code):
        """Calculate shipping cost based on weight for a given country"""
        try:
            # Get shipping method based on weight
            shipping_method = OrderAndItemCharges.objects.filter(
                weight_from__lte=D(str(weight)),
                weight_to__gte=D(str(weight)),
                countries__iso_3166_1_a2=country_code,
            ).first()

            if shipping_method:
                # Return economy shipping price
                return float(shipping_method.price_per_item)
            
            # If no shipping method found, return None
            return None
        except Exception as e:
            logger.error(f"Error calculating shipping cost: {str(e)}")
            return None

    def get_products(self):
        """Get all active products with required data"""
        return (
            Product.objects
            .filter(
                active=True,  # Must be active
                is_available=True,  # Must be available
            )
            .select_related(
                'product_class'
            )
            .prefetch_related(
                'stockrecords',
                'images',
                'categories',
                'tecpap_attributes'
            )
        )

    def escape_xml(self, text):
        """Escape special characters for XML"""
        if not text:
            return ""
        return (
            str(text)
            .replace('&', '&amp;')
            .replace('<', '&lt;')
            .replace('>', '&gt;')
            .replace('"', '&quot;')
            .replace("'", '&apos;')
        )

    def generate_feed_content(self, products, domain):
        """Generate Google Merchant Feed XML content"""
        # Get strategy for availability checks
        strategy = Selector().strategy()

        xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n'
        xml_content += '<rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">\n'
        xml_content += '<channel>\n'
        xml_content += f'<title>PARTAN Auto Teile - {domain}</title>\n'
        xml_content += f'<link>https://www.{domain}</link>\n'
        xml_content += '<description>Autoteile und Zubehör</description>\n'

        for product in products:
            # Check availability using purchase info
            purchase_info = strategy.fetch_for_product(product)
            if not purchase_info.availability.is_available_to_buy:
                continue

            # Get stockrecord
            stockrecord = product.stockrecords.first()
            if not stockrecord:
                continue

            shipping_cost = self.get_shipping_cost(product.package_weight, 'DE')  # Default to Germany
            if shipping_cost is None:
                continue

            # Get German category name
            category = product.categories.first()
            category_name_de = category.name_de if category else ""

            # Combine title parts manually with German category
            title_parts = [
                category_name_de,
                product.original_code_cleaned,
                product.get_main_manufacturer(),
                product.get_main_model_cleaned(),
                product.get_main_year()
            ]
            # Filter out None/empty values and join with spaces
            title = ' '.join(filter(None, title_parts))
            title = self.escape_xml(title[:150])  # Limit to 150 chars and escape XML

            # Generate German description using template
            description_template = '%(title)s %(code)s für %(brand)s %(model)s %(year)s. Bester Preis, schneller Versand.'
            description = description_template % {
                "year": product.get_main_year(),
                "brand": product.get_main_manufacturer(),
                "model": product.get_main_model_cleaned(),
                "code": f"{product.original_code} / {product.original_code_cleaned}",
                "title": category_name_de
            }
            description = self.escape_xml(description)

            xml_content += '<item>\n'
            xml_content += f'  <g:id>{product.id}</g:id>\n'
            xml_content += f'  <g:title>{title}</g:title>\n'
            xml_content += f'  <g:description>{description}</g:description>\n'
            # German URL as main link (for German market)
            xml_content += f'  <g:link>https://www.{domain}/de/catalogue/{product.slug}_{product.id}</g:link>\n'
            
            # Get product images
            images = list(product.images.all())
            
            if images:
                # Add main image
                main_image = images[0]
                xml_content += f'  <g:image_link>https://www.{domain}/media/{main_image.original.name}</g:image_link>\n'
                
                # Add up to 10 additional images
                for additional_image in images[1:11]:  # Limit to next 10 images after main image
                    xml_content += f'  <g:additional_image_link>https://www.{domain}/media/{additional_image.original.name}</g:additional_image_link>\n'
            else:
                # Fallback to default image if no images found
                xml_content += f'  <g:image_link>https://www.{domain}/media/image_not_found.jpg</g:image_link>\n'
            
            xml_content += '  <g:condition>used</g:condition>\n'
            xml_content += f'  <g:availability>{("in_stock" if stockrecord.num_in_stock > 0 else "out_of_stock")}</g:availability>\n'
            xml_content += f'  <g:price>{stockrecord.price} {stockrecord.price_currency}</g:price>\n'
            xml_content += f'  <g:brand>{self.escape_xml(product.get_main_manufacturer())}</g:brand>\n'
            xml_content += f'  <g:mpn>{self.escape_xml(product.original_code_cleaned or "N/A")}</g:mpn>\n'
            xml_content += '  <g:google_product_category>Vehicles &amp; Parts &gt; Vehicle Parts &amp; Accessories &gt; Motor Vehicle Parts</g:google_product_category>\n'
            xml_content += f'  <g:product_type>{self.escape_xml(category_name_de)}</g:product_type>\n'
            
            # Add shipping info
            xml_content += '  <g:shipping>\n'
            xml_content += '    <g:country>DE</g:country>\n'
            xml_content += '    <g:service>Economy</g:service>\n'
            xml_content += f'    <g:price>{shipping_cost} EUR</g:price>\n'
            xml_content += '  </g:shipping>\n'
            
            # Add weight
            if product.package_weight:
                xml_content += f'  <g:shipping_weight>{product.package_weight} kg</g:shipping_weight>\n'

            xml_content += '</item>\n'

        xml_content += '</channel>\n'
        xml_content += '</rss>'
        return xml_content

    def handle(self, *args, **options):
        try:
            # Use country code for directory and full domain for URLs
            country_code = 'de'  # For directory
            domain = 'partan.de'  # For URLs
            logger.info(f"Generating feed for German market: {country_code}")

            # Get all products
            products = self.get_products()
            total_products = len(products)
            logger.info(f"Found {total_products} products")

            # Calculate chunks
            chunk_size = 50000
            num_chunks = (total_products + chunk_size - 1) // chunk_size
            
            # Create directory if not exists
            feed_dir = os.path.join(settings.MEDIA_ROOT, 'merchant_feeds', country_code)
            os.makedirs(feed_dir, exist_ok=True)
            
            # Generate full feed
            full_feed_path = os.path.join(feed_dir, 'google_merchant_feed_full.xml.gz')
            with gzip.open(full_feed_path, 'wt', encoding='utf-8') as f:
                f.write(self.generate_feed_content(products, domain))
            
            # Generate chunks
            for i in range(num_chunks):
                start_idx = i * chunk_size
                end_idx = min((i + 1) * chunk_size, total_products)
                chunk_products = products[start_idx:end_idx]
                
                chunk_filename = f'google_merchant_feed_part{i+1}.xml.gz'
                chunk_path = os.path.join(feed_dir, chunk_filename)
                
                with gzip.open(chunk_path, 'wt', encoding='utf-8') as f:
                    f.write(self.generate_feed_content(chunk_products, domain))
            
            # Generate index file
            index_path = os.path.join(feed_dir, 'google_merchant_feed_index.xml')
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
                f.write('<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n')
                
                # Add full feed
                f.write('  <sitemap>\n')
                f.write(f'    <loc>https://www.{domain}/google-merchant-feed-full.xml.gz</loc>\n')
                f.write('  </sitemap>\n')
                
                # Add chunks
                for i in range(num_chunks):
                    f.write('  <sitemap>\n')
                    f.write(f'    <loc>https://www.{domain}/google-merchant-feed-part{i+1}.xml.gz</loc>\n')
                    f.write('  </sitemap>\n')
                
                f.write('</sitemapindex>')
            
            # Create symlink to full feed
            symlink_path = os.path.join(feed_dir, 'google_merchant_feed.xml.gz')
            if os.path.exists(symlink_path):
                os.unlink(symlink_path)
            os.symlink('google_merchant_feed_full.xml.gz', symlink_path)
            
            logger.info(f"Successfully generated {num_chunks} Google Merchant feed files for German market")
        except Exception as e:
            logger.error(f"Error generating Google Merchant feeds: {str(e)}")
            raise 