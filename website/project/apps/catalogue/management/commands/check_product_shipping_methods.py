# Python
import logging
from datetime import datetime, timedelta
import time

# Django
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db.models import Max, Q
from django.utils import timezone

from project.apps.catalogue.models import Product
from project.apps.shipping.models import OrderAndItemCharges

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Command for checking and fixing missing shipping methods.
    
    This command has three main modes of operation:
    
    1. Default mode (runs hourly via cron):
       Checks for shipping templates modified in the last hour and updates
       only the products affected by those templates. If no templates were
       modified, it still checks for products without shipping methods.
       
       python manage.py check_product_shipping_methods
    
    2. Specific template mode:
       Updates only products that fall within the weight range of a specific
       shipping template.
       
       python manage.py check_product_shipping_methods --template=123
    
    3. Full update mode:
       Updates shipping methods for all active products.
       
       python manage.py check_product_shipping_methods --full
    
    Additional options:
    
    --last-hours=N  Check for templates modified in the last N hours (default: 1)
                    Example: python manage.py check_product_shipping_methods --last-hours=4
    --batch-size=N  Process products in batches of N (default: 1000)
                    Example: python manage.py check_product_shipping_methods --batch-size=500
    """

    help = "Checks shipping methods and fixes missing ones, and updates products affected by shipping template changes"

    def add_arguments(self, parser):
        parser.add_argument(
            '--full',
            action='store_true',
            dest='full',
            default=False,
            help='Force a full update of all products',
        )
        parser.add_argument(
            '--template',
            dest='template_id',
            type=int,
            default=None,
            help='Update products for a specific shipping template ID',
        )
        parser.add_argument(
            '--last-hours',
            dest='last_hours',
            type=int,
            default=1,
            help='Check for templates modified in the last N hours (default: 1)',
        )
        parser.add_argument(
            '--batch-size',
            dest='batch_size',
            type=int,
            default=1000,
            help='Process products in batches of N (default: 1000)',
        )

    def process_products_in_batches(self, products_queryset, batch_size, process_function):
        """Process products in batches to avoid memory issues.
        
        Args:
            products_queryset: Django QuerySet of products to process
            batch_size: Number of products to process in each batch
            process_function: Function to call with each batch of products
        """
        total_count = products_queryset.count()
        logger.info(f"Found {total_count} products to process")
        
        if total_count == 0:
            return
        
        processed = 0
        start_time = time.time()
        
        # Process in batches by ID instead of loading all products into memory
        for i in range(0, total_count, batch_size):
            batch_ids = list(products_queryset.values_list('id', flat=True)[i:i+batch_size])
            batch = products_queryset.model.objects.filter(id__in=batch_ids)
            
            process_function(list(batch))
            
            processed += len(batch_ids)
            elapsed_time = time.time() - start_time
            avg_speed = processed / elapsed_time if elapsed_time > 0 else 0
            
            logger.info(f"Processed {processed}/{total_count} products ({processed/total_count*100:.1f}%) - {avg_speed:.1f} products/sec")

    def handle(self, *args, **options):
        force_full_update = options.get('full', False)
        template_id = options.get('template_id')
        last_hours = options.get('last_hours', 1)
        batch_size = options.get('batch_size', 1000)
        
        start_time = time.time()
        
        if template_id:
            # Update products for a specific template
            try:
                template = OrderAndItemCharges.objects.get(id=template_id)
                logger.info(f"Updating products for template ID {template_id}: {template.name}")
                self.stdout.write(f"Updating products for template ID {template_id}: {template.name}")
                
                products_queryset = Product.objects.filter(
                    active=True,
                    package_weight__gt=template.weight_from,
                    package_weight__lte=template.weight_to
                ).only('id', 'package_weight')
                
                if products_queryset.exists():
                    self.process_products_in_batches(
                        products_queryset,
                        batch_size,
                        Product.check_and_fix_missing_shipping_methods
                    )
                else:
                    logger.info(f"No products found for template {template_id}")
                    self.stdout.write(f"No products found for template {template_id}")
            except OrderAndItemCharges.DoesNotExist:
                logger.error(f"Template with ID {template_id} does not exist")
                self.stdout.write(self.style.ERROR(f"Template with ID {template_id} does not exist"))
                return
                
        elif force_full_update:
            # Full update requested
            logger.info("Running full shipping methods update")
            self.stdout.write("Running full shipping methods update...")
            
            products_queryset = Product.objects.filter(active=True).only('id', 'package_weight')
            self.process_products_in_batches(
                products_queryset,
                batch_size,
                Product.check_and_fix_missing_shipping_methods
            )
        else:
            # Get templates modified in the last hour (or specified hours)
            cutoff_time = timezone.now() - timedelta(hours=last_hours)
            modified_templates = OrderAndItemCharges.objects.filter(modified_at__gt=cutoff_time)
            
            if modified_templates.exists():
                template_count = modified_templates.count()
                logger.info(f"Found {template_count} shipping templates modified in the last {last_hours} hour(s)")
                
                # Create a Q object for efficient OR filtering
                weight_query = Q()
                for template in modified_templates:
                    weight_query |= Q(
                        package_weight__gt=template.weight_from,
                        package_weight__lte=template.weight_to
                    )
                
                # Apply the weight filter to find affected products
                if weight_query:
                    products_queryset = Product.objects.filter(
                        active=True
                    ).filter(
                        weight_query
                    ).only('id', 'package_weight').distinct()
                    
                    self.process_products_in_batches(
                        products_queryset,
                        batch_size,
                        Product.check_and_fix_missing_shipping_methods
                    )
                else:
                    logger.info("No valid weight ranges found in modified templates")
            else:
                logger.info(f"No shipping templates modified in the last {last_hours} hour(s)")
                
                # Still check for products without shipping methods
                logger.info("Checking for products without shipping methods")
                
                products_queryset = Product.objects.filter(
                    active=True, 
                    shipping_methods__isnull=True
                ).only('id', 'package_weight')
                
                self.process_products_in_batches(
                    products_queryset,
                    batch_size,
                    Product.check_and_fix_missing_shipping_methods
                )
        
        elapsed_time = time.time() - start_time
        logger.info(f"Command completed in {elapsed_time:.2f} seconds")
        
        #Product.check_and_fix_missing_shipping_methods_for_main_countries()
