# Python
import logging

# Django
from django.core.management.base import BaseCommand

from project.apps.catalogue.models import Product

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Command for checking product stock and disabling inconsistent products"""

    help = "Compares stock records and disables products having bigger stock than allowed"

    def handle(self, *args, **options):
        Product.compare_stock()
