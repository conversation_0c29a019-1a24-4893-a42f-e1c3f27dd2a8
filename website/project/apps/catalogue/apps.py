from django.urls import path, include

from oscar.apps.catalogue import apps
from oscar.core.loading import get_class


class CatalogueOnlyConfig(apps.CatalogueOnlyConfig):
    name = 'project.apps.catalogue'

    def ready(self):
        super().ready()
        self.catalogue_view = get_class('catalogue.views', 'ProductListView')
        self.detail_view = get_class('catalogue.views', 'ProductDetailView')
        self.category_view = get_class('catalogue.views', 'ProductCategoryView')

    def get_urls(self):
        urls = super().get_urls()
        urls += [
            path('<product_slug>_<int:pk>/', self.detail_view.as_view(), name='detail'),
            path(
                'category/<category_slug>_<int:pk>/',
                self.category_view.as_view(),
                name='category',
            ),
            path('ranges/<slug>/', self.range_view.as_view(), name='range'),
        ]
        return self.post_process_urls(urls)


class CatalogueReviewsOnlyConfig(apps.CatalogueReviewsOnlyConfig):
    def get_urls(self):
        urls = [path('reviews/', include(self.reviews_app.urls[0]))]
        return self.post_process_urls(urls)


class CatalogueConfig(CatalogueOnlyConfig, CatalogueReviewsOnlyConfig):
    """
    Composite class combining Products with Reviews
    """
