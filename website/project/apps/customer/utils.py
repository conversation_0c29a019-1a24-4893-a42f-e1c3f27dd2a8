from django.urls import reverse
from django.utils.http import int_to_base36
from django.contrib.auth.tokens import default_token_generator


def get_password_reset_url(user, token_generator=default_token_generator):
    """
    Generate a password-reset URL for a given user
    """
    return reverse(
        'password-reset-confirm',
        kwargs={'uidb36': int_to_base36(user.id), 'token': default_token_generator.make_token(user)},
    )


def normalise_email(email):
    """
    The local part of an email address is case-sensitive, the domain part
    isn't.  This function lowercases the host and should be used in all email
    handling.
    """
    clean_email = email.strip()
    if '@' in clean_email:
        local, host = clean_email.split('@')
        return local + '@' + host.lower()
    return clean_email
