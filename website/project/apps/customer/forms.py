from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import CommonPasswordValidator
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core import validators
from django.utils.translation import gettext_lazy as _

from oscar.apps.address import forms as address_forms
from oscar.apps.customer import forms as customer_forms
from oscar.apps.customer.utils import normalise_email
from oscar.core.loading import get_profile_class

Profile = get_profile_class()
from project.apps.address.models import UserAddress


class UserAddressForm(address_forms.UserAddressForm):
    class Meta:
        model = UserAddress
        # exclude = (
        #     'title',
        #     'last_name',
        #     'line2',
        #     'line3',
        #     'state',
        #     'user',
        #     'num_orders',
        #     'hash',
        #     'search_text',
        #     'is_default_for_billing',
        #     'is_default_for_shipping',
        # )
        fields = [
            'first_name',
            'line1',
            'line4',
            'postcode',
            'country',
            'phone_number',
            'notes',
            'company_name',
            'company_code',
            'vat_number',
        ]


class EmailAuthenticationForm(customer_forms.EmailAuthenticationForm):
    def __init__(self, *args, **kwargs):
        super(EmailAuthenticationForm, self).__init__(*args, **kwargs)
        for fk in list(self.fields.keys()):
            self.fields[fk].widget.attrs['class'] = 'form-control'


class CustomUserCreationForm(customer_forms.EmailUserCreationForm):
    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput,
        validators=[validators.MinLengthValidator(8)],
    )
    agree_with_terms = forms.BooleanField(label=_('Agree with terms and conditions'))

    def __init__(self, *args, **kwargs):
        super(CustomUserCreationForm, self).__init__(*args, **kwargs)
        self.fields['first_name'].label = _('First name / Company name')
        self.fields['first_name'].required = True
        for fk in list(self.fields.keys()):
            if fk != 'agree_with_terms':
                self.fields[fk].widget.attrs['class'] = 'form-control'

    class Meta:
        model = User
        fields = ('first_name', 'email')


class ProfileForm(forms.ModelForm):
    email = forms.EmailField(label=_('Email address'), required=True)
    first_name = forms.CharField(label=_('First name / Company name'), required=True)

    class Meta:
        model = Profile
        fields = [
            'first_name',
            'email',
            'contact_name',
            'contact_address',
            'contact_phone',
            'contact_fax',
            'contact_skype',
            'contact_info',
            'bank_info',
        ]

    def __init__(self, user, *args, **kwargs):
        self.user = user
        try:
            instance = Profile.objects.get(user=user)
        except ObjectDoesNotExist:
            # User has no profile, try a blank one
            instance = Profile(user=user)
        kwargs['instance'] = instance

        super().__init__(*args, **kwargs)

        self.fields['contact_info'].widget.attrs['rows'] = 5
        self.fields['bank_info'].widget.attrs['rows'] = 5
        self.fields['email'].initial = self.instance.user.email

        # Add user fields (we look for core user fields first)
        core_field_names = set([f.name for f in User._meta.fields])
        user_field_names = ['email']
        for field_name in ('first_name',):
            if field_name in core_field_names:
                user_field_names.append(field_name)
        user_field_names.extend(User._meta.additional_fields)

        # Store user fields so we know what to save later
        self.user_field_names = user_field_names

        # Add additional user fields
        additional_fields = forms.fields_for_model(User, fields=user_field_names)
        self.fields.update(additional_fields)

        # Set initial values
        for field_name in user_field_names:
            self.fields[field_name].initial = getattr(user, field_name)

    def clean_email(self):
        email = normalise_email(self.cleaned_data['email'])
        if User._default_manager.filter(email=email).exclude(id=self.user.id).exists():
            raise ValidationError(_("A user with this email address already exists"))
        return email

    def save(self, *args, **kwargs):
        user = self.instance.user

        # Save user also
        for field_name in self.user_field_names:
            setattr(user, field_name, self.cleaned_data[field_name])
        user.save()

        return super(ProfileForm, self).save(*args, **kwargs)


class ProductAlertForm(customer_forms.ProductAlertForm):
    def __init__(self, user, product, *args, **kwargs):
        super(ProductAlertForm, self).__init__(user, product, *args, **kwargs)
        for fk in self.fields:
            self.fields[fk].widget.attrs['class'] = 'form-control'
