from django.urls import path

from oscar.apps.customer import apps
from oscar.core.loading import get_class


class CustomerConfig(apps.CustomerConfig):
    name = 'project.apps.customer'

    def ready(self):
        super().ready()
        self.address_create_view = get_class('customer.views', 'AddressCreateView')
        self.address_update_view = get_class('customer.views', 'AddressUpdateView')
        self.login_view = get_class('customer.views', 'AccountAuthView')
        self.profile_view = get_class('customer.views', 'ProfileView')
        self.profile_update_view = get_class('customer.views', 'ProfileUpdateView')
        self.set_currency = get_class('customer.views', 'SetCurrencyView')
        self.close_warranty = get_class('customer.views', 'CloseWarrantyView')
        self.ac_search_suggestions = get_class('customer.views', 'AcSearchSuggestionsView')

    def get_urls(self):
        urls = super().get_urls()
        urls += [
            path('set-currency', self.set_currency.as_view(), name='set_currency'),
            path('close-warranty', self.close_warranty.as_view(), name='close_warranty'),
            path('ac-search-suggestions', self.ac_search_suggestions.as_view(), name='ac_search_suggestions'),
        ]
        return self.post_process_urls(urls)
