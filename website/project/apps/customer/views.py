import json
from datetime import datetime

from django.conf import settings
from django.contrib import messages
from django.contrib.auth import login as auth_login
from django.core.mail import send_mail
from django.urls import reverse
from django.http import HttpResponseRedirect, HttpResponse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.views.generic import TemplateView, View

from oscar.apps.customer import views
from oscar.core.compat import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from oscar.core.loading import get_profile_class

from .forms import CustomUserCreationForm, ProfileForm, UserAddressForm, EmailAuthenticationForm
from flatpages.models import FlatPage
from project.apps.catalogue.models import Product
from project.apps.partner.models import Partner, Currency

User = get_user_model()


class AcSearchSuggestionsView(View):
    def get(self, request, *args, **kwargs):
        search = request.GET.get('search', '')
        data = []
        if search:
            products = Product.objects.visible().filter(title__icontains=search)
            data = [
                {'value': '%s ... (ID=%s)' % (p.title[:40], p.commercial_id), 'label': p.title} for p in products.all()
            ]

            if products.count() == 0:
                try:
                    int(search)
                    products = Product.objects.filter(commercial_id__icontains=search).values('commercial_id')
                    data = [{'value': p['commercial_id'], 'label': p['commercial_id']} for p in products]
                except ValueError:
                    pass
        return HttpResponse(json.dumps(data), content_type="application/json")


class CloseWarrantyView(View):
    def get(self, request, *args, **kwargs):
        request.session['warranty_closed'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return HttpResponse('ok')


class FlatPageView(TemplateView):
    template_name = 'flatpages/default.html'

    def get_context_data(self, **kwargs):
        site_domain = self.request.get_host()
        context = super(FlatPageView, self).get_context_data(**kwargs)
        flat_page_url = self.request.GET.get('url')
        try:
            flat_page = FlatPage.objects.get(url=flat_page_url, sites__domain__exact=site_domain)
        except FlatPage.DoesNotExist:
            flat_page = None

        if flat_page:
            context['flatpage'] = flat_page
        return context


class SetCurrencyView(View):
    def post(self, request, *args, **kwargs):
        currency = request.POST.get('currency', 0)
        if currency:
            try:
                currency = Currency.objects.get(id=currency)
            except Currency.DoesNotExist:
                currency = None
            if currency:
                request.basket.user_currency = currency.currency
                request.basket.save()
                request.session['user_currency'] = request.basket.get_user_currency()
        return HttpResponseRedirect(request.META['HTTP_REFERER'])


class TermsAndConditionsView(TemplateView):
    template_name = 'oscar/customer/terms_and_conditions.html'

    def get_terms_and_conditions(self):
        site_domain = self.request.get_host()
        try:
            flat_page = FlatPage.objects.get(url='/terms-and-conditions/', sites__domain__exact=site_domain)
        except FlatPage.DoesNotExist:
            flat_page = None

        if flat_page:
            return mark_safe(flat_page.content)
        else:
            return ''

    def get_context_data(self, **kwargs):
        context = super(TermsAndConditionsView, self).get_context_data(**kwargs)
        context.update({'terms_and_conditions': self.get_terms_and_conditions()})
        return context


class RemoteContractView(TemplateView):
    template_name = 'oscar/customer/remote_contract.html'

    def get_remote_contract(self):
        site_domain = self.request.get_host()
        try:
            flat_page = FlatPage.objects.get(url='/remote-contract/', sites__domain__exact=site_domain)
        except FlatPage.DoesNotExist:
            flat_page = None

        if flat_page:
            return mark_safe(flat_page.content)
        else:
            return ''

    def get_context_data(self, **kwargs):
        context = super(RemoteContractView, self).get_context_data(**kwargs)
        context.update({'contract': self.get_remote_contract()})
        return context


class AddressCreateView(views.AddressCreateView):
    form_class = UserAddressForm


class AddressUpdateView(views.AddressUpdateView):
    form_class = UserAddressForm


class AccountAuthView(views.AccountAuthView):
    registration_form_class = CustomUserCreationForm
    login_form_class = EmailAuthenticationForm

    def validate_login_form(self):
        form = self.get_login_form(self.request)
        if form.is_valid():
            auth_login(self.request, form.get_user())
            # check if user is partner/vendor
            if self.request.user.is_staff:
                return HttpResponseRedirect(reverse('dashboard:index'))
            if form.cleaned_data['redirect_url']:
                return HttpResponseRedirect(form.cleaned_data['redirect_url'])
            else:
                return HttpResponseRedirect(reverse('customer:profile-view'))

        ctx = self.get_context_data(login_form=form)
        return self.render_to_response(ctx)

    def validate_registration_form(self):
        form = self.get_registration_form(self.request)
        if form.is_valid():
            self.register_user(form)
            membership = form.cleaned_data.get('membership', 'free')
            first_name = form.cleaned_data['first_name']
            messages.success(self.request, _("Your account successfully created"))
            return HttpResponseRedirect(form.cleaned_data['redirect_url'])

        ctx = self.get_context_data(registration_form=form)
        return self.render_to_response(ctx)


class ProfileView(views.ProfileView):
    def get_profile_fields(self, user):
        field_data = []

        # Check for custom user model
        for field_name in User._meta.additional_fields:
            field_data.append(self.get_model_field_data(user, field_name))

        # Check for profile class
        profile_class = get_profile_class()
        if profile_class:
            try:
                profile = profile_class.objects.get(user=user)
            except ObjectDoesNotExist:
                profile = profile_class(user=user)

            field_names = [f.name for f in profile._meta.local_fields]
            for field_name in field_names:
                if field_name in ('user', 'owner', 'id'):
                    continue

                vendor_fields = profile_class.VENDOR_FIELDS
                if not self.request.user.is_staff and field_name in vendor_fields:
                    continue

                field_data.append(self.get_model_field_data(profile, field_name))

        return field_data


class ProfileUpdateView(views.ProfileUpdateView):
    form_class = ProfileForm

    def get_form_kwargs(self):
        kwargs = super(ProfileUpdateView, self).get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
