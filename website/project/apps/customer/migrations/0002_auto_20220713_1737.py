# -*- coding: utf-8 -*-


from django.db import models, migrations
import tinymce.models
import django.core.validators
import oscar.models.fields.autoslugfield


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RemoteContract',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('contract', tinymce.models.HTMLField(verbose_name='Contract text')),
            ],
            options={
                'verbose_name': 'Remote contract',
                'verbose_name_plural': 'Remote contract',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='RemoteContractTranslation',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('lang', models.Char<PERSON>ield(max_length=20, verbose_name='Language')),
                ('contract', tinymce.models.HTMLField(verbose_name='Remote contract')),
                (
                    'parent',
                    models.ForeignKey(
                        related_name='translations',
                        verbose_name='Remote contract',
                        to='customer.RemoteContract',
                        on_delete=models.CASCADE,
                    ),
                ),
            ],
            options={},
            bases=(models.Model,),
        ),
        migrations.AlterField(
            model_name='communicationeventtype',
            name='code',
            field=oscar.models.fields.autoslugfield.AutoSlugField(
                populate_from=b'name',
                validators=[
                    django.core.validators.RegexValidator(
                        regex=b'^[a-zA-Z_][0-9a-zA-Z_]*$',
                        message="Code can only contain the letters a-z, A-Z, digits, and underscores, and can't start with a digit.",
                    )
                ],
                editable=False,
                max_length=128,
                separator='_',
                blank=True,
                help_text='Code used for looking up this event programmatically',
                unique=True,
                verbose_name='Code',
            ),
            preserve_default=True,
        ),
    ]
