# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-08-06 17:02
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations
import oscar.models.fields.autoslugfield


class Migration(migrations.Migration):

    dependencies = [
        ('customer', '0005_auto_20220806_1455'),
    ]

    operations = [
        migrations.AlterField(
            model_name='communicationeventtype',
            name='code',
            field=oscar.models.fields.autoslugfield.AutoSlugField(blank=True, editable=False, help_text='Code used for looking up this event programmatically', max_length=128, populate_from='name', separator='_', unique=True, validators=[django.core.validators.RegexValidator(message="Code can only contain the letters a-z, A-Z, digits, and underscores, and can't start with a digit.", regex='^[a-zA-Z_][0-9a-zA-Z_]*$')], verbose_name='Code'),
        ),
    ]
