from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.utils.translation import gettext_lazy as _

from oscar.apps.basket.admin import BasketAdmin

from .models import Basket, Line


class LineInline(admin.TabularInline):
    model = Line
    readonly_fields = (
        'line_reference',
        'product',
        'price_excl_tax',
        'price_incl_tax',
        'price_currency',
        'stockrecord',
    )


class BasketAdminForm(forms.ModelForm):
    class Meta:
        model = Basket
        fields = "__all__"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance:
            self.fields['order_shipping_address'].queryset = self.fields['order_shipping_address'].queryset.filter(
                id=self.instance.order_shipping_address_id
            )
            self.fields['order_billing_address'].queryset = self.fields['order_billing_address'].queryset.filter(
                id=self.instance.order_billing_address_id
            )


def place_order(modeladmin, request, queryset):
    count = 0
    for basket in queryset.filter(status=Basket.FROZEN):
        basket.place_order_from_frozen_basket()
        count += 1

    if count:
        messages.success(request, 'Selected frozen baskets converted to the orders')


place_order.short_description = _('Place order from frozen basket')


class CustomBasketAdmin(admin.ModelAdmin):
    def get_email(self, obj):
        if obj.owner:
            return obj.owner.email
        return obj.order_guest_email
    get_email.short_description = _('Email')

    list_display = (
        'id',
        'get_email',
        'owner',
        'branch',
        'status',
        'num_lines',
        'date_created',
        'date_frozen',
        'date_submitted',
        'order_shipping_address',
    )
    list_filter = ['status', 'branch']
    readonly_fields = ('owner', 'date_merged', 'date_submitted')
    search_fields = ['id', 'order_guest_email', 'owner__email']
    form = BasketAdminForm
    inlines = [LineInline]
    actions = [place_order]


try:
    admin.site.unregister(Basket)
except:
    pass
admin.site.register(Basket, CustomBasketAdmin)

import oscar.apps.basket.admin
