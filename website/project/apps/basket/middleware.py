from oscar.apps.basket.middleware import BasketMiddleware as OscarBasketMiddleware
from oscar.core.loading import get_model

Basket = get_model('basket', 'basket')


class BasketMiddleware(OscarBasketMiddleware):
    def get_basket(self, request):
        basket = super().get_basket(request)

        # take user currency from session or use default one as fallback
        user_currency = request.session.get('user_currency', None)
        if user_currency:
            basket.user_currency = user_currency['currency']
        else:
            basket.user_currency = basket.get_default_user_currency()['currency']

        # Cache basket instance for the during of this request
        request._basket_cache = basket

        return basket
