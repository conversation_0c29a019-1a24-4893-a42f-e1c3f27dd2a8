from django.urls import path


from oscar.apps.basket import apps
from oscar.core.loading import get_class


class BasketConfig(apps.BasketConfig):
    name = 'project.apps.basket'

    def ready(self):
        super().ready()
        self.add_view = get_class('basket.views', 'BasketAddView')
        self.summary_view = get_class('basket.views', 'BasketView')
        self.change_shipping_view = get_class('basket.views', 'ChangeShippingView')
        self.add_notes_view = get_class('basket.views', 'BasketAddNotesView')
        self.save_for_later_view = get_class('basket.views', 'SaveForLaterView')

    def get_urls(self):
        urls = super().get_urls()
        urls += [
            path('shipping/change/', self.change_shipping_view.as_view(), name='shipping-change'),
            path('notes/add/', self.add_notes_view.as_view(), name='add_notes'),
            path('save-for-later/', self.save_for_later_view.as_view(), name='save-for-later'),
        ]
        return self.post_process_urls(urls)
