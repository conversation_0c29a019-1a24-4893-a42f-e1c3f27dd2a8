from django import forms
from django.apps import apps
from django.utils.encoding import force_str
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from ipware import get_client_ip
from oscar.apps.basket import forms as basket_forms

from project.utils import get_country_code_from_ip_address

Basket = apps.get_model('basket', 'Basket')
Country = apps.get_model('address', 'Country')


class CustomRadioSelect(forms.RadioSelect):
    template_name = 'oscar/basket/forms/widgets/shipping_form_radio.html'
    option_template_name = 'oscar/basket/forms/widgets/shipping_form_radio_option.html'


class ShippingForm(forms.Form):
    country = forms.ModelChoiceField(
        label=_('Shipping country'),
        empty_label=_('Choose your country'),
        required=False,
        queryset=Country.objects.filter(is_shipping_country=True).all(),
    )
    shipping_method = forms.ChoiceField(
        label=_('Shipping method'), choices=[], widget=CustomRadioSelect, required=False
    )

    def __init__(self, *args, **kwargs):
        if 'request' in kwargs:
            self.request = kwargs['request']
            del kwargs['request']
        else:
            self.request = None

        if 'basket' in kwargs:
            self.basket = kwargs['basket']
            del kwargs['basket']
        else:
            self.basket = None

        super(ShippingForm, self).__init__(*args, **kwargs)

        if self.request:
            shipping_choices = Basket.SHIPPING_CHOICES

            if self.request.branch.branch == 'eu':
                client_ip, is_routable = get_client_ip(self.request)
                country_code = get_country_code_from_ip_address(client_ip)
            else:
                country_code = self.request.branch.country

            self.fields['shipping_method'].choices = shipping_choices
            self.fields['shipping_method'].initial = Basket.SHIPPING_ECONOMY
            self.fields['country'].initial = country_code

            if self.basket:
                if self.basket.shipping_method:
                    self.fields['shipping_method'].initial = self.basket.shipping_method
                if self.basket.country_id:
                    self.fields['country'].initial = self.basket.country_id


class BasketLineForm(basket_forms.BasketLineForm):
    def __init__(self, strategy, *args, **kwargs):
        super(BasketLineForm, self).__init__(strategy, *args, **kwargs)
        self.fields['quantity'].widget.attrs['class'] = 'qty'
