from oscar.apps.basket.utils import BasketMessageGenerator as CoreBasketMessageGenerator


class BasketMessageGenerator(CoreBasketMessageGenerator):
    def get_messages(self, basket, offers_before, offers_after, include_buttons=True):
        messages = []
        messages.extend(self.get_offer_messages(offers_before, offers_after))
        # messages.extend(self.get_new_total_messages(basket, include_buttons))
        return messages
