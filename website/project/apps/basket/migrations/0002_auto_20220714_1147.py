# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
from django.conf import settings
import oscar.core.utils


class Migration(migrations.Migration):

    dependencies = [
        ('voucher', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('partner', '0002_auto_20220713_1737'),
        ('catalogue', '0001_initial'),
        ('order', '0002_auto_20220713_1737'),
        ('address', '0002_auto_20220713_1735'),
        ('basket', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='basket',
            name='branch',
            field=models.CharField(default=b'eu', max_length=10, editable=False, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='country',
            field=models.ForeignKey(
                default=None, editable=False, to='address.Country', null=True, on_delete=models.PROTECT
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='notes',
            field=models.TextField(default=b''),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_billing_address',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                verbose_name='Billing Address',
                blank=True,
                to='order.BillingAddress',
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_guest_email',
            field=models.EmailField(max_length=75, null=True, verbose_name='Guest email address', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_payment_method',
            field=models.CharField(default=b'', max_length=128, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_shipping_address',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.SET_NULL,
                verbose_name='Shipping Address',
                blank=True,
                to='order.ShippingAddress',
                null=True,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_shipping_code',
            field=models.CharField(default=b'', max_length=128, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_shipping_excl_tax',
            field=models.DecimalField(
                default=0, verbose_name='Shipping charge (excl. tax)', max_digits=12, decimal_places=2
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_shipping_incl_tax',
            field=models.DecimalField(
                default=0, verbose_name='Shipping charge (inc. tax)', max_digits=12, decimal_places=2
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_shipping_method',
            field=models.CharField(max_length=128, null=True, verbose_name='Shipping method', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_total_excl_tax',
            field=models.DecimalField(
                default=0, verbose_name='Order total (excl. tax)', max_digits=12, decimal_places=2
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='order_total_incl_tax',
            field=models.DecimalField(
                default=0, verbose_name='Order total (inc. tax)', max_digits=12, decimal_places=2
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='owner',
            field=models.ForeignKey(
                related_name='baskets',
                verbose_name='Owner',
                to=settings.AUTH_USER_MODEL,
                null=True,
                on_delete=models.PROTECT,
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='shipping_method',
            field=models.CharField(
                default=b'economy',
                max_length=50,
                editable=False,
                choices=[
                    (b'economy', 'Economy shipping (3-5 work days)'),
                    (b'express', 'Express shipping (1-3 work days)'),
                ],
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='user_currency',
            field=models.CharField(default=b'EUR', max_length=3, editable=False),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='basket',
            name='vouchers',
            field=models.ManyToManyField(to='voucher.Voucher', verbose_name='Vouchers', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='line',
            name='basket',
            field=models.ForeignKey(
                related_name='lines', default=1, verbose_name='Basket', to='basket.Basket', on_delete=models.CASCADE
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='line',
            name='product',
            field=models.ForeignKey(
                related_name='basket_lines',
                default=1,
                verbose_name='Product',
                to='catalogue.Product',
                on_delete=models.CASCADE,
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='line',
            name='stockrecord',
            field=models.ForeignKey(
                related_name='basket_lines', default=1, to='partner.StockRecord', on_delete=models.CASCADE
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='lineattribute',
            name='option',
            field=models.ForeignKey(default=1, verbose_name='Option', to='catalogue.Option', on_delete=models.CASCADE),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='line',
            name='price_currency',
            field=models.CharField(
                default=oscar.core.utils.get_default_currency, max_length=12, verbose_name='Currency'
            ),
            preserve_default=True,
        ),
        migrations.AlterUniqueTogether(
            name='line',
            unique_together=set([('basket', 'line_reference')]),
        ),
    ]
