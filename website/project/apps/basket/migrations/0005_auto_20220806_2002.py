# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-08-06 17:02
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('basket', '0004_auto_20220802_1648'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basket',
            name='branch',
            field=models.CharField(blank=True, default='eu', editable=False, max_length=10),
        ),
        migrations.AlterField(
            model_name='basket',
            name='notes',
            field=models.TextField(default=''),
        ),
        migrations.AlterField(
            model_name='basket',
            name='order_payment_method',
            field=models.CharField(blank=True, default='', max_length=128),
        ),
        migrations.AlterField(
            model_name='basket',
            name='order_shipping_code',
            field=models.CharField(blank=True, default='', max_length=128),
        ),
        migrations.AlterField(
            model_name='basket',
            name='shipping_method',
            field=models.CharField(choices=[('fixed-price-economy', 'Economy shipping (3-5 work days)'), ('fixed-price-express', 'Express shipping (1-3 work days)')], default='fixed-price-economy', editable=False, max_length=50),
        ),
        migrations.AlterField(
            model_name='basket',
            name='user_currency',
            field=models.CharField(default='EUR', editable=False, max_length=3),
        ),
    ]
