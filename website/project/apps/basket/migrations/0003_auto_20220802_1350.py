# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('basket', '0002_auto_20220714_1147'),
    ]

    operations = [
        migrations.AlterField(
            model_name='basket',
            name='order_guest_email',
            field=models.EmailField(max_length=254, null=True, verbose_name='Guest email address', blank=True),
        ),
        migrations.AlterField(
            model_name='basket',
            name='shipping_method',
            field=models.CharField(default=b'fixed-price-economy', max_length=50, editable=False, choices=[(b'fixed-price-economy', 'Economy shipping (3-5 work days)'), (b'fixed-price-express', 'Express shipping (1-3 work days)')]),
        ),
    ]
