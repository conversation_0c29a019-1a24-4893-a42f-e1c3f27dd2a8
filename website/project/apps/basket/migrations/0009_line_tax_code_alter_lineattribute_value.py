# Generated by Django 4.2.9 on 2024-06-21 15:47

import django.core.serializers.json
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('basket', '0008_line_date_updated'),
    ]

    operations = [
        migrations.AddField(
            model_name='line',
            name='tax_code',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='VAT rate code'),
        ),
        migrations.AlterField(
            model_name='lineattribute',
            name='value',
            field=models.J<PERSON><PERSON>ield(encoder=django.core.serializers.json.DjangoJSONEncoder, verbose_name='Value'),
        ),
    ]
