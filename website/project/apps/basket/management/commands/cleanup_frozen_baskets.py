from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from oscar.core.loading import get_model
import logging

logger = logging.getLogger('info')
Basket = get_model('basket', 'Basket')

class Command(BaseCommand):

    def handle(self, *args, **options):
        self.stdout.write('Starting frozen baskets cleanup...')
        
        # Find frozen baskets older than specified time periods
        now = timezone.now()
        one_hour_ago = now - timezone.timedelta(hours=1)
        six_hours_ago = now - timezone.timedelta(hours=6)
        
        # Handle baskets with no payment method (abandoned during checkout)
        abandoned_baskets = Basket.objects.filter(
            status=Basket.FROZEN,
            date_created__lt=one_hour_ago,
            order_payment_method=''
        )
        
        # Handle baskets with failed/incomplete payments that are older
        failed_payment_baskets = Basket.objects.filter(
            status=Basket.FROZEN,
            date_created__lt=six_hours_ago,
            order_payment_method__isnull=False
        ).exclude(
            order_payment_method=''
        )
        
        count = 0
        for basket in abandoned_baskets:
            logger.info(
                f"Cleaning up abandoned frozen basket {basket.id} (created: {basket.date_created}, "
                f"owner: {basket.owner.email if basket.owner else 'Anonymous'})"
            )
            basket.status = Basket.OPEN
            basket.order_payment_method = ''  # Clear any payment method
            basket.save()
            count += 1
            
        for basket in failed_payment_baskets:
            logger.info(
                f"Cleaning up failed payment basket {basket.id} (created: {basket.date_created}, "
                f"payment method: {basket.order_payment_method}, "
                f"owner: {basket.owner.email if basket.owner else 'Anonymous'})"
            )
            basket.status = Basket.OPEN
            basket.order_payment_method = ''  # Clear failed payment method
            basket.save()
            count += 1
            
        if count > 0:
            logger.info(f"Cleaned up {count} frozen baskets")