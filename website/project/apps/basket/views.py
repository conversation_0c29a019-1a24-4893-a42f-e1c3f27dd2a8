import logging
from urllib.parse import urlparse

from django.contrib import messages
from django.urls import reverse, resolve
from django.http import HttpResponse, HttpResponseRedirect, Http404, JsonResponse
from django.template import loader
from django.utils.translation import gettext_lazy as _
from django.views.generic import View, FormView
from django.apps import apps
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_POST

from oscar.apps.basket import views
from oscar.core.loading import get_class, get_classes, get_model

from project.apps.catalogue.models import Product
from project.apps.shipping.repository import Repository

(BasketLineForm, ShippingForm,) = get_classes(
    'basket.forms',
    (
        'BasketLineForm',
        'ShippingForm',
    ),
)
BasketMessageGenerator = get_class('basket.utils', 'BasketMessageGenerator')
Line = get_model('basket', 'line')
Basket = get_model('basket', 'basket')

logger = logging.getLogger('apps.basket')


class BasketView(views.BasketView):
    form_class = BasketLineForm

    def get_context_data(self, **kwargs):
        context = super(BasketView, self).get_context_data(**kwargs)
        context['shipping_form'] = ShippingForm(request=self.request, basket=self.request.basket)

        # Get current country from basket or session
        country = self.request.basket.country
        if not country:
            Country = apps.get_model('address', 'Country')
            try:
                country = Country.objects.get(iso_3166_1_a2=self.request.country_code)
            except Country.DoesNotExist:
                country = Country.objects.get(iso_3166_1_a2='LT')

        # Check shipping availability for each line
        for line in context['formset']:
            shipping_preform = line.instance.product.get_shipping_preform(country)
            line.instance.shipping_available = shipping_preform is not None
            line.instance.shipping_country = country

        return context


class BasketAddView(views.BasketAddView):
    def form_valid(self, form):
        offers_before = self.request.basket.applied_offers()

        if not self.request.basket.is_vendor_valid(form.product.owner):
            messages.error(self.request, _('All basket products must have same vendor'))
            return self.form_invalid(form)

        if form.product.is_full_car():
            messages.error(self.request, _('This product is not allowed to add to the basket'))
            return self.form_invalid(form)

        self.request.basket.add_product(form.product, form.cleaned_data['quantity'], form.cleaned_options())

        messages.success(self.request, self.get_success_message(form), extra_tags='safe noicon')

        # Check for additional offer messages
        BasketMessageGenerator().apply_messages(self.request, offers_before)

        # Send signal for basket addition
        self.add_signal.send(sender=self, product=form.product, user=self.request.user, request=self.request)

        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        current_qty = self.request.basket.product_quantity(form.product)
        desired_qty = current_qty + form.cleaned_data.get('quantity', 1)
        result = self.request.strategy.fetch_for_product(form.product)
        is_permitted, reason = result.availability.is_purchase_permitted(desired_qty)
        if current_qty == 1 and not is_permitted:
            return HttpResponseRedirect(reverse('basket:summary'))
        else:
            msgs = []
            for error in list(form.errors.values()):
                msgs.append(error.as_text())
            messages.error(self.request, ",".join(msgs))
            return HttpResponseRedirect(self.request.META.get('HTTP_REFERER', reverse('basket:summary')))

    def get_success_url(self):
        #        url = None
        #        if self.request.POST.get('next'):
        #            url = self.request.POST.get('next')
        #        elif 'HTTP_REFERER' in self.request.META:
        #            url = self.request.META['HTTP_REFERER']
        #        if url:
        #            # We only allow internal URLs so we see if the url resolves
        #            try:
        #                resolve(urlparse(url).path)
        #            except Http404:
        #                url = None
        #            url = '%s#quick-basket' % url
        #        if url is None:
        #            url = reverse('basket:summary')
        url = reverse('basket:summary')
        return url


class BasketAddNotesView(View):
    def post(self, request, *args, **kwargs):
        notes = request.POST.get('notes', '')
        request.basket.notes = notes
        request.basket.save()
        return HttpResponse('ok')


class ChangeShippingView(FormView):
    form_class = ShippingForm

    def get(self, request, *args, **kwargs):
        return HttpResponseRedirect(reverse('basket:summary'))

    def get_form_kwargs(self):
        kwargs = super(ChangeShippingView, self).get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs

    def form_valid(self, form):
        country = form.cleaned_data['country']
        shipping_method = form.cleaned_data['shipping_method']

        if self.request.POST.get('product', None):
            product_id = self.request.POST.get('product')
            try:
                product = Product.objects.get(id=product_id)
            except Product.DoesNotExist:
                product = None
            if product:
                shipping_price = product.get_shipping_price(self.request.basket.__class__, shipping_method, country)
                try:
                    shipping_delay = product.get_shipping_delay(country.iso_3166_1_a2)
                except AttributeError:
                    shipping_delay = None
                #                logger.info(
                #                    'CHANGE_SHIPPING_VIEW: Changed shipping country to %s, '
                #                    'shipping charge %s, shipping delay %s',
                #                    country.iso_3166_1_a2, shipping_price, shipping_delay
                #                )
                template = loader.get_template('oscar/catalogue/partials/product_shipping_price.html')
                context = {'shipping_price': shipping_price, 'shipping_delay': shipping_delay}
                return HttpResponse(template.render(context, self.request))
            else:
                return HttpResponse('0')
        else:
            if not self.request.basket.id:
                return HttpResponseRedirect(self.request.META.get('HTTP_REFERER', reverse('basket:summary')))
            if shipping_method == self.request.basket.SHIPPING_LOCAL_PICK_UP:
                country = None
            self.request.basket.country = country
            self.request.basket.shipping_method = shipping_method
            self.request.basket.save()
            return HttpResponseRedirect(self.request.META.get('HTTP_REFERER', reverse('basket:summary')))

    def form_invalid(self, form):
        if self.request.POST.get('product'):
            return HttpResponse('0')
        else:
            return HttpResponseRedirect(reverse('basket:summary'))


class SaveForLaterView(View):
    """
    View to handle saving a product directly to the user's saved items list
    without adding it to the basket first.
    """
    @method_decorator(login_required)
    @method_decorator(require_POST)
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        product_id = request.POST.get('product_id')

        try:
            product = Product.objects.get(id=product_id)
        except (Product.DoesNotExist, ValueError):
            return JsonResponse({
                'success': False,
                'already_saved': False
            }, status=500)

        # Get or create user's saved basket
        try:
            saved_basket = Basket.saved.get_or_create(owner=request.user)[0]
        except Basket.MultipleObjectsReturned:
            # If there are multiple saved baskets, use the first one
            saved_basket = Basket.saved.filter(owner=request.user).first()

        # Check if product is already in the saved basket
        if saved_basket.lines.filter(product=product).exists():
            return JsonResponse({
                'success': True,
                'already_saved': True
            })

        # Make sure the basket has a strategy assigned
        from oscar.core.loading import get_class

        Applicator = get_class('offer.applicator', 'Applicator')
        Selector = get_class('partner.strategy', 'Selector')

        # Assign strategy to basket
        saved_basket.strategy = Selector().strategy(request)

        try:
            # Add product to the saved basket
            saved_basket.add_product(product, quantity=1)

            # Apply offers
            Applicator().apply(saved_basket, request.user, request)

            return JsonResponse({
                'success': True,
                'already_saved': False
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'already_saved': False,
                'message': str(e)
            }, status=500)
