import datetime

from django import forms
from django.contrib.auth.models import User
from django.utils.translation import gettext as _

from project.apps.pap.models import Organization
from project.apps.catalogue.models import ProductDescriptionTemplate


class ProductDescriptionTemplateForm(forms.ModelForm):
    class Meta:
        model = ProductDescriptionTemplate
        fields = "__all__"


class ProductSearchForm(forms.Form):
    upc = forms.CharField(max_length=64, required=False, label=_('UPC'))
    title = forms.CharField(max_length=255, required=False, label=_('Product title'))
    date_created_from = forms.DateField(label=_('Date created from'), required=False)
    date_created_to = forms.DateField(label=_('Date created to'), required=False)
    manager = forms.ModelChoiceField(label=_('Manager'), required=False, queryset=User.objects.none())

    def __init__(self, *args, **kwargs):
        self.user = kwargs['user']
        del kwargs['user']
        super().__init__(*args, **kwargs)
        organization = Organization.get_user_organization(self.user)
        self.fields['manager'].queryset = organization.users.all()

    def clean(self):
        cleaned_data = super().clean()
        cleaned_data['upc'] = cleaned_data['upc'].strip()
        cleaned_data['title'] = cleaned_data['title'].strip()
        return cleaned_data
