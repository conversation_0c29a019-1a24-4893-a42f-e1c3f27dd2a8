import datetime

from django.urls import reverse
from django.contrib import messages
from django.db.models import Q, F, Count
from django.http import HttpResponseRedirect, Http404
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views import generic
from django.views.generic import TemplateView

from oscar.apps.dashboard.catalogue import views
from oscar.core.utils import slugify
from oscar.core.loading import get_model
from oscar.views import sort_queryset
from oscar.views.generic import ObjectLookupView

from .forms import ProductDescriptionTemplateForm, ProductSearchForm
from .tables import ProductTable
from project.accounts.models import Account
from project.apps.catalogue.models import Product, ProductDescriptionTemplate


class ProductDescriptionTemplateListView(generic.ListView):
    model = ProductDescriptionTemplate
    context_object_name = "templates"
    template_name = "oscar/dashboard/catalogue/product_description_template_list.html"

    def get_queryset(self):
        queryset = super(ProductDescriptionTemplateListView, self).get_queryset()
        owner, subowner = Account.get_owner(self.request.user)
        queryset = queryset.filter(owner=owner)
        return queryset


class ProductDescriptionTemplateCreateView(generic.CreateView):
    template_name = "oscar/dashboard/catalogue/product_description_template_form.html"
    model = ProductDescriptionTemplate
    form_class = ProductDescriptionTemplateForm

    def get_context_data(self, **kwargs):
        ctx = super(ProductDescriptionTemplateCreateView, self).get_context_data(
            **kwargs
        )
        ctx["title"] = _("Add a new product description template")
        return ctx

    def form_valid(self, form):
        owner, subowner = Account.get_owner(self.request.user)
        obj = form.save(commit=False)
        obj.owner = owner
        obj.save()
        return super(ProductDescriptionTemplateCreateView, self).form_valid(form)

    def get_success_url(self):
        messages.info(
            self.request, _("Product description template created successfully")
        )
        return reverse("dashboard:catalogue-product-description-template-list")


class ProductDescriptionTemplateUpdateView(generic.UpdateView):
    template_name = "oscar/dashboard/catalogue/product_description_template_form.html"
    model = ProductDescriptionTemplate
    form_class = ProductDescriptionTemplateForm

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ProductDescriptionTemplateUpdateView, self).dispatch(
            request, *args, **kwargs
        )

    def get_context_data(self, **kwargs):
        ctx = super(ProductDescriptionTemplateUpdateView, self).get_context_data(
            **kwargs
        )
        ctx["title"] = _("Update product description template '%s'") % self.object.name
        return ctx

    def get_success_url(self):
        messages.info(
            self.request, _("Product description template updated successfully")
        )
        return reverse("dashboard:catalogue-product-description-template-list")


class ProductDescriptionTemplateDeleteView(generic.DeleteView):
    template_name = "oscar/dashboard/catalogue/product_description_template_delete.html"
    model = ProductDescriptionTemplate

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ProductDescriptionTemplateDeleteView, self).dispatch(
            request, *args, **kwargs
        )

    def get_success_url(self):
        messages.info(
            self.request, _("Product description template deleted successfully")
        )
        return reverse("dashboard:catalogue-product-description-template-list")


class ProductListView(views.ProductListView):
    form_class = ProductSearchForm
    table_class = ProductTable

    def dispatch(self, request, *args, **kwargs):
        if not request.GET:
            return HttpResponseRedirect(
                f"{reverse('dashboard:catalogue-product-list')}?date_created_from={timezone.now().date()}&date_created_to={timezone.now().date()}"
            )
        return super().dispatch(request, *args, **kwargs)

    def get_description(self, form):
        if form.is_valid() and any(form.cleaned_data.values()):
            return _("Product search results %s") % self.get_queryset().count()
        return _("Products")

    def get_table_pagination(self, table):
        return dict(per_page=200)

    def filter_queryset(self, queryset):
        from project.apps.pap.models import Organization

        organization = Organization.get_user_organization(self.request.user)
        qs = queryset.filter(subowner__in=organization.users.all())

        return qs

    def apply_search(self, queryset):
        """
        Search through the filtered queryset.

        We must make sure that we don't return search results that the user is not allowed
        to see (see filter_queryset).
        """
        self.form = self.form_class(self.request.GET, user=self.request.user)

        if not self.form.is_valid():
            return queryset

        data = self.form.cleaned_data

        upc = data.get("upc")
        if upc:
            # Filter the queryset by upc
            # For usability reasons, we first look at exact matches and only return
            # them if there are any. Otherwise we return all results
            # that contain the UPC.

            # Look up all matches (child products, products not allowed to access) ...
            matches_upc = Product.objects.filter(
                Q(upc__iexact=upc) | Q(children__upc__iexact=upc)
            )

            # ... and use that to pick all standalone or parent products that the user is
            # allowed to access.
            qs_match = queryset.filter(
                Q(id__in=matches_upc.values("id"))
                | Q(id__in=matches_upc.values("parent_id"))
            )

            if qs_match.exists():
                # If there's a direct UPC match, return just that.
                queryset = qs_match
            else:
                # No direct UPC match. Let's try the same with an icontains search.
                matches_upc = Product.objects.filter(
                    Q(upc__icontains=upc) | Q(children__upc__icontains=upc)
                )
                queryset = queryset.filter(
                    Q(id__in=matches_upc.values("id"))
                    | Q(id__in=matches_upc.values("parent_id"))
                )

        title = data.get("title")
        if title:
            queryset = queryset.filter(
                Q(title__icontains=title) | Q(children__title__icontains=title)
            )

        date_created_from = data.get("date_created_from")
        if date_created_from:
            queryset = queryset.filter(
                date_created__gte=datetime.datetime.combine(
                    date_created_from, datetime.time(0, 0, 0)
                )
            )

        date_created_to = data.get("date_created_to")
        if date_created_to:
            queryset = queryset.filter(
                date_created__lte=datetime.datetime.combine(
                    date_created_to, datetime.time(23, 59, 59)
                )
            )

        manager = data.get("manager")
        if manager:
            queryset = queryset.filter(subowner=manager)

        return queryset.distinct()

    # def get_context_data(self, **kwargs):
    #     context = super().get_context_data(**kwargs)
    #     return context


class ProductLookupView(ObjectLookupView):
    model = Product

    def get_queryset(self):
        owner, subowner = Account.get_owner(self.request.user)
        qs = self.model.objects.browsable().filter(owner=owner)
        if subowner:
            qs = qs.filter(subowner=subowner)
        return qs

    def lookup_filter(self, qs, term):
        return qs.filter(Q(title__icontains=term) | Q(parent__title__icontains=term))
    
Category = get_model('catalogue', 'Category')
Product = get_model('catalogue', 'Product')

class CategoryListView(TemplateView):
    template_name = 'oscar/dashboard/catalogue/category_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = _('Categories')
        
        categories = Category.objects.annotate(
            product_count=Count('product', distinct=True)
        ).order_by('path').annotate(
            level=F('depth') - 1,
            name_with_depth=F('name')
        )
        
        context['categories'] = categories
        return context