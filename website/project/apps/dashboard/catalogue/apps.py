from django.urls import path

from oscar.apps.dashboard.catalogue import apps
from oscar.core.loading import get_class


class CatalogueDashboardConfig(apps.CatalogueDashboardConfig):
    name = 'project.apps.dashboard.catalogue'

    def ready(self):
        super().ready()

        self.product_list_view = get_class('dashboard.catalogue.views', 'ProductListView')
        self.product_lookup_view = get_class('dashboard.catalogue.views', 'ProductLookupView')
        self.product_description_template_list_view = get_class(
            'dashboard.catalogue.views', 'ProductDescriptionTemplateListView'
        )
        self.product_description_template_create_view = get_class(
            'dashboard.catalogue.views', 'ProductDescriptionTemplateCreateView'
        )
        self.product_description_template_update_view = get_class(
            'dashboard.catalogue.views', 'ProductDescriptionTemplateUpdateView'
        )
        self.product_description_template_delete_view = get_class(
            'dashboard.catalogue.views', 'ProductDescriptionTemplateDeleteView'
        )
        self.category_list_view = get_class('dashboard.catalogue.views', 'CategoryListView')

    def get_urls(self):
        urls = super().get_urls()
        urls += [
            path(
                'product-description-templates/',
                self.product_description_template_list_view.as_view(),
                name='catalogue-product-description-template-list',
            ),
            path(
                'product-description-templates/create/',
                self.product_description_template_create_view.as_view(),
                name='catalogue-product-description-template-create',
            ),
            path(
                'product-description-templates/<int:pk>/update/',
                self.product_description_template_update_view.as_view(),
                name='catalogue-product-description-template-update',
            ),
            path(
                'product-description-template/<int:pk>/delete/',
                self.product_description_template_delete_view.as_view(),
                name='catalogue-product-description-template-delete',
            ),
            path(
                'categories/',
                self.category_list_view.as_view(),
                name='catalogue-category-list',
            ),
        ]
        return self.post_process_urls(urls)
