from django.urls import path, re_path
from django.utils.translation import gettext_lazy as _

from oscar.core.application import OscarDashboardConfig
from oscar.core.loading import get_class


class PromotionsDashboardConfig(OscarDashboardConfig):
    label = 'promotions_dashboard'
    name = 'project.apps.dashboard.promotions'
    verbose_name = _('Promotions dashboard')

    default_permissions = [
        'is_staff',
    ]

    view_names = (
        ('create_%s_view', 'Create%sView'),
        ('update_%s_view', 'Update%sView'),
        ('delete_%s_view', 'Delete%sView'),
    )

    def get_promotion_classes(self):
        from project.apps.promotions.conf import PROMOTION_CLASSES

        return PROMOTION_CLASSES

    def ready(self):
        self.list_view = get_class('dashboard.promotions.views', 'ListView')
        self.page_list = get_class('dashboard.promotions.views', 'PageListView')
        self.page_detail = get_class('dashboard.promotions.views', 'PageDetailView')
        self.create_redirect_view = get_class('dashboard.promotions.views', 'CreateRedirectView')
        self.delete_page_promotion_view = get_class('dashboard.promotions.views', 'DeletePagePromotionView')

        for klass in self.get_promotion_classes():
            for attr_name, view_name in self.view_names:
                full_attr_name = attr_name % klass.classname()
                full_view_name = view_name % klass.__name__
                view = get_class('dashboard.promotions.views', full_view_name)
                locals()[full_attr_name] = view

    def get_urls(self):
        urls = [
            path('', self.list_view.as_view(), name='promotion-list'),
            path('pages/', self.page_list.as_view(), name='promotion-list-by-page'),
            re_path(
                r'^page/(?P<path>/([\w-]+(/[\w-]+)*/)?)$', self.page_detail.as_view(), name='promotion-list-by-url'
            ),
            path('create/', self.create_redirect_view.as_view(), name='promotion-create-redirect'),
            path(
                'page-promotion/<int:pk>/$',
                self.delete_page_promotion_view.as_view(),
                name='pagepromotion-delete',
            ),
        ]

        for klass in self.get_promotion_classes():
            code = klass.classname()
            urls += [
                path(
                    'create/%s/' % code,
                    getattr(self, 'create_%s_view' % code).as_view(),
                    name='promotion-create-%s' % code,
                ),
                path(
                    'update/<ptype>/<int:pk>/',
                    getattr(self, 'update_%s_view' % code).as_view(),
                    name='promotion-update',
                ),
                path(
                    'delete/<ptype>/<int:pk>/',
                    getattr(self, 'delete_%s_view' % code).as_view(),
                    name='promotion-delete',
                ),
            ]

        return self.post_process_urls(urls)
