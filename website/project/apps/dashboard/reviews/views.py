from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from oscar.apps.dashboard.reviews import views
from oscar.views import sort_queryset

from project.apps.catalogue.reviews.models import OrderReview
from project.apps.dashboard.reviews import forms


class ReviewListView(views.ReviewListView):
    model = OrderReview
    form_class = forms.OrderReviewSearchForm
    review_form_class = forms.DashboardOrderReviewForm

    def get(self, request, *args, **kwargs):
        response = super(views.ReviewListView, self).get(request, **kwargs)
        self.form = self.form_class()
        return response

    def get_date_from_to_queryset(self, date_from, date_to, queryset=None):
        queryset = self.model.objects.filter(order__owner=self.request.user)
        return super(ReviewListView, self)\
                   .get_date_from_to_queryset(date_from, date_to, queryset=queryset)

    def get_queryset(self):
        queryset = self.model.objects.filter(order__owner=self.request.user)
        queryset = sort_queryset(queryset, self.request,
                                 ['score', 'total_votes', 'date_created'])
        self.desc_ctx = {
            'main_filter': _('All reviews'),
            'date_filter': '',
            'status_filter': '',
            'kw_filter': '',
            'name_filter': '',
        }

        self.form = self.form_class(self.request.GET)
        if not self.form.is_valid():
            return queryset

        data = self.form.cleaned_data

        # checking for empty string rather then True is required
        # as zero is a valid value for 'status' but would be
        # evaluated to False
        if data['status'] != '':
            queryset = queryset.filter(status=data['status']).distinct()
            display_status = self.form.get_friendly_status()
            self.desc_ctx['status_filter'] \
                = _(" with status matching '%s'") % display_status

        if data['keyword']:
            queryset = queryset.filter(
                Q(title__icontains=data['keyword']) |
                Q(body__icontains=data['keyword'])
            ).distinct()
            self.desc_ctx['kw_filter'] \
                = _(" with keyword matching '%s'") % data['keyword']

        queryset = self.get_date_from_to_queryset(data['date_from'],
                                                  data['date_to'], queryset)

        if data['name']:
            # If the value is two words, then assume they are first name and
            # last name
            parts = data['name'].split()
            if len(parts) >= 2:
                queryset = queryset.filter(
                    user__first_name__istartswith=parts[0],
                    user__last_name__istartswith=parts[1]
                ).distinct()
            else:
                queryset = queryset.filter(
                    Q(user__first_name__istartswith=parts[0]) |
                    Q(user__last_name__istartswith=parts[-1])
                ).distinct()
            self.desc_ctx['name_filter'] \
                = _(" with customer name matching '%s'") % data['name']

        return queryset

    def get_context_data(self, **kwargs):
        context = super(views.ReviewListView, self).get_context_data(**kwargs)
        context['review_form'] = self.review_form_class()
        context['form'] = self.form
        context['description'] = self.desc_template % self.desc_ctx
        return context


class ReviewUpdateView(views.ReviewUpdateView):
    model = OrderReview
    form_class = forms.DashboardOrderReviewForm


class ReviewDeleteView(views.ReviewDeleteView):
    model = OrderReview

