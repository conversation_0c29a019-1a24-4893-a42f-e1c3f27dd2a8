from oscar.apps.dashboard.reviews import apps
from oscar.core.loading import get_class


class ReviewsDashboardConfig(apps.ReviewsDashboardConfig):
    name = 'project.apps.dashboard.reviews'

    def ready(self):
        super().ready()
        self.list_view = get_class('dashboard.reviews.views', 'ReviewListView')
        self.update_view = get_class('dashboard.reviews.views', 'ReviewUpdateView')
        self.delete_view = get_class('dashboard.reviews.views', 'ReviewDeleteView')
