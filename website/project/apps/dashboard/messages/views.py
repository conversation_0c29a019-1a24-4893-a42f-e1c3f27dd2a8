import csv
import json
import os
import logging
from datetime import datetime
from pathlib import Path

from django.http import HttpResponse
from django.conf import settings
from django.core.cache import cache
from django.views.generic import ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.translation import gettext as _
from django.contrib.auth.decorators import user_passes_test
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.core.paginator import Paginator, EmptyPage
from django.contrib import messages

from project.apps.pap.models import Organization, EbayApiCredentials

logger = logging.getLogger(__name__)

def get_rrr_messages(organization_id=None):
    """Get RRR stock check messages for a specific organization or all messages if no organization specified"""
    messages = []
    
    messages_dir = os.path.join(settings.MEDIA_ROOT, 'messages', 'RRR_stock_check')
    if os.path.exists(messages_dir):
        try:
            with open(os.path.join(messages_dir, 'rrr_stock_check_report.json'), 'r') as f:
                data = json.load(f)
                if organization_id:
                    # Filter for specific organization
                    for org_data in data['organizations']:
                        if str(org_data['id']) == str(organization_id):
                            messages.append({
                                'organization': {
                                    'name': org_data['name'],
                                    'id': org_data['id']
                                },
                                'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                                'environment': data['environment'],
                                'total_parts_checked': len(org_data['active_parts']) + len(org_data['deleted_local']) + len(org_data['deleted_rrr']),
                                'total_active_parts': len(org_data['active_parts']),
                                'active_parts': org_data['active_parts'],
                                'deleted_local': org_data['deleted_local'],
                                'deleted_rrr': org_data['deleted_rrr']
                            })
                else:
                    # Include all organizations
                    for org_data in data['organizations']:
                        messages.append({
                            'organization': {
                                'name': org_data['name'],
                                'id': org_data['id']
                            },
                            'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                            'environment': data['environment'],
                            'total_parts_checked': len(org_data['active_parts']) + len(org_data['deleted_local']) + len(org_data['deleted_rrr']),
                            'total_active_parts': len(org_data['active_parts']),
                            'active_parts': org_data['active_parts'],
                            'deleted_local': org_data['deleted_local'],
                            'deleted_rrr': org_data['deleted_rrr']
                        })
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error reading message file: {e}")
    
    return messages

def get_ebay_messages(organization_id=None):
    """Get eBay stock check messages for a specific organization or all messages if no organization specified"""
    messages = []
    
    messages_dir = os.path.join(settings.MEDIA_ROOT, 'messages', 'ebay_stock_check')
    json_file = os.path.join(messages_dir, 'ebay_stock_check_latest.json')
    
    if not os.path.exists(messages_dir):
        logger.warning("Messages directory does not exist")
        return messages
        
    if not os.path.exists(json_file):
        logger.warning("eBay stock check report file does not exist")
        return messages
        
    try:
        with open(json_file, 'r') as f:
            try:
                data = json.load(f)
                logger.info(f"Loaded JSON data with timestamp: {data.get('timestamp')}")
            except json.JSONDecodeError as e:
                logger.warning(f"Invalid JSON in stock check report: {e}")
                return messages
                
            if not isinstance(data, dict) or 'organizations' not in data:
                logger.warning("Invalid data structure in stock check report")
                return messages

            # Get organizations that own eBay credentials
            # For credentials ID 1, only PAP organization can see it
            # For other credentials, organizations can see them if they have access through ManyToMany
            pap_org = Organization.objects.filter(name="PAP").first()
            organizations_with_credentials = set()
            
            # Add organizations that have access to non-PAP credentials
            organizations_with_credentials.update(
                Organization.objects.filter(
                    ebay_api_credentials__in=EbayApiCredentials.objects.filter(
                        organizations__isnull=False
                    ).exclude(id=1)
                ).values_list('id', flat=True)
            )
            
            # Add PAP organization if it exists (for credentials ID 1)
            if pap_org:
                organizations_with_credentials.add(pap_org.id)
            
            logger.info(f"Organizations with credentials: {organizations_with_credentials}")
                
            if organization_id:
                logger.info(f"Filtering for organization_id: {organization_id}")
                # Filter for specific organization
                for client_data in data['organizations']:
                    for org_data in client_data['organizations']:
                        if str(org_data['id']) == str(organization_id):
                            logger.info(f"Found matching organization: {org_data['name']}")
                            # Check if organization owns any eBay credentials
                            if org_data['id'] in organizations_with_credentials:
                                logger.info(f"Organization {org_data['name']} has eBay credentials")
                                
                                messages.append({
                                    'organization': {
                                        'name': org_data['name'],
                                        'id': org_data['id']
                                    },
                                    'client_name': client_data['client_name'],
                                    'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                                    'environment': data['environment'],
                                    'total_items': client_data['total_items'],
                                    'processed_skus': client_data['processed_skus'],
                                    'mismatched_parts': org_data['mismatched_parts'],  # Use mismatched_parts for HTML view
                                    'skus_not_found': client_data['skus_not_found'],
                                    'advertisements_not_found': client_data['advertisements_not_found'],
                                    'stock_mismatches': len(org_data['mismatched_parts'])
                                })
            else:
                # Process all organizations that own eBay credentials
                for client_data in data['organizations']:
                    logger.info(f"Processing client: {client_data['client_name']}")
                    for org_data in client_data['organizations']:
                        logger.info(f"Checking organization: {org_data['name']} (ID: {org_data['id']})")
                        if org_data['id'] in organizations_with_credentials:
                            logger.info(f"Organization {org_data['name']} has credentials - adding to messages")
                            
                            messages.append({
                                'organization': {
                                    'name': org_data['name'],
                                    'id': org_data['id']
                                },
                                'client_name': client_data['client_name'],
                                'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                                'environment': data['environment'],
                                'total_items': client_data['total_items'],
                                'processed_skus': client_data['processed_skus'],
                                'mismatched_parts': org_data['mismatched_parts'],  # Use mismatched_parts for HTML view
                                'skus_not_found': client_data['skus_not_found'],
                                'advertisements_not_found': client_data['advertisements_not_found'],
                                'stock_mismatches': len(org_data['mismatched_parts'])
                            })

    except IOError as e:
        logger.warning(f"Error reading eBay message file: {e}")
    except Exception as e:
        logger.warning(f"Unexpected error processing eBay messages: {e}")
    
    logger.info(f"Returning {len(messages)} messages")
    return messages

def get_ebay_messages_full(organization_id=None):
    """Get eBay stock check messages from full JSON file for a specific organization or all messages if no organization specified"""
    messages = []
    
    messages_dir = os.path.join(settings.MEDIA_ROOT, 'messages', 'ebay_stock_check')
    json_file = os.path.join(messages_dir, 'ebay_stock_check_latest_full.json')
    
    logger.info(f"Looking for full JSON file at: {json_file}")
    
    if not os.path.exists(messages_dir):
        logger.warning("Messages directory does not exist")
        return messages
        
    if not os.path.exists(json_file):
        logger.warning("eBay stock check full report file does not exist")
        return messages
        
    try:
        with open(json_file, 'r') as f:
            try:
                data = json.load(f)
                logger.info(f"Loaded full JSON data with timestamp: {data.get('timestamp')}")
                logger.info(f"Number of organizations in data: {len(data.get('organizations', []))}")
            except json.JSONDecodeError as e:
                logger.warning(f"Invalid JSON in stock check report: {e}")
                return messages
                
            if not isinstance(data, dict) or 'organizations' not in data:
                logger.warning("Invalid data structure in stock check report")
                return messages

            # Get organizations that own eBay credentials
            # For credentials ID 1, only PAP organization can see it
            # For other credentials, organizations can see them if they have access through ManyToMany
            pap_org = Organization.objects.filter(name="PAP").first()
            organizations_with_credentials = set()
            
            # Add organizations that have access to non-PAP credentials
            organizations_with_credentials.update(
                Organization.objects.filter(
                    ebay_api_credentials__in=EbayApiCredentials.objects.filter(
                        organizations__isnull=False
                    ).exclude(id=1)
                ).values_list('id', flat=True)
            )
            
            # Add PAP organization if it exists (for credentials ID 1)
            if pap_org:
                organizations_with_credentials.add(pap_org.id)
            
            logger.info(f"Organizations with credentials: {organizations_with_credentials}")
                
            if organization_id:
                logger.info(f"Filtering for organization_id: {organization_id}")
                # Filter for specific organization
                for client_data in data['organizations']:
                    logger.info(f"Checking client: {client_data['client_name']}")
                    for org_data in client_data['organizations']:
                        if str(org_data['id']) == str(organization_id):
                            logger.info(f"Found matching organization: {org_data['name']}")
                            # Check if organization owns any eBay credentials
                            if org_data['id'] in organizations_with_credentials:
                                logger.info(f"Organization {org_data['name']} has eBay credentials")
                                # Get all parts for CSV export
                                processed_parts = org_data.get('processed_parts', [])
                                logger.info(f"Found {len(processed_parts)} processed parts")
                                
                                messages.append({
                                    'organization': {
                                        'name': org_data['name'],
                                        'id': org_data['id']
                                    },
                                    'client_name': client_data['client_name'],
                                    'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                                    'environment': data['environment'],
                                    'total_items': client_data['total_items'],
                                    'processed_skus': client_data['processed_skus'],
                                    'processed_parts': processed_parts,  # Use processed_parts for CSV export
                                    'skus_not_found': client_data['skus_not_found'],
                                    'advertisements_not_found': client_data['advertisements_not_found'],
                                    'stock_mismatches': len(org_data.get('mismatched_parts', []))
                                })
            else:
                # Process all organizations that own eBay credentials
                for client_data in data['organizations']:
                    logger.info(f"Processing client: {client_data['client_name']}")
                    for org_data in client_data['organizations']:
                        logger.info(f"Checking organization: {org_data['name']} (ID: {org_data['id']})")
                        if org_data['id'] in organizations_with_credentials:
                            logger.info(f"Organization {org_data['name']} has credentials - adding to messages")
                            # Get all parts for CSV export
                            processed_parts = org_data.get('processed_parts', [])
                            logger.info(f"Found {len(processed_parts)} processed parts")
                            
                            messages.append({
                                'organization': {
                                    'name': org_data['name'],
                                    'id': org_data['id']
                                },
                                'client_name': client_data['client_name'],
                                'timestamp': datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00')),
                                'environment': data['environment'],
                                'total_items': client_data['total_items'],
                                'processed_skus': client_data['processed_skus'],
                                'processed_parts': processed_parts,  # Use processed_parts for CSV export
                                'skus_not_found': client_data['skus_not_found'],
                                'advertisements_not_found': client_data['advertisements_not_found'],
                                'stock_mismatches': len(org_data.get('mismatched_parts', []))
                            })

    except IOError as e:
        logger.warning(f"Error reading eBay message file: {e}", exc_info=True)
    except Exception as e:
        logger.warning(f"Unexpected error processing eBay messages: {e}", exc_info=True)
   
    return messages

def staff_required(user):
    """Check if user is staff or superuser"""
    return user.is_staff or user.is_superuser

@user_passes_test(staff_required)
def export_rrr_stock_check_csv(request):
    """Export RRR stock check results to CSV"""
    organization_id = request.GET.get('organization')
    
    # Get user's organization info
    user_org = request.user.organizations.first()
    is_pap_staff = (user_org and user_org.name == "PAP") or request.user.email == "<EMAIL>"
    is_external_manager = user_org and user_org.name != "PAP"
    
    # Get messages based on user role
    if request.user.is_superuser:
        messages = get_rrr_messages(organization_id)
    elif is_pap_staff:
        # Staff can see PAP organization messages
        pap_org = Organization.objects.filter(name="PAP").first()
        if pap_org:
            messages = get_rrr_messages(pap_org.id)
        else:
            messages = []
    elif is_external_manager:
        # External managers can only see their organization's messages
        if organization_id and str(user_org.id) != organization_id:
            return HttpResponse('Unauthorized', status=403)
        messages = get_rrr_messages(user_org.id)
    else:
        return HttpResponse('Unauthorized', status=403)
    
    if not messages:
        messages.error(request, "No data available for export")
        return redirect('dashboard:rrr-messages-list')

    # Create the HttpResponse object with CSV header
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    org_suffix = f'_org_{organization_id}' if organization_id else ''
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="rrr_stock_check_{timestamp}{org_suffix}.csv"'
    
    writer = csv.writer(response)
    writer.writerow(['RRR Part ID', 'Vehicle Part ID'])

    # Write active parts for each message
    for message in messages:
        # Get active parts directly from the message
        active_parts = message.get('active_parts', [])

        # Write to CSV
        for part in active_parts:
            writer.writerow([
                part['rrr_part_id'],
                part['vehicle_part_id']
            ])

    return response

@user_passes_test(staff_required)
def export_ebay_stock_check_csv(request):
    """Export eBay stock check results to CSV"""
    organization_id = request.GET.get('organization')
    
    # Get user's organization info and check permissions
    user_org = request.user.organizations.first()
    is_pap_staff = (user_org and user_org.name == "PAP") or request.user.email == "<EMAIL>"
    is_external_manager = user_org and user_org.name != "PAP"
    
    # Get messages based on user role
    if request.user.is_superuser:
        message_list = get_ebay_messages_full(organization_id)
    elif is_pap_staff:
        # Staff can see PAP organization messages
        pap_org = Organization.objects.filter(name="PAP").first()
        if pap_org:
            message_list = get_ebay_messages_full(pap_org.id)
        else:
            message_list = []
    elif is_external_manager:
        # Check if user's organization owns any eBay credentials
        if EbayApiCredentials.objects.filter(organizations=user_org).exists():
            if organization_id and str(user_org.id) != organization_id:
                return HttpResponse('Unauthorized', status=403)
            message_list = get_ebay_messages_full(user_org.id)  # Use full messages for CSV export
        else:
            # User's organization doesn't own any credentials - no access to messages
            return []
    else:
        return HttpResponse('Unauthorized', status=403)
    
    if not message_list:
        messages.error(request, "No data available for export")
        return redirect('messages:ebay-messages-list')

    # Create the HttpResponse object with CSV header
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    org_suffix = f'_org_{organization_id}' if organization_id else ''
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="ebay_stock_check_{timestamp}{org_suffix}.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'Part ID',
        'Primary Market Quantity',
        'Local Quantity',
        'eBay Account',
        'Has Primary Market Mismatch',
        'Additional Markets Count',
        'Additional Markets Details',
        'Critical Issues',
        'Warnings'
    ])

    # Write all parts for each message
    for message in message_list:
        client_name = message.get('client_name', '')
        parts = message.get('processed_parts', [])  # Use all processed parts, not just mismatched

        for part in parts:
            # Check for primary market mismatch
            has_primary_mismatch = (part['ebay_quantity'] == 0 and part['our_quantity'] > 0) or \
                                 (part['ebay_quantity'] > 0 and part['our_quantity'] == 0)
            
            # Collect additional market info
            additional_markets = part.get('additional_markets', [])
            additional_markets_count = len(additional_markets)
            additional_markets_details = []
            critical_issues = []
            warnings = []
            
            for market in additional_markets:
                market_detail = f"{market['item_id']}:{market['quantity']}"
                additional_markets_details.append(market_detail)
                
                if market['type'] == 'critical':
                    critical_issues.append(market['message'])
                elif market['type'] == 'warning':
                    warnings.append(market['message'])

            writer.writerow([
                part.get('part_id', ''),
                part.get('ebay_quantity', 0),
                part.get('our_quantity', 0),
                client_name,
                'Yes' if has_primary_mismatch else 'No',
                additional_markets_count,
                '; '.join(additional_markets_details) if additional_markets_details else '',
                '; '.join(critical_issues) if critical_issues else '',
                '; '.join(warnings) if warnings else ''
            ])

    return response

class RRRMessageListView(LoginRequiredMixin, ListView):
    login_url = '/accounts/login/'
    template_name = 'oscar/dashboard/messages/rrr_stock_check.html'
    context_object_name = 'system_messages'
    paginate_by = None

    def get_queryset(self):
        # Get organization ID from request if specified
        organization_id = self.request.GET.get('organization')
        
        # Get user's organization info
        user_org = self.request.user.organizations.first()
        is_pap_staff = (user_org and user_org.name == "PAP") or self.request.user.email == "<EMAIL>"
        is_external_manager = user_org and user_org.name != "PAP"
        
        # If user is superuser, they can see all messages
        if self.request.user.is_superuser:
            messages = get_rrr_messages(organization_id)
        # Staff users from <NAME_EMAIL> can see PAP organization messages
        elif is_pap_staff:
            pap_org = Organization.objects.filter(name="PAP").first()
            if pap_org:
                messages = get_rrr_messages(pap_org.id)
            else:
                messages = []
        # External managers can only see their organization's messages
        elif is_external_manager:
            if organization_id and str(user_org.id) != organization_id:
                return []
            messages = get_rrr_messages(user_org.id)
        else:
            return []
        
        # Paginate parts for each message
        for message in messages:
            # Get page numbers from request
            deleted_local_page = self.request.GET.get('deleted_local_page', 1)
            deleted_rrr_page = self.request.GET.get('deleted_rrr_page', 1)
            
            # Paginate deleted local parts
            deleted_local_paginator = Paginator(message['deleted_local'], 50)
            message['deleted_local_page'] = deleted_local_paginator.get_page(deleted_local_page)
            message['deleted_local_paginator'] = deleted_local_paginator
            
            # Paginate deleted RRR parts
            deleted_rrr_paginator = Paginator(message['deleted_rrr'], 50)
            message['deleted_rrr_page'] = deleted_rrr_paginator.get_page(deleted_rrr_page)
            message['deleted_rrr_paginator'] = deleted_rrr_paginator
        
        return messages

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data(**kwargs)
        ctx['title'] = _('RRR Stock Check Results')
        
        # Get organization ID from request if specified
        organization_id = self.request.GET.get('organization')
        ctx['selected_organization'] = organization_id
        
        # Get user's organization info
        user_org = self.request.user.organizations.first()
        is_pap_staff = (user_org and user_org.name == "PAP") or self.request.user.email == "<EMAIL>"
        
        # Get organizations that have messages
        organizations = []
        messages_dir = os.path.join(settings.MEDIA_ROOT, 'messages', 'RRR_stock_check')
        if os.path.exists(messages_dir):
            try:
                with open(os.path.join(messages_dir, 'rrr_stock_check_report.json'), 'r') as f:
                    data = json.load(f)
                    
                    # If superuser, show all organizations
                    if self.request.user.is_superuser:
                        for org_data in data['organizations']:
                            organizations.append({
                                'id': org_data['id'],
                                'name': org_data['name']
                            })
                    # If PAP staff, show only PAP organization
                    elif is_pap_staff:
                        for org_data in data['organizations']:
                            if org_data['name'] == "PAP":
                                organizations.append({
                                    'id': org_data['id'],
                                    'name': org_data['name']
                                })
                                break
                    # For regular users, show only their organization
                    elif user_org:
                        for org_data in data['organizations']:
                            if str(org_data['id']) == str(user_org.id):
                                organizations.append({
                                    'id': org_data['id'],
                                    'name': org_data['name']
                                })
                                break
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error reading message file: {e}")
        
        ctx['organizations'] = organizations
        
        return ctx 

class EbayMessageListView(LoginRequiredMixin, ListView):
    login_url = '/accounts/login/'
    template_name = 'oscar/dashboard/messages/ebay_stock_check.html'
    context_object_name = 'system_messages'
    paginate_by = None  # Disable ListView's pagination as we handle it manually

    def get_queryset(self):
        # Get organization ID from request if specified
        organization_id = self.request.GET.get('organization')
        
        # Get user's organization info
        user_org = self.request.user.organizations.first()
        is_pap_staff = (user_org and user_org.name == "PAP") or self.request.user.email == "<EMAIL>"
        is_external_manager = user_org and user_org.name != "PAP"
        
        # Get messages based on user role
        if self.request.user.is_superuser:
            message_list = get_ebay_messages(organization_id)
        elif is_pap_staff:
            pap_org = Organization.objects.filter(name="PAP").first()
            if pap_org:
                message_list = get_ebay_messages(pap_org.id)
            else:
                message_list = []
        elif is_external_manager:
            # Check if user's organization owns any eBay credentials
            if EbayApiCredentials.objects.filter(organizations=user_org).exists():
                if organization_id and str(user_org.id) != organization_id:
                    return []
                message_list = get_ebay_messages(user_org.id)  # Use regular get_ebay_messages for HTML view
            else:
                # User's organization doesn't own any credentials - no access to messages
                return []
        else:
            return []
        
        # Paginate mismatched parts for each message
        for message in message_list:
            # Get page number from request
            mismatched_parts_page = self.request.GET.get('mismatched_parts_page', 1)
            
            # Sort mismatched parts by priority (critical first, then warnings)
            sorted_parts = sorted(
                message['mismatched_parts'],
                key=lambda part: (0 if part['has_critical_issue'] else 1, part['part_id'])
            )
            
            # Create paginator with 50 items per page
            mismatched_parts_paginator = Paginator(sorted_parts, 50)
            
            try:
                # Get the requested page
                page_obj = mismatched_parts_paginator.page(mismatched_parts_page)
            except (ValueError, EmptyPage):
                # If page is not an integer or out of range, deliver first page
                page_obj = mismatched_parts_paginator.page(1)
            
            # Add paginated data to message
            message['mismatched_parts_page'] = page_obj
            message['mismatched_parts_paginator'] = mismatched_parts_paginator
        
        return message_list

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data(**kwargs)
        ctx['title'] = _('eBay Stock Check Results')
        
        # Add organization selection context
        organization_id = self.request.GET.get('organization')
        ctx['selected_organization'] = organization_id
        
        # Get organizations that own eBay credentials
        organizations = []
        messages_dir = os.path.join(settings.MEDIA_ROOT, 'messages', 'ebay_stock_check')
        if os.path.exists(messages_dir):
            try:
                with open(os.path.join(messages_dir, 'ebay_stock_check_latest.json'), 'r') as f:
                    data = json.load(f)
                    # Get organizations that own eBay credentials
                    # For credentials ID 1, only PAP organization can see it
                    # For other credentials, organizations can see them if they have access through ManyToMany
                    pap_org = Organization.objects.filter(name="PAP").first()
                    organizations_with_credentials = set()
                    
                    # Add organizations that have access to non-PAP credentials
                    organizations_with_credentials.update(
                        Organization.objects.filter(
                            ebay_api_credentials__in=EbayApiCredentials.objects.filter(
                                organizations__isnull=False
                            ).exclude(id=1)
                        ).values_list('id', flat=True)
                    )
                    
                    # Add PAP organization if it exists (for credentials ID 1)
                    if pap_org:
                        organizations_with_credentials.add(pap_org.id)
                    
                    # Filter organizations based on user role
                    if self.request.user.is_superuser:
                        for client_data in data['organizations']:
                            for org_data in client_data['organizations']:
                                if (org_data['id'] in organizations_with_credentials and 
                                    not any(org['id'] == org_data['id'] for org in organizations)):
                                    organizations.append({
                                        'id': org_data['id'],
                                        'name': org_data['name']
                                    })
                    else:
                        user_org = self.request.user.organizations.first()
                        is_pap_staff = (user_org and user_org.name == "PAP") or self.request.user.email == "<EMAIL>"
                        
                        if is_pap_staff:
                            # Show only PAP organization if it owns credentials
                            for client_data in data['organizations']:
                                for org_data in client_data['organizations']:
                                    if (org_data['id'] in organizations_with_credentials and 
                                        org_data['name'] == "PAP"):
                                        organizations.append({
                                            'id': org_data['id'],
                                            'name': org_data['name']
                                        })
                                        break
                        elif user_org and user_org.id in organizations_with_credentials:
                            # Show only user's organization if it owns credentials
                            organizations.append({
                                'id': user_org.id,
                                'name': user_org.name
                            })
            except (json.JSONDecodeError, IOError) as e:
                logger.error(f"Error reading eBay message file: {e}")
        
        ctx['organizations'] = organizations
        return ctx 