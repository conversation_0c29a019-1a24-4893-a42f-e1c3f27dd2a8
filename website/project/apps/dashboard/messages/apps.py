from django.urls import path
from django.utils.translation import gettext_lazy as _
from oscar.core.application import OscarDashboardConfig


class MessagesDashboardConfig(OscarDashboardConfig):
    label = 'messages_dashboard'
    name = 'project.apps.dashboard.messages'
    verbose_name = _('Messages')

    namespace = 'messages'
    permissions_map = {
        'messages:rrr-messages-list': ['warehouse_access_fn'],
        'messages:rrr-export-csv': ['warehouse_access_fn'],
        'messages:ebay-messages-list': ['warehouse_access_fn'],
        'messages:ebay-export-csv': ['warehouse_access_fn'],
    }

    def ready(self):
        from . import views
        self.rrr_message_list_view = views.RRRMessageListView
        self.ebay_message_list_view = views.EbayMessageListView
        self.rrr_export_csv_view = views.export_rrr_stock_check_csv
        self.ebay_export_csv_view = views.export_ebay_stock_check_csv

    def get_urls(self):
        from . import views
        urls = [
            path('rrr/', self.rrr_message_list_view.as_view(), name='rrr-messages-list'),
            path('rrr/export-csv/', self.rrr_export_csv_view, name='rrr-export-csv'),
            path('ebay/', self.ebay_message_list_view.as_view(), name='ebay-messages-list'),
            path('ebay/export-csv/', self.ebay_export_csv_view, name='ebay-export-csv'),
        ]
        return self.post_process_urls(urls) 