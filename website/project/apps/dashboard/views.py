from datetime import timed<PERSON>ta
from decimal import Decimal as D, ROUND_UP, ROUND_HALF_UP

from django.urls import reverse
from django.http import HttpResponseRedirect, JsonResponse
from django.utils import timezone
from django.db.models import Avg, Sum, Count
from django.db import models
from django.conf import settings

from oscar.apps.dashboard import views
from oscar.core.loading import get_model
from oscar.apps.basket.abstract_models import AbstractBasket

from project.apps.pap.models import VehiclePartStockMutation, VehiclePart, VehiclePartLocation

Account = get_model('accounts', 'Account')
Basket = get_model('basket', 'Basket')
Line = get_model('order', 'Line')
Order = get_model('order', 'Order')
Product = get_model('catalogue', 'Product')
ProductRecord = get_model('analytics', 'ProductRecord')
User = get_model('auth', 'User')

from .orders.views import queryset_orders_for_user


class IndexView(views.IndexView):
    def get(self, request, *args, **kwargs):
        # Check if request is AJAX by looking at HTTP_X_REQUESTED_WITH header
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            try:
                stats = self.get_stats()
                return JsonResponse(
                    {
                        'total_orders': stats['total_orders'],
                        'total_lines': stats['total_lines'],
                        'total_revenue': float(stats['total_revenue']),
                        'total_open_baskets': stats['total_open_baskets'],
                        'total_frozen_baskets': stats['total_frozen_baskets'],
                    }
                )
            except Exception as e:
                return JsonResponse({'error': str(e)}, status=500)
        return super().get(request, *args, **kwargs)

    def get_open_baskets(self, filters=None):
        """
        Get all open baskets. If *filters* dictionary is provided they will
        be applied on all open baskets and return only filtered results.
        """
        if filters is None:
            filters = {}
        filters['status'] = 'Open'
        return Basket.objects.filter(**filters).exclude(lines=None).distinct()

    def get_user_organization(self):
        """Cache and return user's organization info."""
        if not hasattr(self, '_user_organization_info'):
            self._user_organization_info = {
                'has_org': self.request.user.organizations.exists(),
                'organization': None,
                'is_pap': False,
            }

            if self._user_organization_info['has_org']:
                org = self.request.user.organizations.first()
                self._user_organization_info['organization'] = org
                self._user_organization_info['is_pap'] = org.name == "PAP"

        return self._user_organization_info

    def get_external_managers(self):
        """Get list of external managers IDs."""
        org_info = self.get_user_organization()
        if not org_info['has_org'] or not org_info['is_pap']:
            return []

        if not hasattr(self, '_external_managers'):
            # Cache external managers to avoid repeated queries
            external_orgs = org_info['organization'].__class__.objects.exclude(name="PAP")
            self._external_managers = []
            
            # Use a single query to get all users from all external organizations
            from django.db.models import Prefetch
            external_orgs = external_orgs.prefetch_related(
                Prefetch('users', queryset=User.objects.only('id'))
            )
            
            for org in external_orgs:
                self._external_managers.extend(list(org.users.values_list('id', flat=True)))

        return self._external_managers

    def get_hourly_report(self, hours=24, segments=10, org_info=None, external_managers=None):
        """
        Get report of order revenue split up in hourly chunks.
        """
        # Get datetime for 24 hours ago and align to hour boundaries in user's timezone
        time_now = timezone.localtime(timezone.now())  # Convert to user's timezone
        # Round current time to the current hour
        time_now = time_now.replace(minute=0, second=0, microsecond=0)
        # Start time should be exactly 24 hours before
        start_time = time_now - timedelta(hours=hours - 1)

        # Get base orders queryset with related data - use timezone.localtime for comparison
        orders_last_day = (
            queryset_orders_for_user(self.request.user)
            .select_related('user')
            .prefetch_related('lines__product__subowner', 'lines__product__owner')
            .filter(date_placed__gt=start_time)
        )

        # Get organization info if not provided
        if org_info is None:
            org_info = self.get_user_organization()
            
        # Get external managers if not provided
        if external_managers is None:
            external_managers = self.get_external_managers()
        
        # Calculate org_managers once at the beginning if needed
        org_managers = None
        if org_info['has_org'] and not org_info['is_pap']:
            org_managers = list(org_info['organization'].users.values_list('id', flat=True))
        
        # We'll no longer exclude entire orders with external manager parts
        # Instead, we'll filter at the line level when calculating totals

        order_total_hourly = []
        max_total = D('0.0')

        # Get all local sales for the period in a single query
        local_sales_base = VehiclePartStockMutation.objects.filter(
            mutation=VehiclePartStockMutation.MUTATION_SALES,
            created_at__gt=start_time,
            created_at__lte=time_now + timedelta(hours=1),  # Include full current hour
        ).select_related('vehicle_part_location__vehicle_part__owner', 'vehicle_part_location__vehicle_part')

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    local_sales_base = local_sales_base.exclude(
                        vehicle_part_location__vehicle_part__owner_id__in=external_managers
                    )
            else:
                # For External managers, show only their own sales
                local_sales_base = local_sales_base.filter(
                    vehicle_part_location__vehicle_part__owner_id__in=org_managers
                )

        # Get all local sales at once and convert to local time
        all_local_sales = list(local_sales_base)

        # Process each hour individually
        current_time = start_time
        for hour in range(0, hours):
            end_time = current_time + timedelta(hours=1)

            # Filter orders that were placed in this hour (based on hour component)
            hourly_orders = [
                order
                for order in orders_last_day
                if timezone.localtime(order.date_placed).replace(minute=0, second=0, microsecond=0) == current_time
            ]

            partan_total = D('0.0')
            rrr_total = D('0.0')
            ebay_total = D('0.0')
            recar_total = D('0.0')
            local_total = D('0.0')

            # Calculate totals for each shop at the line level
            for order in hourly_orders:
                # Determine shop type from order number prefix
                shop_type = None
                if order.number.startswith(settings.SHOP_RRR.upper()):
                    shop_type = 'rrr'
                elif order.number.startswith(settings.SHOP_RECAR.upper()):
                    shop_type = 'recar'
                elif order.number.startswith(settings.SHOP_EBAY.upper()):
                    shop_type = 'ebay'
                else:
                    shop_type = 'partan'
                
                # Process each line in the order
                for line in order.lines.all():
                    # Skip lines from external managers if user is PAP
                    if org_info['has_org'] and org_info['is_pap'] and external_managers:
                        if hasattr(line.product, 'subowner_id') and line.product.subowner_id in external_managers:
                            continue
                    
                    # Skip lines not from the user's organization if user is an external manager
                    if org_info['has_org'] and not org_info['is_pap']:
                        if not (hasattr(line.product, 'subowner_id') and line.product.subowner_id in org_managers):
                            continue
                    
                    # Add line total to the appropriate shop total
                    line_total = line.line_price_incl_tax
                    if shop_type == 'rrr':
                        rrr_total += line_total
                    elif shop_type == 'recar':
                        recar_total += line_total
                    elif shop_type == 'ebay':
                        ebay_total += line_total
                    else:  # partan
                        partan_total += line_total

            # Filter local sales for this hour - match by hour component in local time
            hour_local_sales = [
                sale
                for sale in all_local_sales
                if timezone.localtime(sale.created_at).replace(minute=0, second=0, microsecond=0) == current_time
            ]

            local_total = sum(sale.price * sale.quantity for sale in hour_local_sales)

            total_for_hour = partan_total + rrr_total + ebay_total + recar_total + local_total

            if total_for_hour > max_total:
                max_total = total_for_hour

            order_total_hourly.append(
                {
                    'end_time': current_time,
                    'total_incl_tax': total_for_hour,
                    'partan_total': partan_total,
                    'rrr_total': rrr_total,
                    'ebay_total': ebay_total,
                    'recar_total': recar_total,
                    'local_total': local_total,
                }
            )
            current_time = end_time

        # Calculate percentages and prepare y-axis
        if max_total:
            # Add 5% buffer to max value and round to nice number
            max_value = max_total * D('1.05')  # Add 5% buffer
            
            if max_value < D('10'):
                # If less than 10, round to nearest 1
                max_value = max_value.quantize(D('1'), rounding=ROUND_UP)
                nice_step = D('1')
            elif max_value < D('100'):
                # If less than 100, round to nearest 10
                max_value = (max_value / D('10')).quantize(D('1'), rounding=ROUND_UP) * D('10')
                nice_step = D('10')
            elif max_value < D('1000'):
                # If less than 1000, round to nearest 50
                max_value = (max_value / D('50')).quantize(D('1'), rounding=ROUND_UP) * D('50')
                nice_step = D('50')
            else:
                # For larger numbers, round to nearest 100
                max_value = (max_value / D('100')).quantize(D('1'), rounding=ROUND_UP) * D('100')
                nice_step = D('100')

            # Calculate percentages based on max_value without rounding
            for item in order_total_hourly:
                item['percentage'] = item['total_incl_tax'] / max_value * 100
                item['partan_percentage'] = item['partan_total'] / max_value * 100
                item['rrr_percentage'] = item['rrr_total'] / max_value * 100
                item['ebay_percentage'] = item['ebay_total'] / max_value * 100
                item['recar_percentage'] = item['recar_total'] / max_value * 100
                item['local_percentage'] = item['local_total'] / max_value * 100

            # Generate y-axis values using nice steps
            y_range = []
            for i in range(segments + 1):
                value = (max_value / segments * i).quantize(nice_step, rounding=ROUND_HALF_UP)
                y_range.insert(0, value)  # Insert at beginning to maintain descending order
        else:
            max_value = D('0.0')
            y_range = []
            for item in order_total_hourly:
                item['percentage'] = D('0.0')
                item['partan_percentage'] = D('0.0')
                item['rrr_percentage'] = D('0.0')
                item['ebay_percentage'] = D('0.0')
                item['recar_percentage'] = D('0.0')
                item['local_percentage'] = D('0.0')

        ctx = {
            'order_total_hourly': order_total_hourly,
            'max_revenue': max_value,
            'y_range': y_range,
        }
        return ctx

    def get_stats(self):
        # Get period from request, default to 'current_month'
        period = self.request.GET.get('period', 'current_month')

        # Calculate date range based on period
        now_time = timezone.now()
        today = now_time.date()
        
        if period == 'this_year':
            # Start of current year
            start_date = now_time.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
        elif period == 'last_month':
            # Start of last month
            if now_time.month == 1:
                last_month_date = now_time.replace(year=now_time.year-1, month=12, day=1)
            else:
                last_month_date = now_time.replace(month=now_time.month-1, day=1)
            start_date = last_month_date.replace(hour=0, minute=0, second=0, microsecond=0)
            # End of last month (inclusive) - set to midnight of first day of current month
            if now_time.month == 1:
                end_date = now_time.replace(year=now_time.year, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                end_date = now_time.replace(month=now_time.month, day=1, hour=0, minute=0, second=0, microsecond=0)
            now_time = end_date
        elif period == 'current_month':
            # Start of current month
            start_date = now_time.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        else:  # 'all' or invalid period
            start_date = None

        # Get base orders queryset with related data - optimize joins
        orders = queryset_orders_for_user(self.request.user).select_related('user')

        # Store organization info for later use
        org_info = self.get_user_organization()
        
        # Get external managers once and cache the result
        external_managers = self.get_external_managers()
        
        # For External managers, get their IDs once and cache the result
        org_managers = None
        if org_info['has_org'] and not org_info['is_pap']:
            org_managers = list(org_info['organization'].users.values_list('id', flat=True))
            
        # For PAP organization, get PAP managers once and cache the result
        pap_managers = None
        if org_info['has_org'] and org_info['is_pap']:
            pap_managers = list(User.objects.filter(organizations__name="PAP").values_list('id', flat=True))

        # Additional filtering for PAP organization members
        if external_managers:
            orders = orders.exclude(lines__product__subowner_id__in=external_managers).distinct()

        # Apply period filtering to orders
        if start_date:
            orders = orders.filter(date_placed__gte=start_date)
            if period == 'last_month':
                orders = orders.filter(date_placed__lt=end_date)

        datetime_24hrs_ago = now_time - timedelta(hours=24)
        orders_last_day = orders.filter(date_placed__gt=datetime_24hrs_ago)

        # Get local sales for the last 24 hours and selected period in a single query
        local_sales_base = VehiclePartStockMutation.objects.filter(
            mutation=VehiclePartStockMutation.MUTATION_SALES
        ).select_related('vehicle_part_location__vehicle_part__owner')

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    local_sales_base = local_sales_base.exclude(
                        vehicle_part_location__vehicle_part__owner_id__in=external_managers
                    )
            else:
                # For External managers, show only their own sales
                local_sales_base = local_sales_base.filter(
                    vehicle_part_location__vehicle_part__owner_id__in=org_managers
                )

        # Get local sales for last 24 hours - use values() to optimize
        local_sales_24h = local_sales_base.filter(created_at__gt=datetime_24hrs_ago).values('price', 'quantity')
        local_revenue_24h = sum(sale['price'] * sale['quantity'] for sale in local_sales_24h)

        # Get local sales for selected period - use values() to optimize
        if start_date:
            local_sales = local_sales_base.filter(created_at__gte=start_date)
            if period == 'last_month':
                local_sales = local_sales.filter(created_at__lt=end_date)
            local_sales = local_sales.values('price', 'quantity')
        else:
            local_sales = local_sales_base.values('price', 'quantity')
        local_revenue = sum(sale['price'] * sale['quantity'] for sale in local_sales)

        # Calculate total revenue at the line level instead of order level
        # This ensures accurate attribution of revenue to organizations
        # and excludes shipping costs
        
        # For last 24 hours
        lines_last_day = Line.objects.filter(order__in=orders_last_day)
        
        # Apply organization filtering at the line level
        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    lines_last_day = lines_last_day.exclude(
                        product__subowner_id__in=external_managers
                    )
            else:
                # For External managers, show only their lines
                lines_last_day = lines_last_day.filter(
                    product__subowner_id__in=org_managers
                )
        
        # Calculate revenue from order lines for last 24 hours
        total_revenue_last_day = lines_last_day.aggregate(
            Sum('line_price_incl_tax')
        )['line_price_incl_tax__sum'] or D('0.00')
        
        # Add local sales revenue
        total_revenue_last_day += local_revenue_24h
        
        # For all time or selected period
        all_lines = Line.objects.filter(order__in=orders)
        
        # Apply organization filtering at the line level
        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    all_lines = all_lines.exclude(
                        product__subowner_id__in=external_managers
                    )
            else:
                # For External managers, show only their lines
                all_lines = all_lines.filter(
                    product__subowner_id__in=org_managers
                )
        
        # Calculate revenue from order lines for the selected period
        total_revenue = all_lines.aggregate(
            Sum('line_price_incl_tax')
        )['line_price_incl_tax__sum'] or D('0.00')
        
        # Add local sales revenue
        total_revenue += local_revenue

        # Get total lines in a single query - already filtered by organization
        total_lines_last_day = lines_last_day.count()
        total_lines = all_lines.count()

        # Period-filtered new customers calculation
        if org_info['has_org']:
            if org_info['is_pap']:
                # For PAP, we need to find all customers who have ordered PAP products in the last 24 hours
                # Get orders with PAP products (no subowner or PAP manager as subowner)
                pap_orders = orders_last_day.filter(
                    models.Q(lines__product__subowner_id__isnull=True) | 
                    models.Q(lines__product__subowner_id__in=pap_managers)
                ).distinct()
                
                # Now count new registered users from these orders
                new_registered = pap_orders.filter(
                    user__isnull=False, 
                    user__date_joined__gt=datetime_24hrs_ago, 
                    user__is_active=True
                ).values('user').distinct().count()

                # Count new guest customers from these orders
                new_guests = pap_orders.filter(
                    user__isnull=True, 
                    guest_email__isnull=False
                ).exclude(
                    guest_email=''
                ).values('guest_email').distinct().count()
            else:
                # For External managers, show only customers who ordered their products
                # Get orders with this organization's products
                org_orders = orders_last_day.filter(
                    lines__product__subowner_id__in=org_managers
                ).distinct()
                
                # Count new registered users from these orders
                new_registered = org_orders.filter(
                    user__isnull=False,
                    user__date_joined__gt=datetime_24hrs_ago,
                    user__is_active=True
                ).values('user').distinct().count()

                # Count new guest customers from these orders
                new_guests = org_orders.filter(
                    user__isnull=True, 
                    guest_email__isnull=False
                ).exclude(
                    guest_email=''
                ).values('guest_email').distinct().count()
        else:
            # Default case - all customers (for sysadmin)
            # Period-filtered new customers
            new_registered = (
                orders_last_day.filter(
                    user__isnull=False, user__date_joined__gt=datetime_24hrs_ago, user__is_active=True
                )
                .values('user')
                .distinct()
                .count()
            )

            new_guests = (
                orders_last_day.filter(user__isnull=True, guest_email__isnull=False)
                .exclude(guest_email='')
                .values('guest_email')
                .distinct()
                .count()
            )

        total_customers_last_day = new_registered + new_guests

        # Filter baskets based on organization - optimize basket queries
        basket_base_query = Basket.objects.filter(status='Open').exclude(lines=None)
        frozen_baskets_query = Basket.objects.filter(status='Frozen')

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    # Optimize by using a more efficient query approach
                    # Instead of using EXISTS subqueries, use a more direct approach
                    basket_base_query = basket_base_query.exclude(
                        lines__product__subowner_id__in=external_managers
                    ).distinct()
                    
                    frozen_baskets_query = frozen_baskets_query.exclude(
                        lines__product__subowner_id__in=external_managers
                    ).distinct()
            else:
                # For External managers, show only their baskets
                basket_base_query = basket_base_query.filter(
                    lines__product__subowner__in=org_managers
                ).distinct()

                frozen_baskets_query = frozen_baskets_query.filter(
                    lines__product__subowner__in=org_managers
                ).distinct()

        # Get last 24 hours baskets - optimize by using only() to select fewer fields
        total_open_baskets_last_day = basket_base_query.filter(
            date_created__gt=datetime_24hrs_ago
        ).only('id').count()

        total_frozen_baskets_last_day = frozen_baskets_query.filter(
            date_frozen__gt=datetime_24hrs_ago
        ).only('id').count()

        # Apply period filter if needed
        if start_date:
            basket_base_query = basket_base_query.filter(date_created__gte=start_date)
            frozen_baskets_query = frozen_baskets_query.filter(date_frozen__gte=start_date)
            if period == 'last_month':
                basket_base_query = basket_base_query.filter(date_created__lt=end_date)
                frozen_baskets_query = frozen_baskets_query.filter(date_frozen__lt=end_date)

        # Pre-calculate counts to avoid repeated queries
        total_open_baskets = basket_base_query.only('id').count()
        total_frozen_baskets = frozen_baskets_query.only('id').count()

        # Get total products count and today's new products
        products = Product.objects.all()

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    products = products.exclude(subowner_id__in=external_managers)
            else:
                # For External managers, show only their products
                products = products.filter(subowner__in=org_managers)

        # Get products with stock
        vehicle_parts_with_stock = VehiclePart.objects.filter(
            locations__num_in_location__gt=0
        ).distinct()

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    vehicle_parts_with_stock = vehicle_parts_with_stock.exclude(owner_id__in=external_managers)
            else:
                # For External managers, show only their parts
                vehicle_parts_with_stock = vehicle_parts_with_stock.filter(owner_id__in=org_managers)

        # Optimize the count query by using a more efficient approach
        # Instead of fetching all fields and then counting, just count directly
        products_in_stock_count = vehicle_parts_with_stock.only('id').count()

        # Get new vehicle parts today
        vehicle_parts = VehiclePart.objects.all()

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    vehicle_parts = vehicle_parts.exclude(owner_id__in=external_managers)
            else:
                # For External managers, show only their parts
                vehicle_parts = vehicle_parts.filter(owner_id__in=org_managers)

        # Get today's date range in the current timezone
        today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        new_parts_today = (
            vehicle_parts.filter(created_at__gte=today_start, created_at__lt=today_end).distinct().count()
        )

        # Get count of old parts that had stock additions today
        stock_additions_today = VehiclePartLocation.objects.filter(
            num_in_location_increased_at__gte=today_start,
            num_in_location_increased_at__lt=today_end,
            vehicle_part__created_at__lt=today_start,
        )

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    stock_additions_today = stock_additions_today.exclude(vehicle_part__owner_id__in=external_managers)
            else:
                # For External managers, show only their parts
                stock_additions_today = stock_additions_today.filter(vehicle_part__owner_id__in=org_managers)

        restocked_parts_today = stock_additions_today.values('vehicle_part').distinct().count()

        # Get new Partan advertisements today
        partan_products = products.filter(
            pap_product_id__isnull=False, date_created__gte=today_start, date_created__lt=today_end
        ).count()

        # Get new products today
        new_products_today = VehiclePart.objects.filter(created_at__gte=today_start, created_at__lt=today_end)

        if org_info['has_org']:
            if org_info['is_pap']:
                if external_managers:
                    new_products_today = new_products_today.exclude(owner_id__in=external_managers)
            else:
                # For External managers, show only their parts
                new_products_today = new_products_today.filter(owner_id__in=org_managers)

        new_products_today = new_products_today.count()

        stats = {
            'total_orders_last_day': orders_last_day.count(),
            'total_lines_last_day': total_lines_last_day,
            'average_order_costs': orders_last_day.aggregate(Avg('total_incl_tax'))['total_incl_tax__avg']
            or D('0.00'),
            'total_revenue_last_day': total_revenue_last_day,
            'hourly_report_dict': self.get_hourly_report(hours=24, org_info=org_info, external_managers=external_managers),
            'total_open_baskets_last_day': total_open_baskets_last_day,
            'total_frozen_baskets_last_day': total_frozen_baskets_last_day,
            'total_open_baskets': total_open_baskets,
            'total_frozen_baskets': total_frozen_baskets,
            'total_orders': orders.count(),
            'total_lines': total_lines,
            'total_revenue': total_revenue,
            'products_in_stock': products_in_stock_count,
            'all_products': products.count(),
            'new_products_today': new_products_today,
            'restocked_parts_today': restocked_parts_today,
            'new_partan_ads_today': partan_products,
            'order_status_breakdown': orders.order_by('status').values('status').annotate(freq=Count('id')),
            'selected_period': period,
            'total_customers_last_day': total_customers_last_day,
        }

        return stats
