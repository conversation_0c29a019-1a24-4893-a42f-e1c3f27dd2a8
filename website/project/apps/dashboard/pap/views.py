import datetime
import json
import os
import logging
import csv
import io
import hashlib
import time
from io import BytesIO
from decimal import Decimal
from django.conf import settings
from django.contrib import messages
from django.core import serializers
from django.core.exceptions import SuspiciousOperation
from django.urls import reverse_lazy, reverse
from django.db import connection
from django.db.models import F, Q, Sum, Case, When, Count, Prefetch
from django.core.paginator import Paginator
from django.core.cache import cache
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.http import Http404
from django.shortcuts import get_object_or_404, render
from django.utils import timezone

from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _, get_language
from django.views.decorators.cache import cache_control
from django.views.generic import (
    <PERSON>View,
    CreateView,
    UpdateView,
    Delete<PERSON>iew,
    DetailView,
    View,
)

import xlwt
from dateutil.relativedelta import relativedelta
from reportlab.lib import colors
from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER, TA_RIGHT
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table
from django.core.files.base import ContentFile
from django.template.loader import render_to_string

from sorl.thumbnail import get_thumbnail

from .forms import (
    VehicleForm,
    VehicleDeleteForm,
    VehiclePartForm,
    LocationForm,
    BoxForm,
    VehicleSearchForm,
    VehiclePartSearchForm,
    VehiclePartLocationForm,
    VehiclePartAdvertismentFormForPartan,
    VehiclePartAdvertismentUpdateFormForPartan,
    VehiclePartAdvertismentFormForEbay,
    VehiclePartAdvertismentUpdateFormForEbay,
    VehiclePartAdvertismentFormForRrr,
    VehiclePartAdvertismentUpdateFormForRrr,
    VehiclePartAdvertismentFormForRecar,
    VehiclePartAdvertismentUpdateFormForRecar,
    VehiclePartImagesUpdateForm,
    VehicleImageFormSet,
    SimpleSearchForm,
    BoxSearchForm,
    ActionLogSearchForm,
    VehiclePartExportForm,
    VehiclePartStockMutationForm,
    VehiclePartStockMutationSearchForm,
)

from project.apps.catalogue.models import ProductExtraAttribute
from project.apps.pap.models import (
    Vehicle,
    VehiclePart,
    VehiclePartImage,
    VehiclePartStockMutation,
    Location,
    Box,
    Organization,
    ActionLog,
    VehiclePartLocation,
    VehiclePartLocationAllocation,
    VehiclePartAdvertisment,
    clean_code,
)

logger = logging.getLogger(__name__)


class PapMixin(object):
    paginate_by = 15

    def get_form_kwargs(self):
        kwargs = super(PapMixin, self).get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_queryset(self):
        queryset = self.model.objects.all().select_related('organization')

        if not self.request.user.is_superuser:
            organization = Organization.get_user_organization(self.request.user)
            queryset = queryset.filter(organization=organization)

        return queryset

    def log_action(self, action=None, content_type=None, pk=None):
        try:
            object = self.get_object().pk
        except AttributeError:
            object = None
        pk = pk or object
        content_type = content_type or self.model._meta.model_name
        action = action or self.__class__.__name__.replace("View", "")
        action = "Action: {}, Content type: {}, Object: pk={}".format(action, content_type, pk)
        ActionLog.objects.create(user=self.request.user, action=action)

    def post(self, request, *args, **kwargs):
        self.log_action()
        return super(PapMixin, self).post(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """Get context data with optimized pagination and record counting"""
        context = super(PapMixin, self).get_context_data(**kwargs)
        if "page_obj" in context and context["page_obj"]:
            context["min_page"] = context["page_obj"].number - 4
            context["max_page"] = context["page_obj"].number + 4
            
            # Use the paginator's count instead of querying again
            if hasattr(context["page_obj"], 'paginator') and hasattr(context["page_obj"].paginator, 'count'):
                context["num_records"] = context["page_obj"].paginator.count
            else:
                # Fallback to legacy behavior
                context["num_records"] = self.get_queryset().count()
        else:
            context["num_records"] = self.get_queryset().count()
            
        return context


class VehicleSearchView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        search_text = request.GET.get("q")
        vehicles = []
        if search_text:
            vehicles = Vehicle.objects.filter(code__icontains=search_text)
            vehicles = [{"id": i.id, "value": str(i), "label": str(i)} for i in vehicles]
        return HttpResponse(json.dumps(vehicles), content_type="application/json")


class VehicleListView(PapMixin, ListView):
    search_form_class = VehicleSearchForm
    template_name = "oscar/dashboard/pap/vehicle_list.html"
    model = Vehicle

    def dispatch(self, request, *args, **kwargs):
        if request.GET:
            self.search_form = self.search_form_class(request.GET, user=request.user)
        else:
            organization = Organization.get_user_organization(self.request.user)
            return HttpResponseRedirect(f"{reverse('dashboard:vehicle-list')}?organization={organization.id}")
        return super(VehicleListView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = super(VehicleListView, self).get_queryset()

        if self.search_form.is_valid():
            manufacturer = self.search_form.cleaned_data.get("manufacturer")
            model = self.search_form.cleaned_data.get("model")
            type = self.search_form.cleaned_data.get("type")
            fuel = self.search_form.cleaned_data.get("fuel")
            color = self.search_form.cleaned_data.get("color")
            transmission = self.search_form.cleaned_data.get("transmission")
            code = self.search_form.cleaned_data.get("code")
            lot = self.search_form.cleaned_data.get("lot")
            year = self.search_form.cleaned_data.get("year")
            vin = self.search_form.cleaned_data.get("vin")
            organization = self.search_form.cleaned_data.get("organization")

            if manufacturer:
                queryset = queryset.filter(manufacturer__brand__icontains=manufacturer)
            if model:
                queryset = queryset.filter(model__name__icontains=model)
            if type:
                queryset = queryset.filter(type__name__icontains=type)
            if fuel:
                queryset = queryset.filter(fuel=fuel)
            if color:
                queryset = queryset.filter(color=color)
            if transmission:
                queryset = queryset.filter(transmission=transmission)
            if code:
                queryset = queryset.filter(code__icontains=code)
            if lot:
                queryset = queryset.filter(lot__icontains=lot)
            if year:
                queryset = queryset.filter(year=year)
            if vin:
                queryset = queryset.filter(vin__icontains=vin)
            if organization and self.request.user.is_superuser:
                queryset = queryset.filter(organization=organization)

        return queryset

    def get_context_data(self, **kwargs):
        context = super(VehicleListView, self).get_context_data(**kwargs)
        context["form"] = self.search_form
        return context


class ImageHandlingMixin:
    """Mixin to handle image upload, edit, delete, and ordering for vehicle parts (and vehicles in second phase update)."""

    def handle_ajax_image_upload(self):

        new_images = self.request.FILES.getlist('new_images')
        images_data = []
        for image_file in new_images:
            image = self.object.images.create(image=image_file, resized_at=datetime.datetime.now().strftime('%Y-%m-%d %H:%M'))

            try:
                image.resize_image()
            except Exception:
                pass

            image.order = self.object.images.count()
            image.save()

            thumb = get_thumbnail(image.image, "200x200")
            thumbnail_url = thumb.url
            images_data.append(
                {
                    'id': image.id,
                    'url': image.image.url,
                    'thumbnail_url': thumbnail_url,
                    'order': image.order,
                }
            )
        return JsonResponse({'status': 'success', 'images': images_data})

    def handle_ajax_form_submission(self, form):
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def form_valid(self, form):
        try:
            response = super().form_valid(form)

            if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Process deleted images
                deleted_images = self.request.POST.getlist('deleted_images[]', [])
                if deleted_images:
                    self.object.images.filter(id__in=deleted_images).delete()

                # Process image order
                image_order = self.request.POST.getlist('image_order[]', [])
                for item in image_order:
                    try:
                        item_data = json.loads(item)
                        image = self.object.images.get(id=item_data['id'])
                        image.order = item_data['order']
                        image.save()
                    except (json.JSONDecodeError, VehiclePartImage.DoesNotExist):
                        continue

                # Process edited images
                edited_images = {
                    key: self.request.FILES[key] for key in self.request.FILES if key.startswith('edited_images[')
                }
                for key, image_file in edited_images.items():
                    try:
                        image_id = key.replace('edited_images[', '').replace(']', '')
                        image = self.object.images.get(id=image_id)
                        ext = image_file.name.split('.')[-1] if '.' in image_file.name else 'jpeg'
                        filename = f'image_{image_id}.{ext}'
                        image.image.save(filename, image_file, save=False)
                        image.save()
                    except (ValueError, VehiclePartImage.DoesNotExist):
                        continue
            return response
        except Exception as e:
            logger.exception("Failed to process images.")
            return JsonResponse({'status': 'error', 'message': 'Could not process images.'}, status=500)

    def post(self, request, *args, **kwargs):
        """Override POST method to handle AJAX requests."""
        self.object = self.get_object()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            if 'new_images' in request.FILES:
                return self.handle_ajax_image_upload()
            else:
                form = self.get_form()
                return self.handle_ajax_form_submission(form)
        else:
            return super().post(request, *args, **kwargs)


class VehicleMixin(PapMixin):
    form_class = VehicleForm
    template_name = "oscar/dashboard/pap/vehicle_form.html"
    model = Vehicle

    image_formset = VehicleImageFormSet

    def get_object(self, queryset=None):
        self.creating = "pk" not in self.kwargs
        if self.creating:
            return None  # success
        else:
            object = super(VehicleMixin, self).get_object(queryset)
            return object

    def form_valid(self, form):
        return self.process_all_forms(form)

    def form_invalid(self, form):
        return self.process_all_forms(form)

    def process_all_forms(self, form):
        if self.creating and form.is_valid():
            self.object = form.save()

        image_formset = self.image_formset(self.request.POST, self.request.FILES, instance=self.object)

        is_valid = all([form.is_valid(), image_formset.is_valid()])

        if is_valid:
            return self.forms_valid(form, image_formset)
        else:
            if self.creating and form.is_valid():
                self.object.delete()
                self.object = None
            image_formset = self.image_formset(instance=self.object)
            return self.forms_invalid(form, image_formset)

    def forms_valid(self, form, image_formset):
        if not self.creating:
            self.object = form.save()
        # Save formsets
        image_formset.save()
        return HttpResponseRedirect(self.get_success_url())

    def forms_invalid(self, form, image_formset):
        messages.error(
            self.request,
            _("Your submitted data was not valid - please correct the below errors"),
        )
        ctx = self.get_context_data(form=form, image_formset=image_formset)
        return self.render_to_response(ctx)

    def get_context_data(self, **kwargs):
        ctx = super(VehicleMixin, self).get_context_data(**kwargs)
        if "image_formset" not in ctx:
            ctx["image_formset"] = self.image_formset(instance=self.object)
        return ctx

    def get_success_url(self):
        if self.creating:
            msg = _("Created vehicle '%s'") % self.object
        else:
            msg = _("Updated vehicle '%s'") % self.object
        messages.success(self.request, msg)
        url = reverse("dashboard:vehicle-list")
        return url


class VehicleCreateView(VehicleMixin, CreateView):
    pass


class VehicleUpdateView(VehicleMixin, UpdateView):
    def forms_valid(self, form, image_formset):
        response = super(VehicleUpdateView, self).forms_valid(form, image_formset)

        error_messages = []
        vehicle = self.get_object()
        vehicle_part_ids = vehicle.locations.filter(vehicle_part__isnull=False).values_list(
            "vehicle_part_id", flat=True
        )
        vehicle_parts = VehiclePart.objects.filter(id__in=vehicle_part_ids)
        for vehicle_part in vehicle_parts:
            vehicle_part.year = vehicle.year
            vehicle_part.manufacturer = vehicle.manufacturer
            vehicle_part.model = vehicle.model
            vehicle_part.type = vehicle.type
            vehicle_part.engine = vehicle.engine
            vehicle_part.drive_position = vehicle.drive_position
            vehicle_part.save()

            for adv in vehicle_part.advertisments.all():
                success, error_msg = adv.create_or_update_webshop_product(forced=False)
                error_messages.append(error_msg)
        if any(error_messages):
            error_msg = " * ".join(error_messages)
            messages.warning(
                self.request,
                _("Failed to update advertisments related to this vehicle! Failure reason: %s") % error_msg,
            )

        return response


class VehicleDeleteView(PapMixin, DeleteView):
    form_class = VehicleDeleteForm
    template_name = "oscar/dashboard/pap/vehicle_delete.html"
    model = Vehicle
    success_url = reverse_lazy("dashboard:vehicle-list")


class VehicleDetailView(PapMixin, DetailView):
    template_name = "oscar/dashboard/pap/vehicle_details.html"
    model = Vehicle


class VehicleDetailAjaxView(VehicleDetailView):
    template_name = "oscar/dashboard/pap/vehicle_details_ajax.html"


class VehicleDetailJSONView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        vehicle = get_object_or_404(Vehicle, pk=self.kwargs["pk"])
        vehicle_json = serializers.serialize("json", [vehicle])
        return HttpResponse(vehicle_json, content_type="application/json")


class VehicleSalesView(PapMixin, DetailView):
    template_name = "oscar/dashboard/pap/vehicle_sales.html"
    model = Vehicle


class ExportSearchResultsMixin(object):
    export_form_class = None

    def dispatch(self, request, *args, **kwargs):
        if request.method == "POST":
            self.export_form = self.export_form_class(request.POST)
        else:
            self.export_form = self.export_form_class(request.GET)
        return super(ExportSearchResultsMixin, self).dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        if request.GET.get("action", "").lower() == "export":
            file_content, file_name, file_content_type = self.export_search_results()
            response = HttpResponse(file_content, content_type=file_content_type)
            response["Content-Disposition"] = "attachment; filename={}".format(file_name)
            return response
        else:
            return super(ExportSearchResultsMixin, self).get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super(ExportSearchResultsMixin, self).get_context_data(**kwargs)
        context["export_form"] = self.export_form
        return context

    def export_search_results(self):
        file_content = None
        file_name = None
        file_content_type = None
        if self.export_form.is_valid():
            export_as = self.export_form.cleaned_data["export_as"]
            export_fields = self.export_form.cleaned_data["export_fields"]
            if export_as == self.export_form.EXPORT_XLS:
                (
                    file_content,
                    file_name,
                    file_content_type,
                ) = self.export_search_results_as_xls(export_fields)
            elif export_as == self.export_form.EXPORT_CSV:
                (
                    file_content,
                    file_name,
                    file_content_type,
                ) = self.export_search_results_as_csv(export_fields)
            elif export_as == self.export_form.EXPORT_PDF:
                (
                    file_content,
                    file_name,
                    file_content_type,
                ) = self.export_search_results_as_pdf(export_fields)
        return file_content, file_name, file_content_type

    def get_export_file_name(self, export_as):
        return "{}.{}".format(self.model._meta.verbose_name_plural.lower().replace(" ", "-"), export_as)

    def get_search_results(self):
        return self.enrich_object_list(self.get_queryset())

    def export_search_results_as_xls(self, export_fields):
        file_name = self.get_export_file_name("xls")
        file_content_type = "application/vnd.ms-excel"
        file_content = ""

        wb = xlwt.Workbook()
        ws = wb.add_sheet("Export")

        search_results = self.get_search_results()

        for column_counter, field in enumerate(export_fields):
            label = self.export_form.available_fields_map[field]
            ws.write(0, column_counter, str(label))

        for row_counter, result in enumerate(list(search_results)):
            export_line = []
            for column_counter, field in enumerate(export_fields):
                attr = getattr(result, field)
                if callable(attr):
                    attr = attr()
                ws.write(row_counter + 1, column_counter, str(attr))

        output = BytesIO()
        wb.save(output)

        file_content = output.getvalue()

        return file_content, file_name, file_content_type

    def export_search_results_as_csv(self, export_fields):
        file_name = self.get_export_file_name("csv")
        file_content_type = "text/csv"
        file_content = ""

        search_results = self.get_search_results()
        export_lines = []

        export_line = []
        for field in export_fields:
            label = self.export_form.available_fields_map[field]
            export_line.append(str(label))
        export_lines.append(";".join(export_line))

        for result in search_results:
            export_line = []
            for field in export_fields:
                attr = getattr(result, field)
                if callable(attr):
                    attr = attr()
                attr = str(attr).replace(";", " - ").replace(",", " - ").replace("\r", "").replace("\n", "")
                export_line.append(str(attr))
            export_lines.append(";".join(export_line))

        file_content = "\n".join(export_lines)
        return file_content, file_name, file_content_type

    def export_search_results_as_pdf(self, export_fields):
        file_name = self.get_export_file_name("pdf")
        file_content_type = "application/pdf"
        file_content = ""

        search_results = self.get_search_results()

        output = BytesIO()
        doc = SimpleDocTemplate(
            output,
            pagesize=A4,
            rightMargin=20,
            leftMargin=20,
            topMargin=20,
            bottomMargin=15,
        )
        styles = getSampleStyleSheet()
        styles.add(
            ParagraphStyle(
                name="LogoC",
                fontSize=24,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(
            ParagraphStyle(
                name="HeaderC",
                fontSize=20,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(
            ParagraphStyle(
                name="SubHeaderC",
                fontSize=14,
                fontName="open_sans",
                alignment=TA_CENTER,
            )
        )
        styles.add(ParagraphStyle(name="NormalR", fontSize=10, fontName="open_sans", alignment=TA_RIGHT))
        styles.add(ParagraphStyle(name="NormalJ", fontSize=10, fontName="open_sans", alignment=TA_JUSTIFY))
        styles.add(ParagraphStyle(name="NormalC", fontSize=10, fontName="open_sans", alignment=TA_CENTER))
        styles.add(
            ParagraphStyle(
                name="NormalRB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_RIGHT,
            )
        )
        styles.add(
            ParagraphStyle(
                name="NormalJB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_JUSTIFY,
            )
        )
        styles.add(
            ParagraphStyle(
                name="NormalCB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(ParagraphStyle(name="SmallC", fontSize=8, fontName="open_sans", alignment=TA_CENTER))

        content = []
        lines = []

        table_header_line = []
        for field in export_fields:
            label = self.export_form.available_fields_map[field]
            table_header_line.append(Paragraph(str(label), styles["NormalJB"]))
        lines.append(table_header_line)

        for result in search_results:
            table_body_line = []
            for field in export_fields:
                attr = getattr(result, field)
                if callable(attr):
                    attr = attr()
                table_body_line.append(Paragraph(str(attr).replace("<br", "\n"), styles["NormalJ"]))
            lines.append(table_body_line)

        table = Table(
            lines,
            colWidths=[(16 / len(export_fields)) * cm for f in export_fields],
            style=[
                ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ("VALIGN", (0, 0), (-1, -1), "TOP"),
            ],
        )
        content.append(table)
        doc.build(content)
        file_content = output.getvalue()

        return file_content, file_name, file_content_type


class VehiclePartListView(PapMixin, ExportSearchResultsMixin, ListView):
    search_form_class = VehiclePartSearchForm
    export_form_class = VehiclePartExportForm
    template_name = "oscar/dashboard/pap/vehicle_part_list.html"
    model = VehiclePart

    def dispatch(self, request, *args, **kwargs):
        # Create a new timestamp when user opens the page without GET parameters
        # or clicks the "Reset Filters" button
        if (request.method == "GET" and not request.GET) or (request.method == "POST" and request.POST.get("action") == "reset_filters"):
            # Generate a new timestamp
            request.session['vehicle_part_timestamp'] = str(int(time.time()))
            
            # If this is a reset filters action, also clear filters and cache
            if request.method == "POST" and request.POST.get("action") == "reset_filters":
                # Clear all filters from the session
                for key in list(request.session.keys()):
                    if key.startswith("vehicle_part_filter_"):
                        del request.session[key]
                
                # Clear user cache
                self.clear_user_cache()
                
                # Redirect to the same page to avoid form resubmission
                return HttpResponseRedirect(request.path)
        
        # Initialize search form
        if request.method == "POST":
            self.search_form = self.search_form_class(request.POST, user=request.user)
        else:
            self.search_form = self.search_form_class(request.GET, user=request.user)
            
        return super(VehiclePartListView, self).dispatch(request, *args, **kwargs)
        
    def clear_user_cache(self):
        """
        Clear all vehicle part list cache entries for the current user.
        This is called when the user clicks the "Reset Filters" button.
        """
        try:
            # Get all keys that match the pattern for this user
            user_id = self.request.user.id
            cache_pattern = f'vehicle_part_list_count_{user_id}_*'
            
            # If using Redis cache backend
            if hasattr(cache, 'client') and hasattr(cache.client, 'keys'):
                # Get all keys matching the pattern
                keys = cache.client.keys(cache_pattern)
                if keys:
                    # Delete all matching keys
                    cache.client.delete(*keys)
                    logger.info(f"Cleared {len(keys)} cache entries for user {user_id}")
            else:
                # For other cache backends, we can't easily clear by pattern
                # Just log that we can't do it
                logger.warning(f"Cache clearing by pattern not supported for current cache backend")
        except Exception as e:
            # Don't break the page if cache clearing fails
            logger.warning(f"Error clearing cache: {e}")
            
    def get_queryset(self):
        queryset = (
            super(VehiclePartListView, self)
            .get_queryset()
            .only(
                # Core fields needed for display and filtering
                'id', 'code', 'code_cleaned', 'warehouse_code', 'warehouse_code_cleaned',
                'year', 'created_at', 'modified_at', 'status', 'condition',
                
                # Foreign key IDs (needed for joins)
                'manufacturer_id', 'model_id', 'type_id', 'engine_id', 
                'organization_id', 'owner_id',
                
                # Related fields that need to be accessed directly
                'manufacturer__brand', 'model__name', 'type__name', 'engine__code',
                'organization__name', 'owner__email',
            )
            .select_related(
                'manufacturer',     # Manufacturer information
                'model',            # Model information
                'type',             # Type information
                'engine',           # Engine information
                'organization',     # Organization information
                'owner',            # Owner information
            )
            .prefetch_related(
                # Locations and their related objects
                "locations", 
                "locations__location", 
                "locations__box",
                "locations__vehicle",
                "locations__vehicle__manufacturer",  # Deeper prefetch for vehicle manufacturer
                "locations__vehicle__model",         # Deeper prefetch for vehicle model
                "locations__vehicle__type",          # Deeper prefetch for vehicle type
                # Location allocations (used for has_temp_allocations check)
                "locations__allocations",
                # Images
                "images",
                # Categories (used in get_categories_display method)
                "categories",
                # Attributes (used in get_attributes_display method)
                "attributes",
                # Advertisements (used for price_is_older_than_6_months check)
                "advertisments"
            )
        )

        if self.search_form.is_valid():
            vehicle = self.search_form.cleaned_data.get("vehicle")
            year = self.search_form.cleaned_data.get("year")
            manufacturer = self.search_form.cleaned_data.get("manufacturer")
            model = self.search_form.cleaned_data.get("model")
            type = self.search_form.cleaned_data.get("type")
            part_name = self.search_form.cleaned_data.get("part_name")

            part_code = self.search_form.cleaned_data.get("part_code")
            warehouse_code = self.search_form.cleaned_data.get("warehouse_code")

            part_code_cleaned = clean_code(self.search_form.cleaned_data.get("part_code"))
            warehouse_code_cleaned = clean_code(self.search_form.cleaned_data.get("warehouse_code"))

            warehouse_code_fragment = self.search_form.cleaned_data.get("warehouse_code_fragment")

            box = self.search_form.cleaned_data.get("box")
            location = self.search_form.cleaned_data.get("location")
            has_images = self.search_form.cleaned_data.get("has_images")
            has_stock = self.search_form.cleaned_data.get("has_stock")
            has_advertisements = self.search_form.cleaned_data.get("has_advertisements")
            has_temp_allocations = self.search_form.cleaned_data.get("has_temp_allocations")
            price_is_older_than_6_months = self.search_form.cleaned_data.get("price_is_older_than_6_months")
            engine_code = self.search_form.cleaned_data.get("engine_code")
            attribute = self.search_form.cleaned_data.get("attributes")
            id = self.search_form.cleaned_data.get("id")
            quantity = self.search_form.cleaned_data.get("quantity")
            organization = self.search_form.cleaned_data.get("organization")
            condition = self.search_form.cleaned_data.get("condition")
            created_date_from = self.search_form.cleaned_data.get("created_date_from")
            created_date_to = self.search_form.cleaned_data.get("created_date_to")

            if id:
                queryset = queryset.filter(id=id)
            if created_date_from:
                queryset = queryset.filter(created_at__gte=created_date_from)
            if created_date_to:
                queryset = queryset.filter(created_at__lte=created_date_to)
            if vehicle:
                queryset = queryset.filter(locations__vehicle__code=vehicle)
            if year:
                queryset = queryset.filter(year=year)
            if manufacturer:
                queryset = queryset.filter(manufacturer=manufacturer)
            if model:
                queryset = queryset.filter(model__name__icontains=model)
            if type:
                queryset = queryset.filter(type__name__icontains=type)
            if engine_code:
                queryset = queryset.filter(engine__code__icontains=engine_code)
            if part_name:
                queryset = queryset.filter(
                    Q(categories__name__iexact=part_name)
                    | Q(categories__name_en__iexact=part_name)
                    | Q(categories__name_lt__iexact=part_name)
                )
            if part_code:
                queryset = queryset.filter(Q(code__icontains=part_code) | Q(code_cleaned__icontains=part_code_cleaned))
            if warehouse_code:
                queryset = queryset.filter(
                    Q(warehouse_code__iexact=warehouse_code) | Q(warehouse_code_cleaned__iexact=warehouse_code_cleaned)
                )
            elif warehouse_code_fragment:
                queryset = queryset.filter(
                    Q(warehouse_code__icontains=warehouse_code_fragment)
                    | Q(warehouse_code_cleaned__icontains=warehouse_code_fragment)
                )
            if box:
                queryset = queryset.filter(locations__box__name__icontains=box)
            if location:
                queryset = queryset.filter(locations__location__name__icontains=location)
            if attribute:
                queryset = queryset.filter(
                    Q(attributes__name__icontains=attribute) | Q(attributes__name_lt__icontains=attribute)
                )
            if has_advertisements:
                if has_advertisements == "0":
                    queryset = queryset.filter(advertisments__isnull=True)
                elif has_advertisements == "1":
                    queryset = queryset.exclude(advertisments__isnull=True)
                elif has_advertisements in [i[0] for i in VehiclePartAdvertisment.SHOP_CHOICES]:
                    queryset = queryset.filter(advertisments__shop=has_advertisements)
                elif has_advertisements.startswith("not_") and has_advertisements[4:] in [
                    i[0] for i in VehiclePartAdvertisment.SHOP_CHOICES
                ]:
                    queryset = queryset.exclude(advertisments__isnull=True).exclude(
                        id__in=VehiclePartAdvertisment.objects.filter(shop=has_advertisements[4:]).values(
                            "vehicle_part_id"
                        ),
                    )
            if has_images:
                if has_images == "0":
                    queryset = queryset.filter(images__isnull=True)
                elif has_images == "1":
                    queryset = queryset.exclude(images__isnull=True)
            if has_stock:
                if has_stock == "1":
                    queryset = (
                        queryset.annotate(stock=Sum("locations__num_in_location")).filter(stock__gt=0).order_by("-id")
                    )
                elif has_stock == "0":
                    queryset = (
                        queryset.annotate(stock=Sum("locations__num_in_location")).filter(stock=0).order_by("-id")
                    )
            if quantity:
                queryset = (
                    queryset.annotate(stock=Sum("locations__num_in_location"))
                    .filter(stock__gte=quantity)
                    .order_by("-stock")
                )
            if has_temp_allocations:
                if has_temp_allocations == "1":
                    queryset = queryset.filter(locations__allocations__expires_at__isnull=False)
                elif has_temp_allocations == "0":
                    queryset = queryset.exclude(locations__allocations__expires_at__isnull=False)
            if price_is_older_than_6_months:
                half_year_ago = timezone.now() - relativedelta(months=6)
                if price_is_older_than_6_months == "1":
                    queryset = queryset.filter(advertisments__modified_at__lt=half_year_ago)
                elif price_is_older_than_6_months == "0":
                    queryset = queryset.filter(advertisments__modified_at__gte=half_year_ago)
            if condition:
                queryset = queryset.filter(condition=condition)
            if self.request.user.is_superuser and organization:
                queryset = queryset.filter(organization=organization)

        else:
            if self.request.user.is_superuser:
                organization = Organization.get_user_organization(self.request.user)
                queryset = queryset.filter(organization=organization)

        return queryset.distinct()

    def enrich_object_list(self, object_list):
        """Enhance objects with additional data in a single efficient query"""
        # Only proceed if we have objects to process
        if not object_list:
            return object_list
        
        # Calculate date threshold once
        half_year_ago = timezone.now() - relativedelta(months=6)
        
        # Get IDs of only the current paginated objects
        current_page_ids = [obj.id for obj in object_list]
        
        # Check if we need to calculate price age (only if not already filtered by it)
        price_age_filter = self.search_form.cleaned_data.get("price_is_older_than_6_months") if self.search_form.is_valid() else None
        
        # Only query for old prices if we haven't already filtered by it
        old_price_parts = set()
        if price_age_filter is None:
            # Get the subset of these objects that have old prices
            old_price_parts = set(VehiclePart.objects.filter(
                id__in=current_page_ids,
                advertisments__modified_at__lt=half_year_ago,
            ).values_list("id", flat=True))
        elif price_age_filter == "1":
            # If we filtered for old prices, all results have old prices
            old_price_parts = set(current_page_ids)
        # If price_age_filter == "0", none have old prices, so leave as empty set
        
        # Get the box filter value
        box = self.search_form.cleaned_data.get("box") if self.search_form.is_valid() else None
        
        # Apply the data to each object in memory (no more DB queries)
        for obj in object_list:
            obj.price_is_older_than_6_months = obj.id in old_price_parts
            obj._filter_data_for_box = box
            
        return object_list

    def get_context_data(self, **kwargs):
        """Get context data with optimized object list to avoid redundant queries"""
        context = super(VehiclePartListView, self).get_context_data(**kwargs)
        context["form"] = self.search_form
        
        # Enrich the object list only once and store in context
        # This ensures we only process the paginated results
        if "object_list" in context:
            context["object_list"] = self.enrich_object_list(context["object_list"])
        
        # Add environment information to context
        context["is_live_deployment"] = settings.DEPLOYMENT == settings.DEPLOYMENT_LIVE
        
        return context

class CachedCountPaginator(Paginator):
    """
    Custom paginator that caches the count of objects.
    To avoid recalculating the count of objects on each page load.
    """
    
    def __init__(self, *args, cache_key='paginator_count', cache_timeout=10800, **kwargs):
        """
        Initialize the paginator with a cache key and timeout.
        
        Args:
            cache_key: The key to use for caching the count
            cache_timeout: The timeout in seconds (default: 10800 seconds = 3 hours)
                           This is a longer timeout since count calculations can be expensive
                           and the count doesn't change frequently.
        """
        super().__init__(*args, **kwargs)
        self.cache_key = cache_key
        self.cache_timeout = cache_timeout

    @property
    def count(self):
        count = cache.get(self.cache_key)
        if count is None:
            count = super().count
            cache.set(self.cache_key, count, self.cache_timeout)
        return count

class VehiclePartListAjaxView(VehiclePartListView):
    template_name = "oscar/dashboard/pap/vehicle_part_list_ajax.html"
    paginator_class = CachedCountPaginator

    def get_paginator(self, queryset, per_page, orphans=0, allow_empty_first_page=True):
        # Generate a unique cache_key based on user and filter parameters
        params = self.request.GET.copy()
        if 'page' in params:
            del params['page']

        user_id = self.request.user.id
        filter_string = params.urlencode()
        
        # Get the timestamp that was set when the user opened the page without GET parameters
        # or clicked the "Reset Filters" button
        timestamp = self.request.session.get('vehicle_part_timestamp', '0')
        
        # Create a cache key with the timestamp
        cache_key = f'vehicle_part_list_count_{user_id}_{hashlib.md5(filter_string.encode()).hexdigest()}_{timestamp}'

        # Create a paginator with a dynamic cache_key
        # Using the default cache_timeout of 10800 seconds (3 hours) defined in CachedCountPaginator
        return self.paginator_class(
            queryset,
            per_page,
            cache_key=cache_key
        )
    
    def enrich_paginated_objects(self, vehicle_parts):
        """
        Enrich the paginated vehicle parts with additional data using efficient bulk queries.
        This method fetches all related data for the vehicle parts in a single query per data type.
        
        Args:
            vehicle_parts: List of VehiclePart objects to enrich
            
        Returns:
            The enriched list of vehicle parts
        """
        # Safety check - if no vehicle parts, no need to do any prefetching
        if not vehicle_parts:
            return vehicle_parts
            
        # Get all vehicle part IDs for the current page
        vehicle_part_ids = [vp.id for vp in vehicle_parts]
        
        try:
            # Pre-fetch all related data for the vehicle parts on this page in bulk queries
            # We need to do this in a single query for each data type
            # We will use Raw SQL for this, because it's faster than ORM queries and more efficient with large datasets
            # Skip image prefetching - we'll let the template handle that directly
            
            # Get categories for all vehicle parts in a single query
            from django.utils.translation import get_language
            lang_code = get_language()
            
            # Get all categories for all vehicle parts in a single query
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT pap_vehiclepart_categories.vehiclepart_id, 
                           catalogue_category.name_{lang_code}
                    FROM catalogue_category 
                    INNER JOIN pap_vehiclepart_categories 
                    ON (catalogue_category.id = pap_vehiclepart_categories.category_id) 
                    WHERE pap_vehiclepart_categories.vehiclepart_id IN %s
                    ORDER BY catalogue_category.path ASC
                    """,
                    [tuple(vehicle_part_ids) if len(vehicle_part_ids) > 1 else (vehicle_part_ids[0],)]
                )
                categories_data = cursor.fetchall()
            
            # Organize categories by vehicle part
            categories_by_vehicle_part = {}
            for vp_id, category_name in categories_data:
                if vp_id not in categories_by_vehicle_part:
                    categories_by_vehicle_part[vp_id] = []
                categories_by_vehicle_part[vp_id].append(category_name)
            
            # Pre-fetch attributes for all vehicle parts in a single query
            with connection.cursor() as cursor:
                cursor.execute(
                    f"""
                    SELECT pap_vehiclepart_attributes.vehiclepart_id, 
                           catalogue_productextraattribute.name_{lang_code}
                    FROM catalogue_productextraattribute 
                    INNER JOIN pap_vehiclepart_attributes 
                    ON (catalogue_productextraattribute.id = pap_vehiclepart_attributes.productextraattribute_id) 
                    WHERE pap_vehiclepart_attributes.vehiclepart_id IN %s
                    """,
                    [tuple(vehicle_part_ids) if len(vehicle_part_ids) > 1 else (vehicle_part_ids[0],)]
                )
                attributes_data = cursor.fetchall()
            
            # Organize attributes by vehicle part
            attributes_by_vehicle_part = {}
            for vp_id, attribute_name in attributes_data:
                if vp_id not in attributes_by_vehicle_part:
                    attributes_by_vehicle_part[vp_id] = []
                if attribute_name:  # Only add non-empty attributes
                    attributes_by_vehicle_part[vp_id].append(attribute_name)
            
            # Pre-fetch stock information for all vehicle parts in a single query
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT vehicle_part_id, 
                           SUM(num_in_location) AS total_num_in_locations, 
                           SUM(num_allocated) AS total_num_allocated
                    FROM pap_vehiclepartlocation 
                    WHERE vehicle_part_id IN %s
                    GROUP BY vehicle_part_id
                    """,
                    [tuple(vehicle_part_ids) if len(vehicle_part_ids) > 1 else (vehicle_part_ids[0],)]
                )
                stock_data = cursor.fetchall()
            
            # Organize stock data by vehicle part
            stock_by_vehicle_part = {}
            for vp_id, total_in_location, total_allocated in stock_data:
                stock_by_vehicle_part[vp_id] = (
                    total_in_location if total_in_location else 0,
                    total_allocated if total_allocated else 0
                )
            
            # Pre-fetch temporary allocations information for all vehicle parts in a single query
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT DISTINCT vpl.vehicle_part_id
                    FROM pap_vehiclepartlocation vpl
                    INNER JOIN pap_vehiclepartlocationallocation vpla 
                    ON vpl.id = vpla.vehicle_part_location_id
                    WHERE vpl.vehicle_part_id IN %s
                    AND vpla.expires_at IS NOT NULL
                    """,
                    [tuple(vehicle_part_ids) if len(vehicle_part_ids) > 1 else (vehicle_part_ids[0],)]
                )
                temp_allocations_data = cursor.fetchall()
            
            # Create a set of vehicle part IDs with temporary allocations
            vehicle_parts_with_temp_allocations = {vp_id for vp_id, in temp_allocations_data}
            
            # Pre-fetch all model and type data needed by our vehicle parts in a single query
            # Collect all model and type IDs first
            model_ids = set()
            type_ids = set()
            
            for vehicle_part in vehicle_parts:
                if vehicle_part.model_id:
                    model_ids.add(vehicle_part.model_id)
                if vehicle_part.type_id:
                    type_ids.add(vehicle_part.type_id)
            
            # Fetch all model data in one go
            model_data = {}
            if model_ids:
                with connection.cursor() as cursor:
                    cursor.execute(
                        f"""
                        SELECT id, con_start, con_end, name_{lang_code}
                        FROM tecpap_model
                        WHERE id IN %s
                        """,
                        [tuple(model_ids) if len(model_ids) > 1 else (list(model_ids)[0],)]
                    )
                    for model_id, con_start, con_end, name in cursor.fetchall():
                        model_data[model_id] = {
                            'con_start': con_start,
                            'con_end': con_end,
                            'name': name
                        }
            
            # Fetch all type data in one go
            type_data = {}
            if type_ids:
                with connection.cursor() as cursor:
                    cursor.execute(
                        f"""
                        SELECT id, kw_from, con_start, con_end, name_{lang_code}
                        FROM tecpap_type
                        WHERE id IN %s
                        """,
                        [tuple(type_ids) if len(type_ids) > 1 else (list(type_ids)[0],)]
                    )
                    for type_id, kw_from, con_start, con_end, name in cursor.fetchall():
                        type_data[type_id] = {
                            'kw_from': kw_from,
                            'con_start': con_start,
                            'con_end': con_end,
                            'name': name
                        }
            
            # Fetch all engine data for the types in one go
            engine_data = {}
            if type_ids:
                with connection.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT tt.typ_id, te.code
                        FROM tecpap_typeengine tt
                        INNER JOIN tecpap_engine te ON tt.eng_id = te.id
                        WHERE tt.typ_id IN %s AND tt.active = 1
                        """,
                        [tuple(type_ids) if len(type_ids) > 1 else (list(type_ids)[0],)]
                    )
                    for type_id, engine_code in cursor.fetchall():
                        if type_id not in engine_data:
                            engine_data[type_id] = []
                        if engine_code:
                            engine_data[type_id].append(engine_code)
            
            # Pre-fetch image counts for all vehicle parts in a single query
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT vehicle_part_id, COUNT(*) as image_count
                    FROM pap_vehiclepartimage 
                    WHERE vehicle_part_id IN %s
                    GROUP BY vehicle_part_id
                    """,
                    [tuple(vehicle_part_ids) if len(vehicle_part_ids) > 1 else (vehicle_part_ids[0],)]
                )
                image_count_data = cursor.fetchall()
            
            # Organize image counts by vehicle part
            image_count_by_vehicle_part = {}
            for vp_id, image_count in image_count_data:
                image_count_by_vehicle_part[vp_id] = image_count
                
            # Check for prices older than 6 months for all paginated objects regardless of filtering
            half_year_ago = timezone.now() - relativedelta(months=6)
            
            # Get all vehicle parts with advertisements older than 6 months in a single query
            old_price_part_ids = set(VehiclePart.objects.filter(
                id__in=vehicle_part_ids,
                advertisments__modified_at__lt=half_year_ago
            ).values_list('id', flat=True))
            
            # Now attach the pre-fetched data to each vehicle part
            for vehicle_part in vehicle_parts:
                # Cache categories display
                if vehicle_part.id in categories_by_vehicle_part:
                    vehicle_part.cached_categories_display = "<br>".join(categories_by_vehicle_part[vehicle_part.id])
                else:
                    vehicle_part.cached_categories_display = ""
                
                # Cache attributes display
                if vehicle_part.id in attributes_by_vehicle_part:
                    vehicle_part.cached_attributes_display = "<br>".join(attributes_by_vehicle_part[vehicle_part.id])
                else:
                    vehicle_part.cached_attributes_display = ""
                
                # Cache stock display
                if vehicle_part.id in stock_by_vehicle_part:
                    total_in_location, total_allocated = stock_by_vehicle_part[vehicle_part.id]
                    vehicle_part.cached_stock_display = f"{total_in_location}/{total_allocated}"
                    
                    # Cache has_no_stock property as a separate attribute
                    vehicle_part.cached_has_no_stock = total_in_location == 0
                    
                    # Cache is_whole_stock_allocated property
                    vehicle_part.cached_is_whole_stock_allocated = total_in_location > 0 and total_in_location == total_allocated
                else:
                    vehicle_part.cached_stock_display = "0/0"
                    vehicle_part.cached_has_no_stock = True
                    vehicle_part.cached_is_whole_stock_allocated = False
                
                # Cache image count
                vehicle_part.cached_image_count = image_count_by_vehicle_part.get(vehicle_part.id, 0)
                
                # Cache temporary allocations
                vehicle_part.cached_has_temp_allocations = vehicle_part.id in vehicle_parts_with_temp_allocations
                
                # Cache model data
                if vehicle_part.model_id and vehicle_part.model_id in model_data:
                    vehicle_part.cached_model_data = model_data[vehicle_part.model_id]
                
                # Cache type data
                if vehicle_part.type_id and vehicle_part.type_id in type_data:
                    vehicle_part.cached_type_data = type_data[vehicle_part.type_id]
                    
                    # Cache engine codes
                    if vehicle_part.type_id in engine_data:
                        vehicle_part.cached_engine_codes = engine_data[vehicle_part.type_id]
                
                # Set price_is_older_than_6_months flag based on the bulk query
                vehicle_part.price_is_older_than_6_months = vehicle_part.id in old_price_part_ids
            
        except Exception as e:
            logger.warning(f"Error during prefetching data: {e}")
            # Don't break the page on error, just log and continue with regular rendering
            
        return vehicle_parts

    def get_context_data(self, **kwargs):
        """
        Override get_context_data to add debug information to the context
        and pre-compute all data needed by the template.
        """
        # Get the context data from the parent class
        context = super().get_context_data(**kwargs)
        
        # Get all vehicle part objects for the current page and convert to list
        vehicle_parts = list(context['object_list'])
        
        # Enrich the vehicle parts with additional data
        enriched_vehicle_parts = self.enrich_paginated_objects(vehicle_parts)
        
        # Store the enriched object list back in the context
        context['object_list'] = enriched_vehicle_parts
        
        return context    

class VehiclePartListDebugView(VehiclePartListAjaxView):
    """
    Debug view for vehicle parts list.
    This view renders the same data as the AJAX view but as a full page
    to help with debugging SQL queries with Django Debug Toolbar.
    
    This view uses the parent VehiclePartListView's methods to get the filtered queryset,
    but only processes the items needed for the current page.
    """
    template_name = "oscar/dashboard/pap/vehicle_part_list_debug.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        params = self.request.GET.copy()
        
        # Get the paginator to access its cache_key
        paginator = context.get('paginator')
        if not paginator or not hasattr(paginator, 'cache_key'):
            return context

        user_id = self.request.user.id
        filter_string = params.urlencode()    
        cache_key = paginator.cache_key
        # Get cache_timeout from paginator or use default (3 hours)
        # This is a fallback mechanism in case the paginator object doesn't have a cache_timeout attribute
        cache_timeout = getattr(paginator, 'cache_timeout', 10800)
        cache_exists = cache.get(cache_key) is not None
        
        # Try to get remaining time if cache exists
        remaining_seconds = None
        if cache_exists:
            try:
                # Try Redis cache backend first
                if hasattr(cache, 'client') and hasattr(cache.client, 'ttl'):
                    # This works with Redis cache backend
                    remaining_seconds = cache.client.ttl(cache_key)
                    if remaining_seconds < 0:
                        # If TTL returns -1 (no expiry) or -2 (key doesn't exist), set to None
                        remaining_seconds = None
                # Try Django's built-in cache timeout inspection for other backends
                elif hasattr(cache, '_cache') and hasattr(cache._cache, '_expire_info'):
                    # This works with FileBasedCache
                    expire_time = cache._cache._expire_info.get(cache_key)
                    if expire_time:
                        remaining_seconds = max(0, expire_time - time.time())
                        remaining_seconds = int(remaining_seconds)
            except (AttributeError, NotImplementedError):
                # If cache backend doesn't support any of the above methods
                remaining_seconds = None
        
        # Add cache information to context
        context.update({
            'user_id': user_id,
            'filter_params': filter_string,  # Changed from filter_string to filter_params to match template
            'cache_key': cache_key,
            'cache_timeout': cache_timeout,
            'cache_timeout_human': '3 hours',
            'cache_exists': cache_exists,
            'remaining_seconds': remaining_seconds,
            'remaining_time_human': self._format_remaining_time(remaining_seconds) if remaining_seconds else None
        })
        
        return context
        
    def _format_remaining_time(self, seconds):
        """Format seconds into a human-readable time string"""
        if seconds is None:
            return None
            
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        parts = []
        if hours:
            parts.append(f"{int(hours)} hour{'s' if hours != 1 else ''}")
        if minutes:
            parts.append(f"{int(minutes)} minute{'s' if minutes != 1 else ''}")
        if seconds or not parts:
            parts.append(f"{int(seconds)} second{'s' if seconds != 1 else ''}")
            
        return ", ".join(parts)
        
    def post(self, request, *args, **kwargs):
        """Handle POST requests to clear the cache"""
        if 'clear_cache' in request.POST:
            # Create a temporary paginator to get the cache_key
            queryset = self.get_queryset()
            paginator = self.get_paginator(queryset, self.paginate_by)
            
            # Clear the cache using the paginator's cache_key
            if hasattr(paginator, 'cache_key'):
                cache.delete(paginator.cache_key)
                messages.success(request, _("Cache cleared successfully."))
            
        # Redirect back to the same page
        return HttpResponseRedirect(request.get_full_path())

class VehiclePartMixin(PapMixin):
    form_class = VehiclePartForm
    template_name = "oscar/dashboard/pap/vehicle_part_form.html"
    model = VehiclePart

    def dispatch(self, request, *args, **kwargs):
        # Determine if this is a CreateView or UpdateView
        if isinstance(self, CreateView):
            self.creating = True
        elif isinstance(self, UpdateView):
            self.creating = False
        else:
            self.creating = False  # Default to False if neither
        return super().dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        self.creating = "pk" not in self.kwargs
        if self.creating:
            return None
        else:
            return super(VehiclePartMixin, self).get_object(queryset)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['creating'] = self.creating
        if not self.creating:
            context['can_upload_images'] = True
            context['images'] = self.object.images.all()
        return context

    def handle_advertisements(self):
        error_messages = []
        for adv in self.object.advertisments.all():
            success, error_msg = adv.create_or_update_webshop_product(forced=False)
            if not success:
                error_messages.append(error_msg)
        return error_messages

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.save()
        form.save_m2m()

        if self.creating:
            vehicle = form.cleaned_data.get("vehicle")
            box = form.cleaned_data.get("box")
            box_qty = form.cleaned_data.get("box_qty")

            if box_qty:
                VehiclePartLocation.objects.create(
                    vehicle=vehicle,
                    vehicle_part=self.object,
                    box_id=box,
                    num_in_location=box_qty,
                    owner=self.object.owner,
                    organization=self.object.organization,
                )

        error_messages = self.handle_advertisements()

        # Determine the action based on the submitted button
        action = self.request.POST.get('action')

        if self.creating:
            # Actions available during creation
            if action == 'save_and_close':
                redirect_url = reverse("dashboard:vehicle-part-list")
                msg = _("Vehicle part '%s' created successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            elif action == 'save_and_create_new':
                redirect_url = reverse("dashboard:vehicle-part-add")
                msg = _("Vehicle part '%s' created successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            elif action == 'save_and_continue':
                redirect_url = reverse("dashboard:vehicle-part-edit", kwargs={"pk": self.object.pk})
                msg = _("Vehicle part '%s' created successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            else:
                messages.warning(self.request, msg)
                return JsonResponse({'status': 'error', 'message': 'Invalid action.'}, status=400)
        else:
            # Actions available during update
            if action == 'save_and_close':
                redirect_url = reverse("dashboard:vehicle-part-list")
                msg = _("Vehicle part '%s' updated successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            elif action == 'save_and_create_new':
                redirect_url = reverse("dashboard:vehicle-part-add")
                msg = _("Vehicle part '%s' updated successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            elif action == 'save_and_advertise':
                redirect_url = reverse("dashboard:vehicle-part-advertisments", kwargs={"pk": self.object.pk})
                msg = _("Vehicle part '%s' updated successfully") % self.object.code
                messages.success(self.request, msg)
                status = 'success'
            else:
                return JsonResponse({'status': 'error', 'message': 'Invalid action.'}, status=400)

        if any(error_messages):
            msg = _('Vehicle part "%s" updated, but there were issues with advertisements') % self.object.code
            messages.warning(self.request, msg)
            return JsonResponse(
                {'status': 'warning', 'message': msg, 'errors': error_messages, 'redirect_url': redirect_url}
            )
        else:
            return JsonResponse({'status': status, 'message': msg, 'redirect_url': redirect_url})

    def form_invalid(self, form):
        errors = form.errors.get_json_data()
        return JsonResponse({'status': 'error', 'errors': errors}, status=400)


class VehiclePartTitleSearchView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        search_text = request.GET.get("q")
        category_names = []
        if search_text:
            vehicle_parts = (
                VehiclePart.objects.filter(
                    Q(categories__name__icontains=search_text) | Q(categories__name_lt__icontains=search_text)
                )
                .annotate(stock=Sum("locations__num_in_location"))
                .filter(stock__gt=0)
            )
            if not request.user.is_superuser:
                organization = Organization.get_user_organization(request.user)
                vehicle_parts = vehicle_parts.filter(organization=organization)
            lang_code = get_language()
            category_names = vehicle_parts.values_list("categories__name_{}".format(lang_code), flat=True)
            category_names = [{"id": i, "value": i, "label": i} for i in sorted(list(set(category_names)))]
        return HttpResponse(json.dumps(category_names), content_type="application/json")


class VehiclePartAttributeSearchView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        search_text = request.GET.get("q")
        category_names = []
        if search_text:
            vehicle_parts = (
                VehiclePart.objects.filter(
                    Q(attributes__name__icontains=search_text) | Q(attributes__name_lt__icontains=search_text)
                )
                .annotate(stock=Sum("locations__num_in_location"))
                .filter(stock__gt=0)
            )
            if not request.user.is_superuser:
                organization = Organization.get_user_organization(request.user)
                vehicle_parts = vehicle_parts.filter(organization=organization)
            lang_code = get_language()
            attribute_names = vehicle_parts.values_list("attributes__name_{}".format(lang_code), flat=True)
            attribute_names = [{"id": i, "value": i, "label": i} for i in sorted(list(set(attribute_names)))]
        return HttpResponse(json.dumps(attribute_names), content_type="application/json")


class VehiclePartCreateView(VehiclePartMixin, CreateView):
    pass


class VehiclePartUpdateView(ImageHandlingMixin, VehiclePartMixin, UpdateView):
    pass


class VehiclePartDeleteView(PapMixin, DeleteView):
    template_name = "oscar/dashboard/pap/vehicle_part_delete.html"
    model = VehiclePart
    success_url = reverse_lazy("dashboard:vehicle-part-list")


class VehiclePartsByLocationView(PapMixin, ListView):
    template_name = "oscar/dashboard/pap/vehicle_part_collection.html"
    model = VehiclePart
    context_object_name = "vehicle_parts"

    def get(self, *args, **kwargs):
        location_id = self.kwargs.get("pk")
        organization_id = Organization.get_user_organization(self.request.user).id
        parts = (
            VehiclePart.objects.filter(locations__location_id=location_id, organization_id=organization_id)
            .select_related("manufacturer", "model")
            .prefetch_related("categories", "attributes")
        )
        if parts:
            return render(self.request, self.template_name, {"parts": parts, "location": location_id})
        else:
            raise Http404


class VehiclePartDetailView(PapMixin, DetailView):
    template_name = "oscar/dashboard/pap/vehicle_part_details.html"
    model = VehiclePart


class VehiclePartImagesAjaxView(ImageHandlingMixin, VehiclePartMixin, UpdateView):
    form_class = VehiclePartImagesUpdateForm
    template_name = "oscar/dashboard/pap/vehicle_part_images_ajax.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['images'] = self.object.images.all()
        return context


class VehiclePartRemoveTempAllocationsAjaxView(VehiclePartMixin, View):
    def get(self, *args, **kwargs):
        vehicle_part = VehiclePart.objects.get(id=self.kwargs["pk"])
        num_updated = 0
        for location in vehicle_part.locations.all():
            if location.allocations.filter(expires_at__isnull=False):
                for allocation in location.allocations.filter(expires_at__isnull=False):
                    allocation.num_allocated = 0
                    allocation.expires_at = None
                    allocation.save()
                    location.num_allocated -= 1
                    location.save()
                    num_updated += 1
                    self.log_action(content_type=allocation._meta.model_name, pk=allocation.pk)
        if num_updated > 0:
            vehicle_part.update_stock_for_webshops()

        return HttpResponse("OK")


class VehiclePartDetailAjaxView(VehiclePartDetailView):
    template_name = "oscar/dashboard/pap/vehicle_part_details_ajax.html"


class VehiclePartDetailDownloadView(PapMixin, DetailView):
    model = VehiclePart

    available_fields = [
        ("id", _("Nr.")),
        ("get_categories_display", _("Title")),
        ("code", _("Code")),
        ("manufacturer", _("Manufacturer")),
        ("model", _("Model")),
        ("type", _("Type")),
        ("engine", _("Engine")),
        ("warehouse_code", _("Warehouse code")),
        ("get_stock_display", _("Q-ty")),
        ("get_location_display", _("Location")),
        ("get_price_display", _("Price")),
    ]

    def get(self, request, *args, **kwargs):
        vehicle_part = self.get_object()

        output = BytesIO()
        doc = SimpleDocTemplate(
            output,
            pagesize=A4,
            rightMargin=40,
            leftMargin=40,
            topMargin=20,
            bottomMargin=15,
        )
        styles = getSampleStyleSheet()
        styles.add(
            ParagraphStyle(
                name="LogoC",
                fontSize=24,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(
            ParagraphStyle(
                name="HeaderC",
                fontSize=20,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(
            ParagraphStyle(
                name="SubHeaderC",
                fontSize=14,
                fontName="open_sans",
                alignment=TA_CENTER,
            )
        )
        styles.add(ParagraphStyle(name="NormalR", fontSize=10, fontName="open_sans", alignment=TA_RIGHT))
        styles.add(ParagraphStyle(name="NormalJ", fontSize=10, fontName="open_sans", alignment=TA_JUSTIFY))
        styles.add(ParagraphStyle(name="NormalC", fontSize=10, fontName="open_sans", alignment=TA_CENTER))
        styles.add(
            ParagraphStyle(
                name="NormalRB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_RIGHT,
            )
        )
        styles.add(
            ParagraphStyle(
                name="NormalJB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_JUSTIFY,
            )
        )
        styles.add(
            ParagraphStyle(
                name="NormalCB",
                fontSize=10,
                fontName="open_sans_bold",
                alignment=TA_CENTER,
            )
        )
        styles.add(ParagraphStyle(name="SmallC", fontSize=8, fontName="open_sans", alignment=TA_CENTER))

        content = []

        logo = Image(
            os.path.join(settings.PROJECT_MODULE, "static", "img", "invoice_logo_pap.jpg"),
            width=6 * cm,
            height=2.2 * cm,
        )
        content.append(logo)
        content.append(Paragraph("_" * 140, styles["SmallC"]))
        content.append(Paragraph(str(_("Vehicle part %s") % vehicle_part.code), styles["HeaderC"]))
        content.append(Spacer(1, 24))
        content.append(Paragraph(timezone.now().strftime("%Y-%m-%d"), styles["NormalC"]))
        content.append(Spacer(1, 24))

        table_rows = []

        for field, label in self.available_fields:
            attr = getattr(vehicle_part, field)
            if callable(attr):
                value = attr()
            else:
                value = attr
            value = str(value).replace("<br>", "\n")
            table_rows.append(
                [
                    Paragraph("{}:".format(str(label)), styles["NormalJB"]),
                    Paragraph(value, styles["NormalJ"]),
                ]
            )

        if vehicle_part.images.all().count() > 0:
            image_path = vehicle_part.images.all()[0].image.path
            if os.path.exists(image_path) and image_path.rpartition(".")[-1].lower() in ["jpg", "jpeg", "png"]:
                image = Image(image_path, 7.5 * cm, 6 * cm)
                table_rows.append(
                    [
                        Paragraph("{}:".format(str(_("Image"))), styles["NormalJB"]),
                        image,
                    ]
                )

        table = Table(
            table_rows,
            colWidths=[4 * cm, 10 * cm],
            style=[
                ("VALIGN", (0, 0), (-1, -1), "TOP"),
            ],
        )

        content.append(table)

        doc.build(content)
        file_content = output.getvalue()
        file_name = "vehicle-part-{}.pdf".format(vehicle_part.id)
        response = HttpResponse(file_content, content_type="application/pdf")
        response["Content-Disposition"] = "attachment; filename={}".format(file_name)
        return response


class VehiclePartStockMutationListView(PapMixin, ListView):
    template_name = "oscar/dashboard/pap/vehicle_part_stock_mutations.html"
    model = VehiclePartStockMutation
    search_form_class = VehiclePartStockMutationSearchForm

    def dispatch(self, request, *args, **kwargs):
        if request.GET:
            self.search_form = self.search_form_class(request.GET, user=request.user)
        else:
            return HttpResponseRedirect(
                f"{reverse('dashboard:vehicle-part-stock-mutation-list')}?date_from={timezone.now().date()}&date_to={timezone.now().date()}"
            )
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        initial_queryset = super().get_queryset()
        if self.request.user.is_superuser:
            organization = Organization.get_user_organization(self.request.user)
            initial_queryset = initial_queryset.filter(organization=organization)
        else:
            initial_queryset = initial_queryset.filter(owner=self.request.user)

        queryset = initial_queryset

        if self.search_form.is_valid():
            manager = self.search_form.cleaned_data.get("manager")
            date_from = self.search_form.cleaned_data.get("date_from")
            date_to = self.search_form.cleaned_data.get("date_to")
            part_code = self.search_form.cleaned_data.get("part_code")
            warehouse_code = self.search_form.cleaned_data.get("warehouse_code")

            if manager:
                queryset = queryset.filter(owner=manager)
            if date_from:
                queryset = queryset.filter(
                    created_at__gte=datetime.datetime.combine(date_from, datetime.time(0, 0, 0))
                )
            if date_to:
                queryset = queryset.filter(
                    created_at__lte=datetime.datetime.combine(date_to, datetime.time(23, 59, 59))
                )
            if part_code:
                queryset = queryset.filter(vehicle_part_location__vehicle_part__code__icontains=part_code)
            if warehouse_code:
                queryset = queryset.filter(vehicle_part_location__vehicle_part__warehouse_code__icontains=warehouse_code)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["form"] = self.search_form
        context["total_amount"] = sum(
            [i.quantity * i.price * (-1 if i.mutation == i.MUTATION_RETURN else 1) for i in self.get_queryset()]
        )
        return context


class VehiclePartStockMutationCreateView(PapMixin, CreateView):
    template_name = "oscar/dashboard/pap/vehicle_part_stock_mutation_form.html"
    form_class = VehiclePartStockMutationForm
    model = VehiclePartStockMutation

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if self.request.method == "POST":
            kwargs["mutation"] = self.request.POST.get("mutation")
            kwargs["location"] = self.request.POST.get("location")
        else:
            kwargs["mutation"] = None
            kwargs["location"] = None
        return kwargs

    def form_valid(self, form):
        vehicle = form.cleaned_data["vehicle"]
        quantity = form.cleaned_data["quantity"]
        price = form.cleaned_data["price"]
        mutation = form.cleaned_data["mutation"]
        location = form.cleaned_data["location"]

        if mutation == VehiclePartStockMutation.MUTATION_SALES:
            if not location:
                vehicle_part = form.save()
                form.save_m2m()

                vehicle_part_location = VehiclePartLocation.objects.create(
                    vehicle=vehicle,
                    vehicle_part=vehicle_part,
                    location=None,
                    box_id=None,
                    num_in_location=0,
                    owner=self.request.user,
                    organization=vehicle_part.organization,
                )
            else:
                vehicle_part_location = location
                vehicle_part = location.vehicle_part

                if vehicle_part_location.allocations.filter(expires_at__isnull=False).exists():
                    messages.error(
                        self.request,
                        _("Please check reservations for vehicle part location %(vehicle_part)s (%(location)s)!")
                        % {"vehicle_part": vehicle_part, "location": location},
                    )
                    return super().form_invalid(form)

                free_stock = vehicle_part_location.num_in_location - vehicle_part_location.num_allocated
                if free_stock >= quantity:
                    vehicle_part_location.num_in_location -= quantity
                    vehicle_part_location.save()
                    vehicle_part.update_stock_for_webshops()
                else:
                    messages.error(
                        self.request,
                        _("You are not allowed to sell %(quantity)s items. Max. available qty is %(free_stock)s!")
                        % {"quantity": quantity, "free_stock": free_stock},
                    )
                    return super().form_invalid(form)

        elif mutation == VehiclePartStockMutation.MUTATION_RETURN:
            if not location:
                vehicle_part = form.save()
                form.save_m2m()

                vehicle_part_location = VehiclePartLocation.objects.create(
                    vehicle=vehicle,
                    vehicle_part=vehicle_part,
                    location=None,
                    box_id=None,
                    num_in_location=quantity,
                    owner=self.request.user,
                    organization=vehicle_part.organization,
                )
            else:
                vehicle_part_location = location
                vehicle_part = location.vehicle_part
                vehicle_part_location.num_in_location += quantity
                vehicle_part_location.save()
                vehicle_part.update_stock_for_webshops()

        VehiclePartStockMutation.objects.create(
            vehicle_part_location=vehicle_part_location,
            quantity=quantity,
            price=price,
            owner=self.request.user,
            organization=vehicle_part.organization,
            mutation=mutation,
        )

        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        msg = _("Created vehicle part local sale '%s'") % self.object
        messages.success(self.request, msg)
        url = reverse("dashboard:vehicle-part-stock-mutation-list")
        return url

class VehiclePartLocationView(PapMixin, DetailView):
    template_name = "oscar/dashboard/pap/vehicle_part_locations.html"
    model = VehiclePart

    def dispatch(self, request, *args, **kwargs):
        if request.method == "POST":
            self.new_location_form = VehiclePartLocationForm(request.POST, user=request.user)
        else:
            self.new_location_form = VehiclePartLocationForm(user=request.user)
        return super(VehiclePartLocationView, self).dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.new_location_form.is_valid():
            new_location = self.new_location_form.save(commit=False)
            new_location.vehicle_part = self.object
            new_location.save()
            new_location.vehicle_part.update_stock_for_webshops()
            self.log_action(
                action="VehiclePartLocationCreate",
                content_type=new_location._meta.model_name,
                pk=new_location.pk,
            )
        else:
            return self.render_to_response(self.get_context_data(object=self.get_object()))
        return HttpResponseRedirect(reverse("dashboard:vehicle-part-locations", args=[self.kwargs["pk"]]))

    def get_context_data(self, **kwargs):
        context = super(VehiclePartLocationView, self).get_context_data(**kwargs)
        context["locations"] = self.object.locations.select_related("location", "box", "vehicle")
        context["new_location_form"] = self.new_location_form
        return context


class VehiclePartLocationUpdateView(PapMixin, UpdateView):
    form_class = VehiclePartLocationForm
    template_name = "oscar/dashboard/pap/vehicle_part_location_form.html"
    model = VehiclePartLocation

    def form_valid(self, form):
        response = super(VehiclePartLocationUpdateView, self).form_valid(form)
        self.object.vehicle_part.update_stock_for_webshops()
        return response

    def get_success_url(self):
        return reverse("dashboard:vehicle-part-locations", args=[self.object.vehicle_part_id])


class VehiclePartLocationQtyIncreaseAjaxView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        object = VehiclePartLocation.objects.get(id=self.kwargs["pk"])
        object.num_in_location += 1
        object.save()
        object.vehicle_part.update_stock_for_webshops()

        self.log_action(content_type=object._meta.model_name, pk=object.pk)

        data = {"qty": object.num_in_location}
        return HttpResponse(json.dumps(data), content_type="application/json")


class VehiclePartLocationQtyDecreaseAjaxView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        object = VehiclePartLocation.objects.get(id=self.kwargs["pk"])
        if object.num_in_location > object.num_allocated and object.num_in_location > 0:
            object.num_in_location -= 1
            object.save()
            object.vehicle_part.update_stock_for_webshops()

        self.log_action(content_type=object._meta.model_name, pk=object.pk)

        data = {"qty": object.num_in_location}
        return HttpResponse(json.dumps(data), content_type="application/json")


class VehiclePartLocationQtyAllocatedIncreaseAjaxView(PapMixin, View):
    def post(self, request, *args, **kwargs):
        object = VehiclePartLocation.objects.get(id=self.kwargs["pk"])
        object.num_allocated += 1
        object.save()

        notes = request.POST.get('notes')

        # add one allocation for vehicle part location
        vpa = VehiclePartLocationAllocation.objects.create(
            vehicle_part_location=object,
            num_allocated=1,
            expires_at=timezone.now() + datetime.timedelta(days=365),
            notes=notes,
        )

        object.vehicle_part.update_stock_for_webshops()

        self.log_action(content_type=vpa._meta.model_name, pk=vpa.pk)

        data = {"qtyAllocated": object.num_allocated, "notes": vpa.notes}
        return HttpResponse(json.dumps(data), content_type="application/json")


class VehiclePartLocationQtyAllocatedDecreaseAjaxView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        object = VehiclePartLocation.objects.get(id=self.kwargs["pk"])
        if object.allocations.filter(expires_at__isnull=False).count() > 0:
            # remove one allocation for vehicle part location
            allocation = object.allocations.filter(expires_at__isnull=False)[0]
            allocation.num_allocated = 0
            allocation.expires_at = None
            allocation.save()

            object.num_allocated -= 1
            object.save()

            object.vehicle_part.update_stock_for_webshops()

            self.log_action(content_type=allocation._meta.model_name, pk=allocation.pk)

        data = {"qtyAllocated": object.num_allocated}
        return HttpResponse(json.dumps(data), content_type="application/json")


class VehiclePartLocationDeleteView(PapMixin, DeleteView):
    template_name = "oscar/dashboard/pap/vehicle_part_location_delete.html"
    model = VehiclePartLocation

    def get_success_url(self):
        self.object.vehicle_part.update_stock_for_webshops()
        return reverse("dashboard:vehicle-part-locations", args=[self.object.vehicle_part_id])

    def dispatch(self, request, *args, **kwargs):
        object = self.get_object()
        if object.num_allocated:
            raise SuspiciousOperation("This action is not allowed")
        return super(VehiclePartLocationDeleteView, self).dispatch(request, *args, **kwargs)


class VehiclePartLocationSearchJSONView(View):
    def get(self, request, *args, **kwargs):
        vehicle_part_code = request.GET.get("vehicle_part_code")
        vehicle_part_locations_json = []

        if vehicle_part_code:
            vehicle_part_locations = (
                VehiclePartLocation.objects.select_related("location", "box", "vehicle", "vehicle_part")
                .prefetch_related("vechicle_part__categories")
                .filter(
                    Q(vehicle_part__code__iexact=vehicle_part_code)
                    | Q(vehicle_part__warehouse_code__iexact=vehicle_part_code)
                )
                .order_by("-num_in_location")
            )
            if not self.request.user.is_superuser:
                organization = Organization.get_user_organization(self.request.user)
                vehicle_part_locations = vehicle_part_locations.filter(organization=organization)

            vehicle_part_locations = vehicle_part_locations.values(
                "id",
                "num_in_location",
                "num_allocated",
                "location_id",
                "location__name",
                "box_id",
                "box__name",
                "vehicle_id",
                "vehicle__code",
                "vehicle_part__year",
                "vehicle_part__manufacturer_id",
                "vehicle_part__model_id",
                "vehicle_part__weight",
                "vehicle_part__warehouse_code",
                "vehicle_part__categories",
                "vehicle_part__code",
                "vehicle_part__warehouse_code",
            )
            for vehicle_part_location in vehicle_part_locations:
                num_in_location = vehicle_part_location["num_in_location"]
                num_allocated = vehicle_part_location["num_allocated"]
                location_name = (
                    vehicle_part_location["location__name"]
                    if vehicle_part_location["location__name"]
                    else "no location"
                )
                vehicle_code = (
                    vehicle_part_location["vehicle__code"]
                    if vehicle_part_location["vehicle__code"]
                    else "[location has no vehicle!]"
                )
                box_name = vehicle_part_location["box__name"] if vehicle_part_location["box__name"] else "no box"
                vehicle_part_location["name"] = (
                    f"{location_name}, {box_name}, {vehicle_code}, ({num_in_location}/{num_allocated})"
                )
                vehicle_part_location["vehicle_part__weight"] = str(vehicle_part_location["vehicle_part__weight"])
                vehicle_part_locations_json.append(vehicle_part_location)
        return HttpResponse(json.dumps(vehicle_part_locations_json), content_type="application/json")


@method_decorator(cache_control(no_cache=True, must_revalidate=True, no_store=True), name='dispatch')
class VehiclePartAdvertismentView(PapMixin, DetailView):
    template_name = "oscar/dashboard/pap/vehicle_part_advertisments.html"
    model = VehiclePart

    advertisment_forms = {
        settings.SHOP_PARTAN: {
            "class": VehiclePartAdvertismentFormForPartan,
            "form": None,
            "sort": 1,
        },
        settings.SHOP_RRR: {
            "class": VehiclePartAdvertismentFormForRrr,
            "form": None,
            "sort": 2,
        },
        settings.SHOP_EBAY: {
            "class": VehiclePartAdvertismentFormForEbay,
            "form": None,
            "sort": 3,
        },
        settings.SHOP_RECAR: {
            "class": VehiclePartAdvertismentFormForRecar,
            "form": None,
            "sort": 4,
        },
    }

    def dispatch(self, request, *args, **kwargs):
        self.advertisment_form = None
        self.object = self.get_object()

        if request.method == "POST":
            self.shop = request.POST.get("shop")
            self.advertisment_forms[self.shop]["form"] = self.advertisment_forms[self.shop]["class"](
                request.POST, user=request.user, vehicle_part=self.object
            )
            self.advertisment_form = self.advertisment_forms[self.shop]["form"]
        else:
            for form_key in list(self.advertisment_forms.keys()):
                self.advertisment_forms[form_key]["form"] = self.advertisment_forms[form_key]["class"](
                    user=request.user, vehicle_part=self.object
                )

        return super(VehiclePartAdvertismentView, self).dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        if self.advertisment_form.is_valid():
            new_advertisment = self.advertisment_form.save(commit=False)
            new_advertisment.vehicle_part = self.object
            new_advertisment.manager = self.request.user

            # For eBay, don't save until create_or_update_webshop_product succeeds
            if new_advertisment.shop == settings.SHOP_EBAY:
                success, error_msg = new_advertisment.create_or_update_webshop_product(forced=True)
                if success:
                    new_advertisment.save()
                    messages.success(
                        request,
                        _("Successfully created %(shop)s product") % {
                            "shop": new_advertisment.get_shop_display(),
                        },
                    )
                else:
                    messages.error(
                        request,
                        _("Failed to create or update %(shop)s product, failure reason: %(reason)s")
                        % {
                            "shop": new_advertisment.get_shop_display(),
                            "reason": error_msg,
                        },
                    )
                    if request.POST.get('batch_upload'):
                        return JsonResponse({'status': 'error', 'message': error_msg})
                    return self.render_to_response(self.get_context_data(object=self.get_object()))
            else:
                # For other shops, keep existing behavior
                new_advertisment.save()
                success, error_msg = new_advertisment.create_or_update_webshop_product(forced=True)
                if not success:
                    if new_advertisment.shop == settings.SHOP_PARTAN:
                        # delete advertisment in case of Partan error
                        new_advertisment.delete()
                        messages.error(
                            request,
                            _("Failed to create or update %(shop)s product, failure reason: %(reason)s")
                            % {
                                "shop": new_advertisment.get_shop_display(),
                                "reason": error_msg,
                            },
                        )
                        if request.POST.get('batch_upload'):
                            return JsonResponse({'status': 'error', 'message': error_msg})
                        return self.render_to_response(self.get_context_data(object=self.get_object()))
                    else:
                        messages.error(
                            request,
                            _("Created %(shop)s product with some failures: %(reason)s")
                            % {
                                "shop": new_advertisment.get_shop_display(),
                                "reason": error_msg,
                            },
                        )
                        if request.POST.get('batch_upload'):
                            return JsonResponse({'status': 'error', 'message': error_msg})
                        return self.render_to_response(self.get_context_data(object=self.get_object()))
                messages.success(
                    request,
                    _("Successfully created %(shop)s product")
                    % {
                        "shop": new_advertisment.get_shop_display(),
                    },
                )

                # Return JSON only for batch upload
                if request.POST.get('batch_upload'):
                    return JsonResponse({'status': 'success'})

            # Clear form data from session to prevent persistence
            #if 'advertisment_form_data' in request.session:
            #    del request.session['advertisment_form_data']
                
            return HttpResponseRedirect(reverse("dashboard:vehicle-part-advertisments", args=[self.kwargs["pk"]]))

        # Form is not valid
        for field, errors in self.advertisment_form.errors.items():
            for error in errors:
                messages.error(request, f"{field}: {error}")

        if request.POST.get('batch_upload'):
            return JsonResponse({'status': 'error', 'message': 'Form validation failed'})

        return self.render_to_response(self.get_context_data(object=self.get_object()))

    def get_context_data(self, **kwargs):
        context = super(VehiclePartAdvertismentView, self).get_context_data(**kwargs)
        context["advertisment_forms"] = sorted(list(self.advertisment_forms.items()), key=lambda x: x[1]["sort"])
        context["is_live_deployment"] = settings.DEPLOYMENT == settings.DEPLOYMENT_LIVE
        return context


class VehiclePartAdvertismentProductDeleteView(PapMixin, View):
    model = VehiclePartAdvertisment

    def get(self, request, *args, **kwargs):
        object = self.get_queryset().get(id=kwargs["pk"])
        vehicle_part_id = object.vehicle_part_id
        success, error_msg = object.delete_webshop_product()
        if success:
            object.delete()
            messages.success(
                self.request,
                _("RRR product and advertisment deleted successfully"),
            )
        else:
            messages.error(
                self.request,
                _("Failed to delete RRR product, failure reason: %(reason)s") % {"reason": error_msg},
            )
        return HttpResponseRedirect(reverse("dashboard:vehicle-part-advertisments", args=[vehicle_part_id]))


class VehiclePartAdvertismentPriceUpdateView(PapMixin, View):
    model = VehiclePartAdvertisment

    def post(self, request, *args, **kwargs):
        object = self.get_queryset().get(id=kwargs["pk"])
        new_price = request.POST.get("new_price").replace(',', '.')
        object.price = Decimal(new_price)
        object.save()
        success, error_msg = object.create_or_update_webshop_product(forced=True, update_price_only=True)
        if not success:
            err = (_("Failed to update price, failure reason: %(reason)s") % {"reason": error_msg},)
            return HttpResponse(err, status=400)
        return HttpResponse("OK")


class VehiclePartAdvertismentUpdateView(PapMixin, UpdateView):
    template_name = "oscar/dashboard/pap/vehicle_part_advertisment_form.html"
    model = VehiclePartAdvertisment

    advertisment_forms = {
        settings.SHOP_PARTAN: {
            "class": VehiclePartAdvertismentUpdateFormForPartan,
            "form": None,
        },
        settings.SHOP_EBAY: {
            "class": VehiclePartAdvertismentUpdateFormForEbay,
            "form": None,
        },
        settings.SHOP_RRR: {
            "class": VehiclePartAdvertismentUpdateFormForRrr,
            "form": None,
        },
        settings.SHOP_RECAR: {
            "class": VehiclePartAdvertismentUpdateFormForRecar,
            "form": None,
        },
    }

    def get_success_url(self):
        return reverse("dashboard:vehicle-part-advertisments", args=[self.object.vehicle_part_id])

    def get_form_class(self):
        return self.advertisment_forms[self.object.shop]["class"]

    def get_form_kwargs(self):
        kwargs = super(VehiclePartAdvertismentUpdateView, self).get_form_kwargs()
        kwargs["vehicle_part"] = self.object.vehicle_part
        return kwargs

    def form_valid(self, form):
        self.object = form.save()
        # self.object.manager = self.request.user
        # self.object.save()
        success, error_msg = self.object.create_or_update_webshop_product(forced=True)
        if not success:
            messages.error(
                self.request,
                _("Failed to create or update %(shop)s product, failure reason: %(reason)s")
                % {"shop": self.object.get_shop_display(), "reason": error_msg},
            )
            return self.form_invalid(form)
        return super(VehiclePartAdvertismentUpdateView, self).form_valid(form)


class LocationListView(PapMixin, ListView):
    search_form_class = SimpleSearchForm
    template_name = "oscar/dashboard/pap/location_list.html"
    model = Location

    def dispatch(self, request, *args, **kwargs):
        if request.method == "POST":
            self.search_form = self.search_form_class(request.POST)
        else:
            self.search_form = self.search_form_class(request.GET)
        return super(LocationListView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = super(LocationListView, self).get_queryset()
        if self.search_form.is_valid():
            name = self.search_form.cleaned_data.get("name")
            if name:
                queryset = queryset.filter(name__icontains=name)
        return queryset

    def get_context_data(self, **kwargs):
        context = super(LocationListView, self).get_context_data(**kwargs)
        context["form"] = self.search_form
        return context


class LocationMixin(PapMixin):
    form_class = LocationForm
    template_name = "oscar/dashboard/pap/location_form.html"
    model = Location

    def get_object(self, queryset=None):
        self.creating = "pk" not in self.kwargs
        if self.creating:
            return None  # success
        else:
            object = super(LocationMixin, self).get_object(queryset)
            return object

    def get_success_url(self):
        if self.creating:
            msg = _("Created location '%s'") % self.object
        else:
            msg = _("Updated location '%s'") % self.object
        messages.success(self.request, msg)
        url = reverse("dashboard:location-list")
        return url


class LocationCreateView(LocationMixin, CreateView):
    pass


class LocationUpdateView(LocationMixin, UpdateView):
    def form_valid(self, form):
        response = super().form_valid(form)
        location = form.cleaned_data["location"]
        if location:
            for part_location in self.object.vehicle_part_locations.all():
                self.log_action(
                    action=f"Moved item {part_location.vehicle_part.code} ({part_location.pk}) from location {self.object.name} ({self.object.pk}) to location {location.name} ({location.pk})",
                    content_type=self.object._meta.model_name,
                    pk=self.object.pk
                )
            number = self.object.vehicle_part_locations.update(location=location)
            messages.success(self.request, _('Moved "%(number)s" item(s) to "%(location)s"') % {"number": number, "location": location})
        return response


class LocationDeleteView(PapMixin, DeleteView):
    template_name = "oscar/dashboard/pap/location_delete.html"
    model = Location
    success_url = reverse_lazy("dashboard:location-list")


class BoxListView(PapMixin, ListView):
    search_form_class = BoxSearchForm
    template_name = "oscar/dashboard/pap/box_list.html"
    model = Box

    def dispatch(self, request, *args, **kwargs):
        if request.method == "POST":
            self.search_form = self.search_form_class(request.POST, user=request.user)
        else:
            self.search_form = self.search_form_class(request.GET, user=request.user)
        return super(BoxListView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = super(BoxListView, self).get_queryset()
        if self.search_form.is_valid():
            name = self.search_form.cleaned_data.get("name")
            location = self.search_form.cleaned_data.get("location")
            if name:
                queryset = queryset.filter(name__icontains=name)
            if location:
                queryset = queryset.filter(vehicle_part_locations__location=location).distinct()
        return queryset

    def get_context_data(self, **kwargs):
        context = super(BoxListView, self).get_context_data(**kwargs)
        context["form"] = self.search_form
        return context


class BoxMixin(PapMixin):
    form_class = BoxForm
    template_name = "oscar/dashboard/pap/box_form.html"
    model = Box

    def get_object(self, queryset=None):
        self.creating = "pk" not in self.kwargs
        if self.creating:
            return None  # success
        else:
            object = super(BoxMixin, self).get_object(queryset)
            return object

    def get_success_url(self):
        if self.creating:
            msg = _("Created box '%s'") % self.object
        else:
            msg = _("Updated box '%s'") % self.object
        messages.success(self.request, msg)
        url = reverse("dashboard:box-list")
        return url


class BoxCreateView(BoxMixin, CreateView):
    def form_valid(self, form):
        name = form.cleaned_data["name"]
        number_size = form.cleaned_data.get("number_size")
        number_from = form.cleaned_data.get("number_from")
        number_to = form.cleaned_data.get("number_to")
        form_kwargs = self.get_form_kwargs()
        form_data = form_kwargs["data"].copy()
        if all([number_size, number_from, number_to]):
            for i in range(number_from, number_to + 1):
                _name = "{}{}".format(name, str(i).zfill(number_size))
                form_data["name"] = _name
                form_kwargs["data"] = form_data
                _form = self.form_class(**form_kwargs)
                if _form.is_valid():
                    _form.save()
            messages.success(
                self.request,
                _("Created %s box record(s)") % (number_to - number_from + 1),
            )
            return HttpResponseRedirect(reverse("dashboard:box-list"))
        return super(BoxCreateView, self).form_valid(form)


class BoxUpdateView(BoxMixin, UpdateView):
    def form_valid(self, form):
        response = super().form_valid(form)
        location = form.cleaned_data["location"]
        box = form.cleaned_data["box"]
        if location:
            number = self.object.vehicle_part_locations.update(location=location)
            messages.success(self.request, _('Updated %s "Vehicle Part Location" record(s)') % number)
        if box:
            for part_location in self.object.vehicle_part_locations.all():
                self.log_action(
                    action=f"Moved item {part_location.vehicle_part.code} ({part_location.pk}) from box {self.object.name} ({self.object.pk}) to box {box.name} ({box.pk})",
                    content_type=self.object._meta.model_name,
                    pk=self.object.pk
                )
            number = self.object.vehicle_part_locations.update(box=box)
            messages.success(self.request, _('Moved "%(number)s" item(s) to "%(box)s"') % {"number": number, "box": box})
        return response

class BoxDeleteView(PapMixin, DeleteView):
    template_name = "oscar/dashboard/pap/box_delete.html"
    model = Box
    success_url = reverse_lazy("dashboard:box-list")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.pop("user", None)
        return kwargs


class BoxSearchView(PapMixin, View):
    def get(self, request, *args, **kwargs):
        search_text = request.GET.get("q")
        boxes = []
        if search_text:
            boxes = Box.objects.filter(name__icontains=search_text)

            if not request.user.is_superuser:
                organization = Organization.get_user_organization(request.user)
                boxes = boxes.filter(organization=organization)

            boxes = boxes.values("id", "name", "organization__name")
            boxes = [
                {
                    "id": i["id"],
                    "value": "{} ({})".format(i["name"], i["organization__name"]),
                    "label": "{} ({})".format(i["name"], i["organization__name"]),
                }
                for i in boxes
            ]
        return HttpResponse(json.dumps(boxes), content_type="application/json")


class ActionLogListView(PapMixin, ListView):
    search_form_class = ActionLogSearchForm
    template_name = "oscar/dashboard/pap/action_log_list.html"
    model = ActionLog

    def dispatch(self, request, *args, **kwargs):
        if request.method == "POST":
            self.search_form = self.search_form_class(request.POST)
        else:
            self.search_form = self.search_form_class(request.GET)
        return super(ActionLogListView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = super(ActionLogListView, self).get_queryset()
        if self.search_form.is_valid():
            user = self.search_form.cleaned_data.get("user")
            action = self.search_form.cleaned_data.get("action")
            date_from = self.search_form.cleaned_data.get("date_from")
            date_to = self.search_form.cleaned_data.get("date_to")

            if user:
                queryset = queryset.filter(user__email__icontains=user)
            if action:
                queryset = queryset.filter(action__icontains=action)
            if date_from:
                queryset = queryset.filter(created_at__gte=date_from)
            if date_to:
                queryset = queryset.filter(
                    created_at__lte=datetime.datetime.combine(date_to, datetime.time(23, 23, 59))
                )
        return queryset

    def get_context_data(self, **kwargs):
        context = super(ActionLogListView, self).get_context_data(**kwargs)
        context["form"] = self.search_form
        return context