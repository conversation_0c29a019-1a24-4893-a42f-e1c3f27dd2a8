import datetime
import json
from decimal import InvalidOperation, Decimal
from django import forms
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.db.models import Q
from django.forms.models import inlineformset_factory
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from oscar.forms.widgets import ImageInput, DatePickerInput

from project.apps.catalogue.models import Category, ProductExtraAttribute
from project.rrr.models import RrrCategory, RrrCarBrand, RrrCarModel
from project.recar.models import (
    RecarPartCategory,
    RecarManufacturer,
    RecarModel,
    RecarModification,
)


from project.apps.pap.models import (
    Vehicle,
    VehicleImage,
    VehiclePart,
    VehiclePartImage,
    Location,
    VehiclePartLocation,
    Box,
    Organization,
    EbayApiCredentials,
    VehiclePartAdvertisment,
    VehiclePartStockMutation,
    RrrApiCredentials,
)
from project.apps.tecpap.models import (
    Manufacturer,
    Model,
    Type,
    TypeEngine,
    Fuel,
    Engine,
    Color,
    Transmission,
)
from project.ebay.helpers import <PERSON>bayApiClient
from project.ebay.models import EbayCategory


class VehicleImageForm(forms.ModelForm):
    class Meta:
        model = VehicleImage
        fields = "__all__"
        widgets = {
            "image": ImageInput(),
        }


class VehiclePartImageForm(forms.ModelForm):
    class Meta:
        model = VehiclePartImage
        fields = ['order']
        widgets = {
            'order': forms.HiddenInput(),
        }


VehicleImageFormSet = inlineformset_factory(Vehicle, VehicleImage, form=VehicleImageForm, extra=6)


# VehiclePartImageFormSet = inlineformset_factory(
#     VehiclePart,
#     VehiclePartImage,
#     form=VehiclePartImageForm,
#     can_delete=False,
#     extra=0,
#     max_num=24,
#     validate_max=True,
#     min_num=0,
#     validate_min=True,
# )


class PapFormMixin(object):
    """Class for form mixin"""

    def __init__(self, *args, **kwargs):
        self.user = None
        self.organization = None
        if "user" in kwargs:
            self.user = kwargs["user"]
            self.organization = Organization.get_user_organization(self.user)
            del kwargs["user"]
        super(PapFormMixin, self).__init__(*args, **kwargs)

    def save(self, commit=True):
        instance = super(PapFormMixin, self).save(commit=False)
        if not instance.pk:
            instance.owner = self.user
            instance.organization = self.organization
        if commit:
            instance.save()
        return instance


class BaseVehicleForm(PapFormMixin, forms.ModelForm):
    """Class for base vehicle form"""

    model = forms.ModelChoiceField(label=_("Model"), queryset=Model.objects.none())
    type = forms.ModelChoiceField(label=_("Type"), queryset=Type.objects.none())
    engine = forms.ModelChoiceField(label=_("Engine"), queryset=Engine.objects.none())
    publicate_images_externally = forms.BooleanField(label=_("Publicate images externally"), required=False)

    def __init__(self, *args, **kwargs):
        super(BaseVehicleForm, self).__init__(*args, **kwargs)
        
        self.fields["publicate_images_externally"].initial = True

        if self.data:
            self.fields["model"].queryset = Model.objects.filter(manufacturer=self.data["manufacturer"])
            self.fields["type"].queryset = Type.objects.filter(model=self.data["model"])
            if self.data["type"]:
                self.fields["engine"].queryset = Engine.objects.filter(
                    id__in=TypeEngine.objects.filter(typ__id=self.data["type"]).values_list("eng__id", flat=True)
                )

        if self.instance.pk:
            self.fields["model"].queryset = Model.objects.filter(manufacturer=self.instance.manufacturer)
            self.fields["type"].queryset = Type.objects.filter(model=self.instance.model)
            if self.instance.type:
                self.fields["engine"].queryset = Engine.objects.filter(
                    id__in=TypeEngine.objects.filter(typ=self.instance.type).values_list("eng__id", flat=True)
                )

            if self.data:
                self.fields["model"].queryset = Model.objects.filter(manufacturer=self.data["manufacturer"])
                self.fields["type"].queryset = Type.objects.filter(model=self.data["model"])
                if self.data["type"]:
                    self.fields["engine"].queryset = Engine.objects.filter(
                        id__in=TypeEngine.objects.filter(typ__id=self.data["type"]).values_list("eng__id", flat=True)
                    )


class VehicleForm(BaseVehicleForm):
    """Class for vehicle form"""

    class Meta:
        model = Vehicle
        fields = [
            "year",
            "manufacturer",
            "model",
            "type",
            "engine",
            "fuel",
            "body",
            "code",
            "condition",
            "drive_position",
            "color",
            "color_code",
            "transmission",
            "description",
            "vin",
            "mileage",
            "mileage_km",
            "lot",
            "cost",
            "publicate_images_externally"
        ]

    def clean_code(self):
        code = self.cleaned_data["code"]
        if self.instance.pk:
            if Vehicle.objects.exclude(id=self.instance.pk).filter(code=code, organization=self.organization).exists():
                raise forms.ValidationError(
                    _("Vehicle code %s is already used for another vehicle") % code, code='unique'
                )
        else:
            if Vehicle.objects.filter(code=code, organization=self.organization).exists():
                raise forms.ValidationError(
                    _("Vehicle code %s is already used for another vehicle") % code, code='unique'
                )
        return code


class VehicleDeleteForm(forms.Form):
    def __init__(self, *args, **kwargs):
        if 'user' in kwargs:
            del kwargs['user']
        super().__init__(*args, **kwargs)


class VehiclePartForm(BaseVehicleForm):
    """Class for vehicle part form"""

    vehicle = forms.ModelChoiceField(label=_("Vehicle code"), queryset=Vehicle.objects.all(), required=False)
    type = forms.ModelChoiceField(label=_("Type"), queryset=Type.objects.none(), required=False)
    engine = forms.ModelChoiceField(label=_("Engine"), queryset=Engine.objects.none(), required=False)

    box_ac = forms.CharField(label=_("Box"), required=False)
    box = forms.CharField(required=False)
    box_qty = forms.IntegerField(label=_("Quantity per box"), required=True)

    class Meta:
        model = VehiclePart
        fields = [
            "categories",
            "attributes",
            "vehicle",
            "year",
            "manufacturer",
            "model",
            "type",
            "engine",
            "drive_position",
            "code",
            "warehouse_code",
            "box_ac",
            "box",
            "box_qty",
            "condition",
            "weight",
            "dimension_x",
            "dimension_y",
            "dimension_z",
            "info",
        ]

    def clean_box_qty(self):
        box_qty = self.cleaned_data.get('box_qty')
        # Check if this is a new part creation (not editing)
        if not self.instance.pk and (not box_qty or box_qty <= 0):
            raise forms.ValidationError(_("Quantity must be greater than 0 when creating a new part"))
        return box_qty

    def clean_warehouse_code(self):
        warehouse_code = self.cleaned_data["warehouse_code"]
        if warehouse_code.lower() != "masinoje":
            if self.instance.pk:
                if (
                    VehiclePart.objects.exclude(id=self.instance.pk)
                    .filter(warehouse_code=warehouse_code, organization=self.organization)
                    .exists()
                ):
                    raise forms.ValidationError(
                        _("Warehouse code %s is already used for another vehicle part") % warehouse_code, code='unique'
                    )
            else:
                if VehiclePart.objects.filter(warehouse_code=warehouse_code, organization=self.organization).exists():
                    raise forms.ValidationError(
                        _("Warehouse code %s is already used for another vehicle part") % warehouse_code, code='unique'
                    )
        return warehouse_code

    def __init__(self, *args, **kwargs):
        super(VehiclePartForm, self).__init__(*args, **kwargs)
        if self.user.is_superuser:
            self.fields["vehicle"].queryset = Vehicle.objects.all()
        else:
            self.fields["vehicle"].queryset = Vehicle.objects.filter(organization=self.organization)

        category_choices = []
        category_search_choices = []

        steplen = Category.steplen

        categories = Category.objects.filter(depth__gte=2).order_by('path').values('path', 'id', 'name', 'numchild')

        # Build a mapping from full path to category name
        path_to_name = {cat['path']: cat['name'] for cat in categories}

        root_path = Category.objects.filter(depth=1).values_list('path', flat=True).first()

        for cat in categories:
            path = cat['path']
            # Split the path into steps based on steplen
            steps = [path[i : i + steplen] for i in range(0, len(path), steplen)]
            # Filter out any empty strings resulting from the split
            steps = [step for step in steps if step.strip()]

            cumulative = ''
            full_path_names = []
            last_name = ""

            for step in steps:
                cumulative += step
                if cumulative == root_path:
                    continue  # Skip the root category
                name = path_to_name.get(cumulative, "Unknown")
                full_path_names.append(name)
                last_name = name  # Keep track of the last segment

            full_path = " -> ".join(full_path_names)
            choice_label = f"{cat['id']} || {full_path}"
            # Only add to choices if it's a leaf category (numchild == 0)
            if cat['numchild'] == 0:
                category_choices.append([cat['id'], choice_label])
                # For search purposes, create a separate choice with ID and last name
                category_search_choices.append([cat['id'], last_name])

        self.fields["categories"].choices = category_choices

        self.fields["categories"].widget.attrs["data-search_choices"] = json.dumps(category_search_choices)

        if self.instance.pk:
            del self.fields["box_ac"]
            del self.fields["box"]
            del self.fields["box_qty"]
        else:
            self.fields["box"].widget = forms.HiddenInput()
            self.fields["box_ac"].widget.attrs["placeholder"] = _("Start typing box name to select box")


class VehiclePartImagesUpdateForm(PapFormMixin, forms.ModelForm):
    """Empty form for updating vehicle part images"""

    fake_field = forms.CharField(required=False)

    class Meta:
        model = VehiclePart
        fields = ["fake_field", "warehouse_code"]  # we need one optional field at least


class LocationForm(PapFormMixin, forms.ModelForm):
    """Class for warehouse location form"""

    location = forms.ModelChoiceField(
        label=_("Move to Location"),
        queryset=Location.objects.none(),
        required=False,
    )

    class Meta:
        model = Location
        fields = ("name", "location",)

    def __init__(self, *args, **kwargs):
        super(LocationForm, self).__init__(*args, **kwargs)
        if not self.instance.pk:
            del self.fields["location"]
        else:
            self.fields["location"].queryset = Location.objects.select_related('organization').filter(organization=self.organization)


class BoxForm(PapFormMixin, forms.ModelForm):
    """Class for warehouse box form"""

    location = forms.ModelChoiceField(
        label=_("Move to location"),
        queryset=Location.objects.none(),
        required=False,
    )

    box = forms.ModelChoiceField(
        label=_("Move to box"),
        queryset=Box.objects.none(),
        required=False,
    )
    number_size = forms.IntegerField(label=_("Size"), initial=3, required=False)
    number_from = forms.IntegerField(label=_("From"), initial=1, required=False)
    number_to = forms.IntegerField(label=_("To"), initial=999, required=False)

    class Meta:
        model = Box
        fields = ("name", "location", "number_size", "number_from", "number_to")

    def __init__(self, *args, **kwargs):
        super(BoxForm, self).__init__(*args, **kwargs)
        if not self.instance.pk:
            del self.fields["location"]
            del self.fields["box"]
        else:
            del self.fields["number_size"]
            del self.fields["number_from"]
            del self.fields["number_to"]
            self.fields["location"].queryset = Location.objects.select_related('organization').filter(organization=self.organization)
            self.fields["box"].queryset = Box.objects.select_related('organization').filter(organization=self.organization)


class VehiclePartLocationForm(PapFormMixin, forms.ModelForm):
    """Class for vehicle part location form"""

    box_ac = forms.CharField(label=_("Box"), required=False)

    class Meta:
        model = VehiclePartLocation
        fields = ("vehicle", "location", "box", "box_ac", "num_in_location")

    def __init__(self, *args, **kwargs):
        super(VehiclePartLocationForm, self).__init__(*args, **kwargs)
        if self.user.is_superuser:
            self.fields["vehicle"].queryset = Vehicle.objects.all()
            self.fields["location"].queryset = Location.objects.select_related("organization")
        else:
            self.fields["vehicle"].queryset = Vehicle.objects.filter(organization=self.organization)
            self.fields["location"].queryset = Location.objects.select_related("organization").filter(
                organization=self.organization
            )
        self.fields["box"].widget = forms.HiddenInput()
        self.fields["box_ac"].widget.attrs["placeholder"] = _("Start typing box name to select/change box")
        if self.instance.pk:
            self.fields["box_ac"].initial = str(self.instance.box)

        self.fields["num_in_location"].widget.attrs['min'] = "0"

    def clean_num_in_location(self):
        num_in_location = self.cleaned_data["num_in_location"]
        if self.instance.pk:
            if (
                not self.user.is_superuser
                and self.instance.organization == self.instance.organization.MAIN_ORGANIZATION_ID
                and num_in_location < self.instance.num_in_location
            ):
                raise forms.ValidationError(
                    _("You are not alloed to decrease stock using this form. Use local sales / return form!")
                )
            if self.instance.num_allocated and num_in_location < self.instance.num_allocated:
                raise forms.ValidationError(_("Qty can not be less than Qty allocated"))
        return num_in_location


class VehiclePartStockMutationForm(PapFormMixin, forms.ModelForm):
    vehicle = forms.ModelChoiceField(label=_("Vehicle code"), queryset=Vehicle.objects.all(), required=False)
    code = forms.CharField(label=_("Part code"), required=False)
    location = forms.ModelChoiceField(
        label=_("Location"),
        queryset=VehiclePartLocation.objects.none(),
        required=False,
    )
    quantity = forms.IntegerField(label=_("Quantity"))
    price = forms.DecimalField(label=_("Price"))
    mutation = forms.CharField(widget=forms.HiddenInput)

    class Meta:
        model = VehiclePart
        fields = [
            "vehicle",
            "year",
            "manufacturer",
            "model",
            "categories",
            "attributes",
            "code",
            "warehouse_code",
            "location",
            "weight",
            "quantity",
            "price",
        ]

    def __init__(self, *args, **kwargs):
        if "mutation" in kwargs:
            self.mutation = kwargs["mutation"]
            del kwargs["mutation"]
        if "location" in kwargs:
            self.location = kwargs["location"]
            del kwargs["location"]
        super().__init__(*args, **kwargs)

        organization = Organization.get_user_organization(self.user)

        if not self.user.is_superuser:
            self.fields["vehicle"].queryset = Vehicle.objects.filter(organization=organization)

        self.fields["weight"].initial = 1
        self.fields["quantity"].initial = 1
        if self.location:
            self.fields["location"].queryset = VehiclePartLocation.objects.filter(id=self.location)

    def clean_price(self):
        price = self.cleaned_data["price"]
        if price <= 0:
            raise forms.ValidationError(_("Price can not be less or equal to zero"))
        return price


class BaseVehiclePartAdvertismentForm(PapFormMixin, forms.ModelForm):
    """Base class for vehicle part advertisment form"""

    def __init__(self, *args, **kwargs):
        self.vehicle_part = kwargs["vehicle_part"]
        del kwargs["vehicle_part"]
        super(BaseVehiclePartAdvertismentForm, self).__init__(*args, **kwargs)
        if "description_template" in self.fields:
            self.fields["description_template"].initial = 2
        self.fields["shop"].widget = forms.HiddenInput()
        self.fields["shop"].initial = self.SHOP

    def clean_price(self):
        price = self.cleaned_data["price"]
        if price <= 0:
            raise forms.ValidationError(_("Price can not be less or equal to zero"))
        return price

    def clean(self):
        cleaned_data = super(BaseVehiclePartAdvertismentForm, self).clean()
        if self.instance.pk is None:
            if self.SHOP == settings.SHOP_EBAY:
                if not cleaned_data.get("ebay_api_credentials"):
                    raise forms.ValidationError(
                        _("Something went wrong, eBay API credentials record is not provided!")
                    )
                if self.vehicle_part.advertisments.filter(
                    shop=self.SHOP,
                    ebay_api_credentials=cleaned_data["ebay_api_credentials"],
                ).exists():
                    raise forms.ValidationError(
                        _(
                            "Vehicle part advertisment for this eBay account already exists, please choose another account or another shop!"
                        )
                    )
            else:
                if self.vehicle_part.advertisments.filter(shop=self.SHOP).exists():
                    raise forms.ValidationError(
                        _("Vehicle part advertisment for this shop already exists, please choose another shop!")
                    )

            if (
                self.SHOP != settings.SHOP_PARTAN
                and not self.vehicle_part.advertisments.filter(shop=settings.SHOP_PARTAN).exists()
            ):
                raise forms.ValidationError(
                    _("System did not find any advertisment for Partan. Please create advertisment for Partan first!")
                )
        return cleaned_data


class VehiclePartAdvertismentFormForPartan(BaseVehiclePartAdvertismentForm):
    """Class for vehicle part advertisment form for Partan"""

    SHOP = settings.SHOP_PARTAN

    class Meta:
        model = VehiclePartAdvertisment
        fields = ("shop", "price", "description_template", "active")


class VehiclePartAdvertismentUpdateFormForPartan(VehiclePartAdvertismentFormForPartan):
    class Meta:
        model = VehiclePartAdvertisment
        fields = ("shop", "price", "discount", "description_template", "active")


class VehiclePartAdvertismentFormForEbay(BaseVehiclePartAdvertismentForm):
    """Class for vehicle part advertisment form for eBay platform"""

    SHOP = settings.SHOP_EBAY

    ebay_fulfillment_policy_id = forms.ChoiceField(label=_("Fulfillment policy"), choices=[], required=False)

    class Meta:
        model = VehiclePartAdvertisment
        fields = (
            "shop",
            "ebay_api_credentials",
            "ebay_category",
            "price",
            "ebay_fulfillment_policy_id",
            "description_template",
        )

    def __init__(self, *args, **kwargs):
        super(VehiclePartAdvertismentFormForEbay, self).__init__(*args, **kwargs)

        # Get policies once and reuse them
        fulfillment_policies = self.get_fulfillment_policies()
        self.fields["description_template"].initial = 5
        self.fields["ebay_fulfillment_policy_id"].choices = fulfillment_policies
        self.fields["ebay_fulfillment_policy_id"].initial = self.get_initial_fulfillment_policy(fulfillment_policies)
        self.fields["ebay_api_credentials"].required = True
        self.fields["ebay_api_credentials"].queryset = self.organization.ebay_api_credentials.all()

        try:
            self.fields["ebay_api_credentials"].initial = self.organization.ebay_api_credentials.all()[0]
        except IndexError:
            pass

        self.fields["ebay_category"].required = True
        try:
            ebay_category = self.vehicle_part.categories.all()[0].ebay_categories.all()[0]
        except IndexError:
            try:
                ebay_category = EbayCategory.objects.get(id=settings.EBAY_OFFER_FALLBACK_CATEGORY_ID)
            except EbayCategory.DoesNotExist:
                ebay_category = None
        self.fields["ebay_category"].initial = ebay_category

    def get_fulfillment_policies(self):
        """Get fulfillment policies from all available eBay credentials"""
        fulfillment_policies = []
        for ebay_api_credentials in self.organization.ebay_api_credentials.all():
            ebay = EbayApiClient(ebay_api_credentials.id)
            status_code, response_data, error = ebay.get_fulfillment_policies()
            if status_code == 200 and response_data:
                for policy in response_data.get('fulfillmentPolicies', []):
                    if "copy" not in policy['name'].lower():
                        fulfillment_policies.append(
                            (
                                policy["fulfillmentPolicyId"],
                                f"{policy['name']} | {ebay_api_credentials.name}",
                            )
                        )
        return fulfillment_policies

    def get_initial_fulfillment_policy(self, fulfillment_policies):
        """
        Get initial fulfillment policy based on vehicle part weight

        Args:
            fulfillment_policies: List of already fetched policies
        """
        vehicle_part_weight = self.vehicle_part.weight
        initial_policy_id = 0
        initial_weight = 10000000
        for policy in fulfillment_policies:
            policy_name_in_upper_case = policy[1].upper()
            kg_start = policy_name_in_upper_case.find("KG")
            try:
                weight_from_policy_name = Decimal(policy_name_in_upper_case[:kg_start].strip())
            except InvalidOperation:
                weight_from_policy_name = 0
            else:
                if weight_from_policy_name <= initial_weight and weight_from_policy_name >= vehicle_part_weight:
                    initial_policy_id = policy[0]
                    initial_weight = weight_from_policy_name

        return initial_policy_id


class VehiclePartAdvertismentUpdateFormForEbay(VehiclePartAdvertismentFormForEbay):
    pass


class VehiclePartAdvertismentFormForRrr(BaseVehiclePartAdvertismentForm):
    """Class for vehicle part advertisment form for RRR platform"""

    SHOP = settings.SHOP_RRR

    rrr_category = forms.ModelChoiceField(queryset=RrrCategory.objects.filter(depth=3))
    rrr_brand = forms.ModelChoiceField(queryset=RrrCarBrand.objects.all())
    rrr_model = forms.ModelChoiceField(queryset=RrrCarModel.objects.all())

    class Meta:
        model = VehiclePartAdvertisment
        fields = (
            "shop",
            "rrr_api_credentials",
            "rrr_category",
            "rrr_brand",
            "rrr_model",
            "price",
        )

    def __init__(self, *args, **kwargs):
        super(VehiclePartAdvertismentFormForRrr, self).__init__(*args, **kwargs)

        rrr_category_id, rrr_model_id = self.vehicle_part.get_rrr_attributes()

        self.fields["rrr_api_credentials"].required = True
        self.fields["rrr_api_credentials"].queryset = RrrApiCredentials.objects.filter(
            id=self.organization.rrr_api_credentials_id
        )
        try:
            self.fields["rrr_api_credentials"].initial = self.organization.rrr_api_credentials_id
        except AttributeError:
            pass

        try:
            brand = self.vehicle_part.manufacturer.brand
            if brand.upper() == "FORD USA":
                brand = "FORD"
            rrr_car_brand_id = RrrCarBrand.objects.filter(name__iexact=brand)[0].id
            self.fields["rrr_brand"].initial = rrr_car_brand_id
        except IndexError:
            rrr_car_brand_id = None
        else:
            rrr_models = RrrCarModel.objects.filter(brand=rrr_car_brand_id)
            if rrr_models.count() > 0:
                self.fields["rrr_model"].queryset = rrr_models

        self.fields["rrr_category"].initial = rrr_category_id
        self.fields["rrr_model"].initial = rrr_model_id


class VehiclePartAdvertismentUpdateFormForRrr(VehiclePartAdvertismentFormForRrr):
    pass


class VehiclePartAdvertismentFormForRecar(BaseVehiclePartAdvertismentForm):
    """Class for vehicle part advertisment form for Recar platform"""

    SHOP = settings.SHOP_RECAR

    recar_category = forms.ModelChoiceField(queryset=RecarPartCategory.objects.filter())
    recar_manufacturer = forms.ModelChoiceField(queryset=RecarManufacturer.objects.all())
    recar_model = forms.ModelChoiceField(queryset=RecarModel.objects.all())
    recar_modification = forms.ModelChoiceField(queryset=RecarModification.objects.all(), required=False)

    class Meta:
        model = VehiclePartAdvertisment
        fields = (
            "shop",
            "recar_category",
            "recar_manufacturer",
            "recar_model",
            "recar_modification",
            "price",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        (
            recar_category_id,
            recar_model_id,
            recar_modification_id,
        ) = self.vehicle_part.get_recar_attributes()

        try:
            manufacturer = self.vehicle_part.manufacturer.brand
            if manufacturer.upper() == "VOLKSWAGEN":
                manufacturer = "VW"
            recar_manufacturer_id = RecarManufacturer.objects.filter(title__iexact=manufacturer)[0].id
            self.fields["recar_manufacturer"].initial = recar_manufacturer_id
        except IndexError:
            recar_manufacturer_id = None
        else:
            recar_models = RecarModel.objects.filter(manufacturer=recar_manufacturer_id)
            if recar_models.count() > 0:
                self.fields["recar_model"].queryset = recar_models

            recar_modifications = RecarModification.objects.filter(model=recar_model_id)
            if recar_modifications.count() > 0:
                self.fields["recar_modification"].queryset = recar_modifications

        self.fields["recar_category"].initial = recar_category_id
        self.fields["recar_model"].initial = recar_model_id
        self.fields["recar_modification"].initial = recar_modification_id


class VehiclePartAdvertismentUpdateFormForRecar(VehiclePartAdvertismentFormForRecar):
    pass


class SimpleSearchForm(forms.Form):
    """Class for simple search form"""

    name = forms.CharField(label=_("Name"), required=False)


class BoxSearchForm(forms.Form):
    """Class for box search form"""

    name = forms.CharField(label=_("Name"), required=False)
    location = forms.ModelChoiceField(label=_("Location"), required=False, queryset=Location.objects.none())

    def __init__(self, *args, **kwargs):
        self.user = None
        self.organization = None
        if "user" in kwargs:
            self.user = kwargs["user"]
            self.organization = Organization.get_user_organization(self.user)
            del kwargs["user"]

        super().__init__(*args, **kwargs)

        self.fields["location"].queryset = Location.objects.filter(organization=self.organization).select_related(
            'organization'
        )


class BaseVehicleSearchForm(forms.Form):
    """Class for base vehicle search form"""

    year = forms.ChoiceField(
        label=_("Year"),
        choices=[("", "---------")] + Vehicle.YEAR_CHOICES,
        required=False,
    )
    manufacturer = forms.ModelChoiceField(label=_("Manufacturer"), queryset=Manufacturer.objects.all(), required=False)
    model = forms.CharField(label=_("Model"), required=False)
    type = forms.CharField(label=_("Type"), required=False)
    organization = forms.ModelChoiceField(
        label=_("Organization"),
        queryset=Organization.objects.all(),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        self.user = None
        self.organization = None
        if "user" in kwargs:
            self.user = kwargs["user"]
            self.organization = Organization.get_user_organization(self.user)
            del kwargs["user"]
        super().__init__(*args, **kwargs)
        # self.fields['organization'].initial = self.organization


class VehicleSearchForm(BaseVehicleSearchForm):
    """Class for vehicle search form"""

    code = forms.CharField(label=_("Code"), required=False)
    lot = forms.CharField(label=_("Auction Name/No"), required=False)
    vin = forms.CharField(label=_("VIN"), required=False)


class VehiclePartSearchForm(BaseVehicleSearchForm):
    """Class for vehicle part search form"""

    vehicle = forms.CharField(label=_("Vehicle"), required=False)
    created_date_from = forms.DateField(
        label=_("Part created from:"),
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    created_date_to = forms.DateField(
        label=_("Part created to:"), 
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'form-control'})
    )
    part_name = forms.CharField(label=_("Part title"), required=False)
    part_code = forms.CharField(label=_("Part code"), required=False)
    warehouse_code = forms.CharField(label=_("Warehouse code"), required=False)
    warehouse_code_fragment = forms.CharField(label=_("Warehouse code (fragment)"), required=False)
    engine_code = forms.CharField(label=_("Engine code"), required=False)
    attributes = forms.CharField(label=_("Attributes"), required=False)
    has_advertisements = forms.ChoiceField(
        label=_("Has advertisements"),
        choices=[("", "----"), ("1", _("YES")), ("0", _("NO"))]
        + VehiclePartAdvertisment.SHOP_CHOICES
        + [(f"not_{i[0]}", f"NOT {i[1]}") for i in VehiclePartAdvertisment.SHOP_CHOICES],
        required=False,
    )
    price_is_older_than_6_months = forms.ChoiceField(
        label=_("Price is older than 6 months"),
        choices=[("", "----"), ("1", _("YES")), ("0", _("NO"))],
        required=False,
    )
    has_images = forms.ChoiceField(
        label=_("Has images"),
        choices=[("", "----"), ("1", _("YES")), ("0", _("NO"))],
        required=False,
    )
    has_stock = forms.ChoiceField(
        label=_("Has stock"),
        choices=[("", "----"), ("1", _("YES")), ("0", _("NO"))],
        required=False,
    )
    has_temp_allocations = forms.ChoiceField(
        label=_("Has temp. reservations"),
        choices=[("", "----"), ("1", _("YES")), ("0", _("NO"))],
        required=False,
    )
    box = forms.CharField(label=_("Box"), required=False)
    location = forms.CharField(label=_("Location"), required=False)
    id = forms.IntegerField(label=_("ID"), required=False, widget=forms.TextInput(attrs={
        'class': 'form-control',
        'style': 'width: 50px !important; padding: 5px 1px !important;'
    }))
    quantity = forms.IntegerField(label=_("Quantity"), required=False)
    condition = forms.ChoiceField(
        label=_("Condition"),
        choices=[("", "----")] + list(VehiclePart.CONDITION_CHOICES),
        required=False,
    )


class VehiclePartStockMutationSearchForm(forms.Form):
    """Class for vehicle part stock mutation search form"""

    date_from = forms.DateField(label=_("Date from"), required=False, widget=DatePickerInput)
    date_to = forms.DateField(label=_("Date to"), required=False, widget=DatePickerInput)
    manager = forms.ModelChoiceField(label=_("Manager"), queryset=User.objects.filter(is_staff=True), required=False)
    organization = forms.ModelChoiceField(
        label=_("Organization"), 
        queryset=Organization.objects.all(), 
        required=False,
        empty_label=None  # This removes the empty "------" option
    )
    part_code = forms.CharField(label=_("Part code"), required=False)
    warehouse_code = forms.CharField(label=_("Warehouse code"), required=False)

    def __init__(self, *args, **kwargs):
        if "user" in kwargs:
            self.user = kwargs["user"]
            self.organization = Organization.get_user_organization(self.user)
            del kwargs["user"]

        # Initialize with data including organization and manager if not provided
        if not kwargs.get('data') and not kwargs.get('initial'):
            kwargs['initial'] = {
                'organization': self.organization.id,
                'manager': self.user.id  # Set the default manager to current user
            }

        super().__init__(*args, **kwargs)

        if self.user.is_superuser:
            self.fields["manager"].queryset = User.objects.filter(id__in=self.organization.users.all())
            self.fields["organization"].queryset = Organization.objects.all()
            self.fields["organization"].empty_label = "---------"  # Restore empty label for superusers
        else:
            # Show all organization's managers instead of just the current user
            self.fields["manager"].queryset = User.objects.filter(id__in=self.organization.users.all())
            self.fields["organization"].queryset = Organization.objects.filter(id=self.organization.id)
            self.fields["organization"].initial = self.organization
            self.fields["organization"].widget.attrs["disabled"] = True


class ActionLogSearchForm(forms.Form):
    """Class for action log search form"""

    user = forms.CharField(label=_("User"), required=False)
    action = forms.CharField(label=_("Action"), required=False)
    date_from = forms.DateTimeField(label=_("Date from"), required=False)
    date_to = forms.DateTimeField(label=_("Date to"), required=False)


class BaseExportForm(forms.Form):
    """Base class for exporting search results"""

    EXPORT_XLS = "xls"
    EXPORT_CSV = "csv"
    EXPORT_PDF = "pdf"

    EXPORT_CHOISES = ((EXPORT_XLS, "XLS"), (EXPORT_CSV, "CSV"), (EXPORT_PDF, "PDF"))

    export_as = forms.ChoiceField(
        label=_("Export data as"),
        choices=EXPORT_CHOISES,
        required=False,
        initial=EXPORT_CSV,
    )
    export_fields = forms.MultipleChoiceField(label=_("Export fields"), choices=[], required=False)

    def __init__(self, *args, **kwargs):
        super(BaseExportForm, self).__init__(*args, **kwargs)
        self.fields["export_fields"].choices = self.get_export_field_choices()

    def get_export_field_choices(self):
        try:
            fields = self.available_fields
        except AttributeError:
            raise NotImplementedError("Did you forget to add list of available fields `available_fields`?")
        return fields

    @property
    def available_fields_map(self):
        return dict(self.get_export_field_choices())


class VehiclePartExportForm(BaseExportForm):
    available_fields = [
        ("id", _("ID")),
        ("get_box_display", _("Boxes")),
        ("get_location_display", _("Locations")),
        ("code", _("Code")),
        ("warehouse_code", _("Warehouse code")),
        ("get_categories_display", _("Title")),
        ("get_attributes_display", _("Attributes")),
        ("get_stock_display", _("Q-ty")),
        ("year", _("Year")),
        ("manufacturer", _("Manufacturer")),
        ("model", _("Model")),
        ("type", _("Type")),
        ("engine", _("Engine")),
        ("get_price_display", _("Price")),
    ]
