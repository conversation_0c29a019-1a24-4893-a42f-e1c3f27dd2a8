from django.urls import path
from django.utils.translation import gettext_lazy as _

from oscar.core.application import Oscar<PERSON>ashboardConfig
from oscar.core.loading import get_class


class PapDashboardConfig(OscarDashboardConfig):
    label = "pap_dashboard"
    name = "project.apps.dashboard.pap"
    verbose_name = _("PAP dashboard")

    default_permissions = [
        "is_staff",
    ]

    def ready(self):
        super().ready()

        self.vehicle_list_view = get_class("dashboard.pap.views", "VehicleListView")
        self.vehicle_parts_by_location_view = get_class("dashboard.pap.views", "VehiclePartsByLocationView")
        self.vehicle_create_view = get_class("dashboard.pap.views", "VehicleCreateView")
        self.vehicle_update_view = get_class("dashboard.pap.views", "VehicleUpdateView")
        self.vehicle_delete_view = get_class("dashboard.pap.views", "VehicleDeleteView")
        self.vehicle_detail_view = get_class("dashboard.pap.views", "VehicleDetailView")
        self.vehicle_search_view = get_class("dashboard.pap.views", "VehicleSearchView")
        self.vehicle_detail_ajax_view = get_class("dashboard.pap.views", "VehicleDetailAjaxView")
        self.vehicle_detail_json_view = get_class("dashboard.pap.views", "VehicleDetailJSONView")
        self.vehicle_sales_view = get_class("dashboard.pap.views", "VehicleSalesView")

        self.vehicle_part_list_view = get_class("dashboard.pap.views", "VehiclePartListView")
        self.vehicle_part_list_ajax_view = get_class("dashboard.pap.views", "VehiclePartListAjaxView")
        self.vehicle_part_list_debug_view = get_class("dashboard.pap.views", "VehiclePartListDebugView")
        self.vehicle_part_create_view = get_class("dashboard.pap.views", "VehiclePartCreateView")
        self.vehicle_part_update_view = get_class("dashboard.pap.views", "VehiclePartUpdateView")
        self.vehicle_part_delete_view = get_class("dashboard.pap.views", "VehiclePartDeleteView")
        self.vehicle_part_detail_view = get_class("dashboard.pap.views", "VehiclePartDetailView")
        self.vehicle_part_title_search_view = get_class("dashboard.pap.views", "VehiclePartTitleSearchView")
        self.vehicle_part_attribute_search_view = get_class("dashboard.pap.views", "VehiclePartAttributeSearchView")
        self.vehicle_part_detail_ajax_view = get_class("dashboard.pap.views", "VehiclePartDetailAjaxView")
        self.vehicle_part_detail_download_view = get_class("dashboard.pap.views", "VehiclePartDetailDownloadView")
        self.vehicle_part_images_ajax_view = get_class("dashboard.pap.views", "VehiclePartImagesAjaxView")
        self.vehicle_part_location_view = get_class("dashboard.pap.views", "VehiclePartLocationView")
        self.vehicle_part_stock_mutation_list_view = get_class(
            "dashboard.pap.views", "VehiclePartStockMutationListView"
        )
        self.vehicle_part_stock_mutation_create_view = get_class(
            "dashboard.pap.views", "VehiclePartStockMutationCreateView"
        )

        self.vehicle_part_advertisment_view = get_class("dashboard.pap.views", "VehiclePartAdvertismentView")

        self.location_list_view = get_class("dashboard.pap.views", "LocationListView")
        self.location_create_view = get_class("dashboard.pap.views", "LocationCreateView")
        self.location_update_view = get_class("dashboard.pap.views", "LocationUpdateView")
        self.location_delete_view = get_class("dashboard.pap.views", "LocationDeleteView")

        self.box_list_view = get_class("dashboard.pap.views", "BoxListView")
        self.box_create_view = get_class("dashboard.pap.views", "BoxCreateView")
        self.box_update_view = get_class("dashboard.pap.views", "BoxUpdateView")
        self.box_delete_view = get_class("dashboard.pap.views", "BoxDeleteView")
        self.box_search_view = get_class("dashboard.pap.views", "BoxSearchView")

        self.vehicle_part_location_update_view = get_class("dashboard.pap.views", "VehiclePartLocationUpdateView")
        self.vehicle_part_location_delete_view = get_class("dashboard.pap.views", "VehiclePartLocationDeleteView")
        self.vehicle_part_location_qty_increase_ajax_view = get_class(
            "dashboard.pap.views", "VehiclePartLocationQtyIncreaseAjaxView"
        )
        self.vehicle_part_location_qty_decrease_ajax_view = get_class(
            "dashboard.pap.views", "VehiclePartLocationQtyDecreaseAjaxView"
        )
        self.vehicle_part_location_qty_allocated_increase_ajax_view = get_class(
            "dashboard.pap.views", "VehiclePartLocationQtyAllocatedIncreaseAjaxView"
        )
        self.vehicle_part_location_qty_allocated_decrease_ajax_view = get_class(
            "dashboard.pap.views", "VehiclePartLocationQtyAllocatedDecreaseAjaxView"
        )
        self.vehicle_part_remove_temp_allocations_ajax_view = get_class(
            "dashboard.pap.views", "VehiclePartRemoveTempAllocationsAjaxView"
        )
        self.vehicle_part_location_search_json_view = get_class(
            "dashboard.pap.views", "VehiclePartLocationSearchJSONView"
        )
        self.vehicle_part_advertisment_update_view = get_class(
            "dashboard.pap.views", "VehiclePartAdvertismentUpdateView"
        )
        self.vehicle_part_advertisment_price_update_view = get_class(
            "dashboard.pap.views", "VehiclePartAdvertismentPriceUpdateView"
        )
        self.vehicle_part_advertisment_product_delete_view = get_class(
            "dashboard.pap.views", "VehiclePartAdvertismentProductDeleteView"
        )

        self.action_log_list_view = get_class("dashboard.pap.views", "ActionLogListView")

    def get_urls(self):
        urls = [
            path(
                "list-vehicle/",
                self.vehicle_list_view.as_view(),
                name="vehicle-list",
            ),
            path(
                "search-vehicle/",
                self.vehicle_search_view.as_view(),
                name="vehicle-search",
            ),
            path(
                "add-vehicle/",
                self.vehicle_create_view.as_view(),
                name="vehicle-add",
            ),
            path(
                "edit-vehicle/<int:pk>/",
                self.vehicle_update_view.as_view(),
                name="vehicle-edit",
            ),
            path(
                "delete-vehicle/<int:pk>/",
                self.vehicle_delete_view.as_view(),
                name="vehicle-delete",
            ),
            path(
                "detail-vehicle/<int:pk>/",
                self.vehicle_detail_view.as_view(),
                name="vehicle-details",
            ),
            path(
                "detail-vehicle-ajax/<int:pk>/",
                self.vehicle_detail_ajax_view.as_view(),
                name="vehicle-details-ajax",
            ),
            path(
                "detail-vehicle-json/<int:pk>/",
                self.vehicle_detail_json_view.as_view(),
                name="vehicle-details-json",
            ),
            path(
                "detail-vehicle-sales/<int:pk>/",
                self.vehicle_sales_view.as_view(),
                name="vehicle-sales",
            ),
            path(
                "list-vehicle-part/",
                self.vehicle_part_list_view.as_view(),
                name="vehicle-part-list",
            ),
            path(
                "search-vehicle-part-title/",
                self.vehicle_part_title_search_view.as_view(),
                name="vehicle-part-title-search",
            ),
            path(
                "search-vehicle-part-attribute/",
                self.vehicle_part_attribute_search_view.as_view(),
                name="vehicle-part-attribute-search",
            ),
            path(
                "list-vehicle-part-ajax/",
                self.vehicle_part_list_ajax_view.as_view(),
                name="vehicle-part-list-ajax",
            ),
            path(
                "list-vehicle-part-debug/",
                self.vehicle_part_list_debug_view.as_view(),
                name="vehicle-part-list-debug",
            ),
            path(
                "add-vehicle-part/",
                self.vehicle_part_create_view.as_view(),
                name="vehicle-part-add",
            ),
            path(
                "edit-vehicle-part/<int:pk>/",
                self.vehicle_part_update_view.as_view(),
                name="vehicle-part-edit",
            ),
            path(
                "delete-vehicle-part/<int:pk>/",
                self.vehicle_part_delete_view.as_view(),
                name="vehicle-part-delete",
            ),
            path(
                "detail-vehicle-part/<int:pk>/",
                self.vehicle_part_detail_view.as_view(),
                name="vehicle-part-details",
            ),
            path(
                "detail-vehicle-part-ajax/<int:pk>/",
                self.vehicle_part_detail_ajax_view.as_view(),
                name="vehicle-part-details-ajax",
            ),
            path(
                "download-vehicle-part/<int:pk>/",
                self.vehicle_part_detail_download_view.as_view(),
                name="vehicle-part-details-download",
            ),
            path(
                "locate-vehicle-part/<int:pk>/",
                self.vehicle_part_location_view.as_view(),
                name="vehicle-part-locations",
            ),
            path(
                "list-vehicle-part-stock-mutations",
                self.vehicle_part_stock_mutation_list_view.as_view(),
                name="vehicle-part-stock-mutation-list",
            ),
            path(
                "add-vehicle-part-stock-mutation",
                self.vehicle_part_stock_mutation_create_view.as_view(),
                name="vehicle-part-stock-mutation-add",
            ),
            path(
                "advertise-vehicle-part/<int:pk>/",
                self.vehicle_part_advertisment_view.as_view(),
                name="vehicle-part-advertisments",
            ),
            path(
                "image-vehicle-part/<int:pk>/",
                self.vehicle_part_images_ajax_view.as_view(),
                name="vehicle-part-images-ajax",
            ),
            path(
                "remove-temp-allocations/<int:pk>/",
                self.vehicle_part_remove_temp_allocations_ajax_view.as_view(),
                name="vehicle-part-remove-temp-allocations-ajax",
            ),
            path(
                "list-location/",
                self.location_list_view.as_view(),
                name="location-list",
            ),
            path(
                "add-location/",
                self.location_create_view.as_view(),
                name="location-add",
            ),
            path(
                "edit-location/<int:pk>/",
                self.location_update_view.as_view(),
                name="location-edit",
            ),
            path(
                "delete-location/<int:pk>/",
                self.location_delete_view.as_view(),
                name="location-delete",
            ),
            path(
                "parts-by-location/<int:pk>/",
                self.vehicle_parts_by_location_view.as_view(),
                name="parts-by-location",
            ),
            path("list-box/", self.box_list_view.as_view(), name="box-list"),
            path("search-box/", self.box_search_view.as_view(), name="box-search"),
            path("add-box/", self.box_create_view.as_view(), name="box-add"),
            path(
                "edit-box/<int:pk>/",
                self.box_update_view.as_view(),
                name="box-edit",
            ),
            path(
                "delete-box/<int:pk>/",
                self.box_delete_view.as_view(),
                name="box-delete",
            ),
            path(
                "edit-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_update_view.as_view(),
                name="vehicle-part-location-edit",
            ),
            path(
                "delete-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_delete_view.as_view(),
                name="vehicle-part-location-delete",
            ),
            path(
                "qty-increase-for-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_qty_increase_ajax_view.as_view(),
                name="vehicle-part-location-qty-increase",
            ),
            path(
                "qty-decrease-for-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_qty_decrease_ajax_view.as_view(),
                name="vehicle-part-location-qty-decrease",
            ),
            path(
                "qty-allocated-increase-for-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_qty_allocated_increase_ajax_view.as_view(),
                name="vehicle-part-location-qty-allocated-increase",
            ),
            path(
                "qty-allocated-decrease-for-vehicle-part-location/<int:pk>/",
                self.vehicle_part_location_qty_allocated_decrease_ajax_view.as_view(),
                name="vehicle-part-location-qty-allocated-decrease",
            ),
            path(
                "search-vehicle-part-location/",
                self.vehicle_part_location_search_json_view.as_view(),
                name="vehicle-part-location-search-json",
            ),
            path(
                "edit-vehicle-part-advertisment/<int:pk>/",
                self.vehicle_part_advertisment_update_view.as_view(),
                name="vehicle-part-advertisment-edit",
            ),
            path(
                "edit-vehicle-part-advertisment-price-update/<int:pk>/",
                self.vehicle_part_advertisment_price_update_view.as_view(),
                name="vehicle-part-advertisment-price-edit",
            ),
            path(
                "delete-vehicle-part-advertisment/<int:pk>/",
                self.vehicle_part_advertisment_product_delete_view.as_view(),
                name="vehicle-part-advertisment-product-delete",
            ),
            path(
                "list-action-log/",
                self.action_log_list_view.as_view(),
                name="action-log-list",
            ),
        ]
        return self.post_process_urls(urls)
