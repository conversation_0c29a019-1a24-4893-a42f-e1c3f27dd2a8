from decimal import Decimal
from django.conf import settings
from django.db.models import Q, Sum
from project.apps.pap.models import VehiclePart, VehiclePartAdvertisment

from .forms import (
    VehiclePartAdvertismentFormForRrr,
    VehiclePartAdvertismentFormForRecar,
    VehiclePartAdvertismentFormForPartan,
    VehiclePartAdvertismentFormForEbay,
)


def export_vehicle_parts_to_rrr(num_items_to_export=5000, print_results=False):
    """
    Export vehicle parts to RRR
    """

    if settings.DEPLOYMENT != settings.DEPLOYMENT_LIVE:
        return False

    unexported_vehicle_parts = (
        VehiclePart.objects.filter(advertisments__shop=settings.SHOP_PARTAN, advertisments__active=1)
        .exclude(advertisments__shop=settings.SHOP_RRR)
        .annotate(qty=Sum("locations__num_in_location"))
        .filter(qty__gt=0)
        .order_by("-id")[:num_items_to_export]
    )
    for vehicle_part in unexported_vehicle_parts:
        data = {}
        try:
            partan_vehicle_part_advertisment = vehicle_part.advertisments.get(shop=settings.SHOP_PARTAN)
        except VehiclePartAdvertisment.DoesNotExist:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Partan advertisment does not exist!",
                    }
                )
            continue
        except VehiclePartAdvertisment.MultipleObjectsReturned:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Multiple Partan advertisments exist!",
                    }
                )
            continue
        else:
            unbound_rrr_advertisment_form = VehiclePartAdvertismentFormForRrr(
                vehicle_part=vehicle_part, user=partan_vehicle_part_advertisment.owner
            )

            for key, field in list(unbound_rrr_advertisment_form.fields.items()):
                data[key] = field.initial

            data["price"] = (partan_vehicle_part_advertisment.price * Decimal(1.10)).quantize(Decimal(0.00))

        bound_rrr_advertisment_form = VehiclePartAdvertismentFormForRrr(
            data, vehicle_part=vehicle_part, user=partan_vehicle_part_advertisment.owner
        )

        if bound_rrr_advertisment_form.is_valid():
            vehicle_part_advertisment = bound_rrr_advertisment_form.save(commit=False)
            vehicle_part_advertisment.vehicle_part = vehicle_part
            vehicle_part_advertisment.manager = partan_vehicle_part_advertisment.manager
            vehicle_part_advertisment.organization_id = partan_vehicle_part_advertisment.organization_id
            vehicle_part_advertisment.save()
            success, error_message = vehicle_part_advertisment.create_or_update_rrr_product()
            if error_message:
                if print_results:
                    print(
                        {
                            "vehicle_part_id": vehicle_part.id,
                            "error": error_message,
                        }
                    )
                continue
        else:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": bound_rrr_advertisment_form.errors,
                    }
                )
            continue

    return 1


def export_vehicle_parts_to_recar(num_items_to_export=5000, print_results=False):
    """
    Export vehicle parts to Recar
    """

    if settings.DEPLOYMENT != settings.DEPLOYMENT_LIVE:
        return False

    unexported_vehicle_parts = (
        VehiclePart.objects.filter(advertisments__shop=settings.SHOP_PARTAN, advertisments__active=1)
        .exclude(advertisments__shop=settings.SHOP_RECAR)
        .annotate(qty=Sum("locations__num_in_location"))
        .filter(qty__gt=0)
        .order_by("id")[:num_items_to_export]
    )
    counter = 0
    for vehicle_part in unexported_vehicle_parts:
        unbound_recar_advertisment_form = VehiclePartAdvertismentFormForRecar(vehicle_part=vehicle_part)
        data = {}
        for key, field in list(unbound_recar_advertisment_form.fields.items()):
            data[key] = field.initial
        try:
            partan_vehicle_part_advertisment = vehicle_part.advertisments.get(shop=settings.SHOP_PARTAN)
        except VehiclePartAdvertisment.DoesNotExist:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Partan advertisment does not exist!",
                    }
                )
            continue
        except VehiclePartAdvertisment.MultipleObjectsReturned:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Multiple Partan advertisments exist!",
                    }
                )
            continue
        else:
            data["price"] = (partan_vehicle_part_advertisment.price * Decimal(1.10)).quantize(Decimal(0.00))

        bound_recar_advertisment_form = VehiclePartAdvertismentFormForRecar(
            data, vehicle_part=vehicle_part, user=partan_vehicle_part_advertisment.owner
        )

        if bound_recar_advertisment_form.is_valid():
            vehicle_part_advertisment = bound_recar_advertisment_form.save(commit=False)
            vehicle_part_advertisment.vehicle_part = vehicle_part
            vehicle_part_advertisment.manager = partan_vehicle_part_advertisment.manager
            vehicle_part_advertisment.organization_id = partan_vehicle_part_advertisment.organization_id
            vehicle_part_advertisment.save()
            success, error_message = vehicle_part_advertisment.create_or_update_recar_product()
            if error_message:
                continue
            if success:
                counter += 1
        else:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": bound_recar_advertisment_form.errors,
                    }
                )
            continue

    return counter


def export_vehicle_parts_to_ebay(num_items_to_export=10000, print_results=False):
    """
    Export vehicle parts to eBay
    """

    if settings.DEPLOYMENT != settings.DEPLOYMENT_LIVE:
        return False

    unexported_vehicle_parts = (
        VehiclePart.objects.filter(
            advertisments__shop=settings.SHOP_PARTAN,
            advertisments__organization_id=1,
            advertisments__active=1,
            advertisments__price__gte=17,
        )
        .exclude(advertisments__shop=settings.SHOP_EBAY)
        .exclude(Q(attributes__id=990501) | Q(categories__id__in=[493, 495, 496, 497, 498, 499, 500, 502, 503]))
        .annotate(qty=Sum("locations__num_in_location"))
        .filter(qty__gt=0)
        .order_by("id")[:num_items_to_export]
    )
    counter = 0
    for vehicle_part in unexported_vehicle_parts:

        data = {}

        try:
            partan_vehicle_part_advertisment = vehicle_part.advertisments.get(shop=settings.SHOP_PARTAN)
        except VehiclePartAdvertisment.DoesNotExist:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Partan advertisment does not exist!",
                    }
                )
            continue
        except VehiclePartAdvertisment.MultipleObjectsReturned:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": "Multiple Partan advertisments exist!",
                    }
                )
            continue
        else:
            unbound_ebay_advertisment_form = VehiclePartAdvertismentFormForEbay(
                vehicle_part=vehicle_part, user=partan_vehicle_part_advertisment.owner
            )

            for key, field in list(unbound_ebay_advertisment_form.fields.items()):
                data[key] = field.initial

            data["price"] = (partan_vehicle_part_advertisment.price * Decimal(1.15)).quantize(Decimal('0'))

        bound_ebay_advertisment_form = VehiclePartAdvertismentFormForEbay(
            data, vehicle_part=vehicle_part, user=partan_vehicle_part_advertisment.owner
        )

        if bound_ebay_advertisment_form.is_valid():
            vehicle_part_advertisment = bound_ebay_advertisment_form.save(commit=False)
            vehicle_part_advertisment.vehicle_part = vehicle_part
            vehicle_part_advertisment.manager = partan_vehicle_part_advertisment.manager
            vehicle_part_advertisment.organization_id = partan_vehicle_part_advertisment.organization_id
            vehicle_part_advertisment.save()
            success, error_message = vehicle_part_advertisment.create_or_update_ebay_product()
            if error_message:
                vehicle_part_advertisment.delete()
                if print_results:
                    print(
                        {
                            "vehicle_part_id": vehicle_part.id,
                            "error": error_message,
                        }
                    )
                continue
            if success:
                counter += 1
                print(counter)
        else:
            if print_results:
                print(
                    {
                        "vehicle_part_id": vehicle_part.id,
                        "error": bound_ebay_advertisment_form.errors,
                    }
                )
            continue

    return counter


def fix_broken_partan_advertisments():
    """
    Some old Partan vehicle part advertiments have no shop_product_id, this method deletes old advertiments
    and creates new ones.
    """
    vpas = VehiclePartAdvertisment.objects.filter(shop="partan", shop_product_id__isnull=True)
    for vpa in vpas:
        vehicle_part = vpa.vehicle_part
        unbound_partan_advertisment_form = VehiclePartAdvertismentFormForPartan(vehicle_part=vehicle_part)
        data = {}
        for key, field in list(unbound_partan_advertisment_form.fields.items()):
            data[key] = field.initial
        data["price"] = vpa.price
        bound_partan_advertisment_form = VehiclePartAdvertismentFormForPartan(
            data, vehicle_part=vehicle_part, user=vpa.owner
        )
        manager_id = vpa.manager_id
        organization_id = vpa.organization_id
        vpa.delete()
        if bound_partan_advertisment_form.is_valid():
            vehicle_part_advertisment = bound_partan_advertisment_form.save(commit=False)
            vehicle_part_advertisment.vehicle_part = vehicle_part
            vehicle_part_advertisment.manager_id = manager_id
            vehicle_part_advertisment.organization_id = organization_id
            vehicle_part_advertisment.save()
            success, error_message = vehicle_part_advertisment.create_or_update_partan_product()
            if error_message:
                print({"vehicle_part_id": vehicle_part.id, "error": error_message})
                continue
            else:
                print(("Created new vehicle part advertisment for vehicle part ID={}".format(vehicle_part.id)))
        else:
            print(
                {
                    "vehicle_part_id": vehicle_part.id,
                    "error": bound_partan_advertisment_form.errors,
                }
            )
            continue
    return True
