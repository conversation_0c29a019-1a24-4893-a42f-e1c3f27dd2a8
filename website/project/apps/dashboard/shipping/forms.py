from django import forms
from django.utils.translation import gettext_lazy as _

from project.apps.shipping.models import OrderAndItemCharges as Shipping, ShippingZone


class ShippingForm(forms.ModelForm):
    class Meta:
        model = Shipping
        exclude = ('code', 'owner', 'price_per_order', 'free_shipping_threshold')


class ShippingZoneForm(forms.ModelForm):
    class Meta:
        model = ShippingZone
        exclude = ('code', 'owner', 'price_per_order', 'free_shipping_threshold')


class ShippingSearchForm(forms.Form):
    name = forms.CharField(
        required=False,
        label=_('Name')
    )
    country = forms.CharField(
        required=False,
        label=_('Country')
    )
    weight_from = forms.DecimalField(
        required=False,
        label=_('Weight from')
    )
    weight_to = forms.DecimalField(
        required=False,
        label=_('Weight to')
    )
