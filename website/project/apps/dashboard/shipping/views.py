from django.contrib import messages
from django.urls import reverse
from django.http import HttpResponseRedirect, Http404
from django.utils.translation import gettext_lazy as _
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.db.models import Q
from oscar.views import sort_queryset

from .forms import ShippingForm, ShippingZoneForm, ShippingSearchForm

from project.accounts.models import Account
from project.apps.shipping.models import OrderAndItemCharges as Shipping, ShippingZone


class ShippingListView(ListView):
    model = Shipping
    context_object_name = 'shippings'
    template_name = 'oscar/dashboard/shipping/shipping_list.html'
    form_class = ShippingSearchForm

    def get_queryset(self):
        owner, subowner = Account.get_owner(self.request.user)
        qs = self.model.objects.filter(owner=owner)
        
        # Apply search filters
        form = self.form_class(self.request.GET)
        
        if form.is_valid():
            name = form.cleaned_data.get('name')
            country = form.cleaned_data.get('country')
            weight_from = form.cleaned_data.get('weight_from')
            weight_to = form.cleaned_data.get('weight_to')

            if name:
                qs = qs.filter(name__icontains=name)
            if country:
                qs = qs.filter(countries__printable_name__icontains=country).distinct()
            if weight_from is not None:
                qs = qs.filter(weight_from__gte=weight_from)
            if weight_to is not None:
                qs = qs.filter(weight_to__lte=weight_to)

        return sort_queryset(qs, self.request, ['name', 'weight_from', 'weight_to', 'price_per_item', 'price_per_item_express', 'modified_at'])

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data(**kwargs)
        ctx['form'] = self.form_class(self.request.GET)
        return ctx


class ShippingCreateView(CreateView):
    model = Shipping
    template_name = 'oscar/dashboard/shipping/shipping_form.html'
    form_class = ShippingForm

    def get_context_data(self, **kwargs):
        ctx = super(ShippingCreateView, self).get_context_data(**kwargs)
        ctx['title'] = _('Create shipping charge')
        return ctx

    def form_valid(self, form):
        owner, subowner = Account.get_owner(self.request.user)
        shipping = form.save(commit=False)
        shipping.owner = owner
        shipping.save()
        form.save_m2m()
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        messages.success(self.request, _("Shipping charge created"))
        return reverse('dashboard:shipping-list')


class ShippingUpdateView(UpdateView):
    model = Shipping
    template_name = 'oscar/dashboard/shipping/shipping_form.html'
    form_class = ShippingForm

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(self.request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ShippingUpdateView, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        ctx = super(ShippingUpdateView, self).get_context_data(**kwargs)
        ctx['title'] = _('Update shipping charge')
        return ctx

    def form_valid(self, form):
        super(ShippingUpdateView, self).form_valid(form)
        messages.success(self.request, _("Shipping charge updated"))
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        return reverse('dashboard:shipping-list')


class ShippingDeleteView(DeleteView):
    model = Shipping
    template_name = 'oscar/dashboard/shipping/shipping_delete.html'
    context_object_name = 'shipping'

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(self.request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ShippingDeleteView, self).dispatch(request, *args, **kwargs)

    def get_success_url(self):
        messages.warning(self.request, _("Shipping charge deleted"))
        return reverse('dashboard:shipping-list')


class ShippingZoneListView(ListView):
    model = ShippingZone
    template_name = 'oscar/dashboard/shipping/shipping_zone_list.html'
    context_object_name = 'shipping_zones'

    def get_queryset(self):
        owner, subowner = Account.get_owner(self.request.user)
        qs = self.model.objects.filter(owner=owner)
        return qs


class ShippingZoneCreateView(CreateView):
    model = ShippingZone
    template_name = 'oscar/dashboard/shipping/shipping_zone_form.html'
    form_class = ShippingZoneForm

    def get_context_data(self, **kwargs):
        ctx = super(ShippingZoneCreateView, self).get_context_data(**kwargs)
        ctx['title'] = _('Create shipping zone')
        return ctx

    def form_valid(self, form):
        owner, subowner = Account.get_owner(self.request.user)
        shipping_zone = form.save(commit=False)
        shipping_zone.owner = owner
        shipping_zone.save()
        form.save_m2m()
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        messages.success(self.request, _("Shipping zone created"))
        return reverse('dashboard:shipping-zone-list')


class ShippingZoneUpdateView(UpdateView):
    model = ShippingZone
    template_name = 'oscar/dashboard/shipping/shipping_zone_form.html'
    form_class = ShippingZoneForm

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(self.request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ShippingZoneUpdateView, self).dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        ctx = super(ShippingZoneUpdateView, self).get_context_data(**kwargs)
        ctx['title'] = _('Update shipping zone')
        return ctx

    def form_valid(self, form):
        super(ShippingZoneUpdateView, self).form_valid(form)
        messages.success(self.request, _("Shipping zone updated"))
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        return reverse('dashboard:shipping-zone-list')


class ShippingZoneDeleteView(DeleteView):
    model = ShippingZone
    template_name = 'oscar/dashboard/shipping/shipping_zone_delete.html'
    context_object_name = 'shipping_zone'

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        owner, subowner = Account.get_owner(self.request.user)
        if obj and obj.owner != owner:
            raise Http404
        return super(ShippingZoneDeleteView, self).dispatch(request, *args, **kwargs)

    def get_success_url(self):
        messages.warning(self.request, _("Shipping zone deleted"))
        return reverse('dashboard:shipping-zone-list')
