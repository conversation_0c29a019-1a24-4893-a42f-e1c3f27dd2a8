from django.urls import path

from oscar.apps.dashboard.shipping import apps
from oscar.core.loading import get_class


class ShippingDashboardConfig(apps.ShippingDashboardConfig):

    name = 'project.apps.dashboard.shipping'

    default_permissions = [
        'is_staff',
    ]

    def ready(self):
        super().ready()

        self.list_view = get_class('dashboard.shipping.views', 'ShippingListView')
        self.create_view = get_class('dashboard.shipping.views', 'ShippingCreateView')
        self.update_view = get_class('dashboard.shipping.views', 'ShippingUpdateView')
        self.delete_view = get_class('dashboard.shipping.views', 'ShippingDeleteView')

        self.zone_list_view = get_class('dashboard.shipping.views', 'ShippingZoneListView')
        self.zone_create_view = get_class('dashboard.shipping.views', 'ShippingZoneCreateView')
        self.zone_update_view = get_class('dashboard.shipping.views', 'ShippingZoneUpdateView')
        self.zone_delete_view = get_class('dashboard.shipping.views', 'ShippingZoneDeleteView')

    def get_urls(self):
        urls = [
            path('', self.list_view.as_view(), name='shipping-list'),
            path('create/', self.create_view.as_view(), name='shipping-create'),
            path('update/<int:pk>/', self.update_view.as_view(), name='shipping-update'),
            path('delete/<int:pk>/', self.delete_view.as_view(), name='shipping-delete'),
            path('zones/', self.zone_list_view.as_view(), name='shipping-zone-list'),
            path('zone-create/', self.zone_create_view.as_view(), name='shipping-zone-create'),
            path('zone-update/<int:pk>/', self.zone_update_view.as_view(), name='shipping-zone-update'),
            path('zone-delete/<int:pk>/', self.zone_delete_view.as_view(), name='shipping-zone-delete'),
        ]
        return self.post_process_urls(urls)
