from datetime import timed<PERSON><PERSON>
from decimal import Decimal as D

from django import forms
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User

from oscar.apps.dashboard.orders.forms import OrderSearchForm as OscarOrderSearchForm
from oscar.forms.widgets import DateTimePickerInput

from project.accounts.models import Account, Branch
from project.apps.address.models import Country
from project.apps.order.models import Invoice, ShippingOrder, ShippingEventType
from project.apps.checkout.forms import PaymentForm
from project.apps.partner.models import Currency
from project.zipas_go.helpers import ZipasGoApiClient


class InvoiceForm(forms.ModelForm):
    class Meta:
        model = Invoice
        fields = ["vat_size", "invoice_type"]


class OrderSearchForm(OscarOrderSearchForm):
    email = forms.CharField(label=_("Email"), required=False)
    manager = forms.ModelChoiceField(label=_("Manager"), required=False, queryset=None)
    organization = forms.ModelChoiceField(label=_("Organization"), required=False, queryset=None)
    shipping_method = forms.ChoiceField(
        label=_("Shipping Method"),
        required=False,
        choices=[
            ("", "------"),
            ("fixed-price-economy", _("Economy shipping")),
            ("fixed-price-express", _("Express shipping")),
        ],
    )
    shop = forms.ChoiceField(
        label=_("Shop"),
        required=False,
        choices=[
            ("", "------"),
            (settings.SHOP_PARTAN, _("Partan")),
            (settings.SHOP_EBAY, _("eBay")),
            (settings.SHOP_RRR, _("RRR")),
            (settings.SHOP_RECAR, _("Recar")),
        ],
    )
    branch = forms.ChoiceField(
        label=_("Branch"),
        required=False,
        choices=[["", "------"], ["eu", "eu"]] + [[i, i] for i in list(settings.BRANCH_SETTINGS.keys())],
    )
    vehicle_part_id = forms.CharField(label=_("Vehicle Part ID"), required=False)
    shipment_id = forms.CharField(label=_("Shipment ID"), required=False)

    def __init__(self, *args, **kwargs):
        if "request" in kwargs:
            self.request = kwargs["request"]
            del kwargs["request"]
        super(OrderSearchForm, self).__init__(*args, **kwargs)
        owner, subowner = Account.get_owner(self.request.user)

        # Update manager queryset to use User instances
        if self.request.user.is_superuser:
            # Get all users that are managers by getting users associated with accounts that are managers
            self.fields["manager"].queryset = User.objects.filter(account__owner__managers__isnull=False).distinct()
        else:
            # Get users associated with the owner's manager accounts
            manager_users = (
                User.objects.filter(account__in=owner.account.managers.all()) if owner else User.objects.none()
            )
            self.fields["manager"].queryset = manager_users

        # Set organization queryset based on user permissions
        user = self.request.user
        if user.is_superuser:
            self.fields["organization"].queryset = user.organizations.model.objects.all()
        else:
            self.fields["organization"].queryset = user.organizations.all()

        # Change UPC field label
        self.fields['upc'].label = _("Partan shop ID")

        # Hide unwanted fields
        self.fields['partner_sku'].widget = forms.HiddenInput()
        self.fields['voucher'].widget = forms.HiddenInput()

        # Reorder fields to place vehicle_part_id after product_title
        field_order = [
            'order_number',
            'name',
            'product_title',
            'vehicle_part_id',
            'upc',
            'date_from',
            'date_to',
            'payment_method',
            'status',
            'email',
            'manager',
            'organization',
            'shipping_method',
            'shop',
            'branch',
            'shipment_id',
        ]
        self.order_fields(field_order)


class OrderDownloadForm(forms.Form):
    def __init__(self, *args, **kwargs):
        super(OrderDownloadForm, self).__init__(*args, **kwargs)
        fields = [
            ("number", _("Order number")),
            ("status", _("Status")),
            ("created_at", _("Purchase date")),
            ("email", _("Customer")),
            ("total_incl_tax", _("Total incl. tax")),
            ("total_excl_tax", _("Total excl. tax")),
            ("shipping_incl_tax", _("Shipping incl. tax")),
            ("shipping_excl_tax", _("Shipping excl. tax")),
            ("manager", _("Manager")),
            ("branch", _("Branch")),
            ("shipping_address", _("Shipping address")),
            ("billing_address", _("Billing address")),
            ("payment_source_code", _("Payment method")),
            ("line__title", _("Product name")),
            ("line__line_price_incl_tax", _("Product price")),
            ("line__quantity", _("Product quantity")),
            ("line__product__pap_vehicle_part__warehouse_code", _("Warehouse code")),
            ("line__product__pap_vehicle_part__vehicle__code", _("Vehicle code")),
            ("get_revenue_excl_taxes", _("Revenue excl. taxes")),
            ("get_shipment_id", _("Shipment ID")),
        ]
        for field_name, field_label in fields:
            self.fields[field_name] = forms.BooleanField(label=field_label, required=False)


class OrderZipasGoShippingOfferSearchForm(forms.Form):
    pickup_country_code = forms.ChoiceField(
        label=_("Pickup country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    pickup_address = forms.CharField(label=_("Pickup address"))
    pickup_city = forms.CharField(label=_("Pickup city"))
    pickup_postal_code = forms.CharField(label=_("Pickup postal code"))

    delivery_country_code = forms.ChoiceField(
        label=_("Delivery country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    delivery_country_state_code = forms.CharField(label=_("Delivery country state code"), required=False)
    delivery_address = forms.CharField(label=_("Delivery address"))
    delivery_city = forms.CharField(label=_("Delivery city"))
    delivery_postal_code = forms.CharField(label=_("Delivery postal code"))
    delivery_phone_number = forms.CharField(label=_("Delivery phone number"))
    delivery_email = forms.EmailField(label=_("Delivery email"), required=False)

    item_width = forms.IntegerField(label=_("Item width"), initial=10)
    item_length = forms.IntegerField(label=_("Item length"), initial=10)
    item_height = forms.IntegerField(label=_("Item height"), initial=10)
    item_weight = forms.DecimalField(label=_("Item weight"), initial=D("1.00"))
    item_units = forms.IntegerField(label=_("Item units"), initial=1)

    cod_amount = forms.DecimalField(label=_("COD amount"), required=False)
    declared_value = forms.DecimalField(label=_("Declared value"), required=False)
    declared_value_currency = forms.CharField(label=_("Declared value currency"), required=False)

    def __init__(self, *args, **kwargs):
        if "order" in kwargs:
            self.order = kwargs["order"]
            self.organization = self.order.get_organization()
            del kwargs["order"]
        super(OrderZipasGoShippingOfferSearchForm, self).__init__(*args, **kwargs)

        self.zipas_go = ZipasGoApiClient(self.order.zipas_go_api_credentials_id)

        self.fields["pickup_country_code"].initial = self.organization.country.pk
        self.fields["pickup_address"].initial = self.organization.address
        self.fields["pickup_city"].initial = self.organization.city
        self.fields["pickup_postal_code"].initial = self.organization.postal_code

        delivery_street = self.order.shipping_address.line1
        if self.order.shipping_address.line2 and self.order.shipping_address.line2 not in delivery_street:
            delivery_street = "{} {}".format(self.order.shipping_address.line1, self.order.shipping_address.line2)

        self.fields["delivery_country_code"].initial = self.order.shipping_address.country_id
        self.fields["delivery_country_state_code"].initial = self.order.shipping_address.state
        self.fields["delivery_address"].initial = delivery_street
        self.fields["delivery_city"].initial = self.order.shipping_address.line4
        self.fields["delivery_postal_code"].initial = self.order.shipping_address.postcode

        self.fields["delivery_email"].initial = self.order.user.email if self.order.user else self.order.guest_email
        self.fields["delivery_phone_number"].initial = self.order.billing_address.phone_number

        order_value = sum([i.line_price_incl_tax for i in self.order.lines.all()])
        self.fields["cod_amount"].initial = 0
        self.fields["declared_value"].initial = order_value
        self.fields["declared_value_currency"].initial = "EUR"

        if self.order.payment_source_code == PaymentForm.PAYMENT_METHOD_DPD_CASH:
            try:
                currency = Currency.objects.get(currency__startswith=self.order.shipping_address.country_id)
                currency_code = currency.currency
                currency_rate = currency.rate
            except Currency.DoesNotExist:
                currency = self.order.get_user_currency()
                currency_code = currency["currency"]
                currency_rate = D(currency["rate"])
            self.fields["cod_amount"].initial = (self.order.total_incl_tax * D(currency_rate)).quantize(D("0.00"))
            self.fields["declared_value"].initial = (order_value * D(currency_rate)).quantize(D("0.00"))
            self.fields["declared_value_currency"].initial = currency_code


class OrderDhlExpressShippingOfferSearchForm(forms.Form):
    shipper_country_code = forms.ChoiceField(
        label=_("Shipper country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    shipper_city_name = forms.CharField(label=_("Shipper city name"))
    shipper_postal_code = forms.CharField(label=_("Shipper postal code"))
    receiver_country_code = forms.ChoiceField(
        label=_("Receiver country"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    receiver_city_name = forms.CharField(label=_("Receiver city name"))
    receiver_address_line1 = forms.CharField(label=_("Receiver address (line 1)"))
    receiver_address_line2 = forms.CharField(label=_("Receiver address (line 2)"), required=False)
    receiver_postal_code = forms.CharField(label=_("Receiver postal code"))
    receiver_phone_number = forms.CharField(label=_("Receiver phone number"))
    package_length = forms.IntegerField(label=_("Package length"), initial=10)
    package_width = forms.IntegerField(label=_("Package width"), initial=10)
    package_height = forms.IntegerField(label=_("Package height"), initial=10)
    package_weight = forms.IntegerField(label=_("Package weight"), initial=1)
    planned_shipping_date = forms.DateTimeField(label=_("Planned shipping date"))
    is_customs_declarable = forms.BooleanField(label=_("Is customs declarable?"), required=False)
    use_digital_invoice = forms.BooleanField(label=_("Use DHL digital invoice?"), required=False)
    declared_value = forms.DecimalField(label=_("Declared value"), required=False)

    def __init__(self, *args, **kwargs):
        if "order" in kwargs:
            self.order = kwargs["order"]
            del kwargs["order"]
        super().__init__(*args, **kwargs)

        self.fields["shipper_country_code"].initial = settings.DHL_EXPRESS_DEFAULT_SENDER["country_code"]
        self.fields["shipper_city_name"].initial = settings.DHL_EXPRESS_DEFAULT_SENDER["city"]
        self.fields["shipper_postal_code"].initial = settings.DHL_EXPRESS_DEFAULT_SENDER["zipcode"]

        self.fields["receiver_country_code"].initial = self.order.shipping_address.country_id
        self.fields["receiver_city_name"].initial = self.order.shipping_address.line4

        self.fields["receiver_address_line1"].initial = self.order.shipping_address.line1

        self.fields["receiver_address_line2"].initial = self.order.shipping_address.line2

        self.fields["receiver_postal_code"].initial = self.order.shipping_address.postcode
        self.fields["receiver_phone_number"].initial = self.order.billing_address.phone_number

        self.fields["is_customs_declarable"].initial = not self.order.shipping_address.country.eu

        self.fields["planned_shipping_date"].initial = timezone.now()
        self.fields["use_digital_invoice"].initial = True

        line_price = self.order.lines.all()[0].line_price_incl_tax
        self.fields["declared_value"].initial = line_price


class OrderDbSchenkerShippingForm(forms.Form):
    shipper_name = forms.CharField(label=_("Shipper name"))
    shipper_street = forms.CharField(label=_("Shipper sreet"))
    shipper_city = forms.CharField(label=_("Shipper city"))
    shipper_postal_code = forms.CharField(label=_("Shipper postal code"))
    shipper_country_code = forms.ChoiceField(
        label=_("Shipper country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    shipper_phone = forms.CharField(label=_("Shipper phone"))
    shipper_email = forms.CharField(label=_("Shipper email"))
    shipper_contact_email = forms.CharField(label=_("Shipper contact email"))

    pickup_name = forms.CharField(label=_("Pickup name"))
    pickup_street = forms.CharField(label=_("Pickup sreet"))
    pickup_city = forms.CharField(label=_("Pickup city"))
    pickup_postal_code = forms.CharField(label=_("Pickup postal code"))
    pickup_country_code = forms.ChoiceField(
        label=_("Pickup country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    pickup_phone = forms.CharField(label=_("Pickup phone"))
    pickup_email = forms.CharField(label=_("Pickup email"))
    pickup_contact_email = forms.CharField(label=_("Pickup contact email"))

    consignee_name = forms.CharField(label=_("Consignee name"))
    consignee_street = forms.CharField(label=_("Consignee sreet"))
    consignee_city = forms.CharField(label=_("Consignee city"))
    consignee_postal_code = forms.CharField(label=_("Consignee postal code"))
    consignee_country_code = forms.ChoiceField(
        label=_("Consignee country code"),
        choices=[(i.iso_3166_1_a2, i.printable_name) for i in Country.objects.all()],
    )
    consignee_phone = forms.CharField(label=_("Consignee phone"))
    consignee_email = forms.CharField(label=_("Consignee email"))
    consignee_contact_email = forms.CharField(label=_("Consignee contact email"))

    shipment_gross_weight = forms.IntegerField(label=_("Gross weight"), initial=1)
    shipment_width = forms.IntegerField(label=_("Shipment width"), initial=10)
    shipment_length = forms.IntegerField(label=_("Shipment length"), initial=10)
    shipment_height = forms.IntegerField(label=_("Shipment height"), initial=10)
    shipment_volume = forms.DecimalField(label=_('Volume'), initial=1)

    pickup_date_from = forms.DateTimeField(label=_("Pickup date from"), widget=DateTimePickerInput)
    pickup_date_to = forms.DateTimeField(label=_("Pickup date to"), widget=DateTimePickerInput)

    def __init__(self, *args, **kwargs):
        if "order" in kwargs:
            self.order = kwargs["order"]
            self.organization = self.order.get_organization()
            del kwargs["order"]
        super().__init__(*args, **kwargs)

        self.fields["shipper_name"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["company_name"]
        self.fields["shipper_street"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["street_name"]
        self.fields["shipper_city"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["city"]
        self.fields["shipper_postal_code"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["zipcode"]
        self.fields["shipper_country_code"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["country_code"]
        self.fields["shipper_phone"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["phone_number"]
        self.fields["shipper_email"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["email"]
        self.fields["shipper_contact_email"].initial = settings.DB_SCHENKER_DEFAULT_SENDER["contact_email"]

        self.fields["pickup_name"].initial = self.organization.company_name
        self.fields["pickup_street"].initial = self.organization.address
        self.fields["pickup_city"].initial = self.organization.city
        self.fields["pickup_postal_code"].initial = self.organization.postal_code
        self.fields["pickup_country_code"].initial = self.organization.country.pk
        self.fields["pickup_phone"].initial = self.organization.phone
        self.fields["pickup_email"].initial = self.organization.email
        self.fields["pickup_contact_email"].initial = self.organization.contact_person_email

        consignee_name = self.order.shipping_address.company_name if self.order.shipping_address.company_name else self.order.shipping_address.first_name

        consignee_street = self.order.shipping_address.line1
        if self.order.shipping_address.line2 and self.order.shipping_address.line2 not in consignee_street:
            consignee_street = f"{self.order.shipping_address.line1} {self.order.shipping_address.line2}"

        self.fields["consignee_name"].initial = consignee_name
        self.fields["consignee_street"].initial = consignee_street
        self.fields["consignee_city"].initial = self.order.shipping_address.line4
        self.fields["consignee_postal_code"].initial = self.order.shipping_address.postcode
        self.fields["consignee_phone"].initial = self.order.billing_address.phone_number
        self.fields["consignee_country_code"].initial = self.order.shipping_address.country_id
        self.fields["consignee_email"].initial = self.order.user.email if self.order.user else self.order.guest_email
        self.fields["consignee_contact_email"].initial = self.order.user.email if self.order.user else self.order.guest_email

        self.fields["pickup_date_from"].initial = timezone.localtime()
        self.fields["pickup_date_to"].initial = timezone.localtime()

class OrderOtherShippingOrderForm(forms.ModelForm):
    carrier = forms.ChoiceField(choices=[])

    class Meta:
        exclude = ['order', 'shipment_id', 'cod_amount', 'label_base64', 'receiver_address']
        model = ShippingOrder

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['carrier'].choices = list(ShippingEventType.objects.all().values_list('code', 'name'))
