# -*- coding: utf-8 -*

import io
from io import BytesIO
import base64
import datetime
import json
import logging
import xlwt
import xml.etree.ElementTree as ET
from decimal import Decimal as D
from collections import OrderedDict

from django.contrib import messages
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from django.core.mail import mail_admins
from django.shortcuts import redirect
from django.urls import reverse, reverse_lazy
from django.db.models import Q, OuterRef, Subquery, F, Prefetch
from django.http import Http404, HttpResponse, HttpResponseRedirect
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    View,
    FormView,
    ListView,
    DetailView,
    CreateView,
    DeleteView,
)

from oscar.apps.dashboard.orders import views
from oscar.apps.payment.exceptions import PaymentError
from oscar.core.compat import UnicodeCSVWriter as CsvUnicodeWriter
from oscar.core.loading import get_class
from oscar.core.utils import format_datetime
from oscar.views import sort_queryset
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, PageBreak, Spacer

from .forms import (
    OrderZipasGoShippingOfferSearchForm,
    OrderSearchForm,
    OrderDownloadForm,
    OrderDhlExpressShippingOfferSearchForm,
    OrderOtherShippingOrderForm,
    OrderDbSchenkerShippingForm
)
from project.accounts.models import Account
from project.apps.address.models import Country
from project.apps.order.models import (
    Order,
    ShippingOrder,
    OrderNote,
    OrderStatusChange,
    Invoice,
    ShippingEventType,
    Line,
)
from project.apps.partner.models import Currency
from project.zipas_go.helpers import ZipasGoApiClient
from project.dhl_express.helpers import DhlExpressApiClient
from project.db_schenker.helpers import DbSchenkerApiClient
from project.apps.pap.models import VehiclePart, VehiclePartLocationAllocation
from project.apps.order.helpers import DbSchenkerPriceCalculator


EventHandler = get_class("order.processing", "EventHandler")


logger = logging.getLogger(__name__)


def queryset_orders_for_user(user):
    queryset = (
        Order._default_manager.select_related(
            "user",
            "shipping_address",
            "billing_address",
            "shipping_address__country",
            "billing_address__country",
        )
        .prefetch_related("lines__product__subowner")
        .all()
    )
    if user.organizations.exists():
        organization = user.organizations.first()
        pap_organization = organization.__class__.objects.get(name="PAP")
        if organization.id != pap_organization.id:
            org_managers = organization.users.all()
            queryset = queryset.filter(lines__product__subowner__in=org_managers).distinct()
        else:
            orgs_having_own_ebay = organization.__class__.objects.exclude(
                ebay_api_credentials__in=pap_organization.ebay_api_credentials.all()
            )
            orgs_having_own_ebay__managers = []
            for org in orgs_having_own_ebay:
                orgs_having_own_ebay__managers.extend(list(org.users.all()))

            orgs_having_own_rrr = organization.__class__.objects.exclude(
                Q(rrr_api_credentials_id=pap_organization.rrr_api_credentials_id) | Q(rrr_api_credentials__isnull=True)
            )
            orgs_having_own_rrr__managers = []
            for org in orgs_having_own_rrr:
                orgs_having_own_rrr__managers.extend(list(org.users.all()))

            queryset = (
                queryset.exclude(
                    number__icontains="EBAY",
                    lines__product__subowner__in=orgs_having_own_ebay__managers,
                )
                .exclude(
                    number__icontains="RRR",
                    lines__product__subowner__in=orgs_having_own_rrr__managers,
                )
                .distinct()
            )

    return queryset


def queryset_invoices_for_user(user):
    owner, subowner = Account.get_owner(user)
    queryset = Invoice._default_manager.filter(owner=owner)
    return queryset


def get_order_for_user_or_404(user, number):
    try:
        return queryset_orders_for_user(user).get(number=number)
    except ObjectDoesNotExist:
        raise Http404()


def get_invoice_for_user_or_404(user, pk):
    try:
        return queryset_invoices_for_user(user).get(pk=pk)
    except ObjectDoesNotExist:
        raise Http404()


class OrderPrintView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super(OrderPrintView, self).dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        pdf_info = self.order.render_pdf()
        response = HttpResponse(pdf_info[1], content_type="%s" % pdf_info[2])
        response["Content-Disposition"] = 'attachment; filename="%s"' % pdf_info[0]
        return response


class OrderZipasGoShippingOfferSearchView(FormView):
    form_class = OrderZipasGoShippingOfferSearchForm
    template_name = "oscar/dashboard/orders/order_zipas_go_shipping_offers.html"
    success_url = None

    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["order"] = self.order
        return kwargs

    def form_valid(self, form):
        context_data = self.get_context_data(form=form)
        context_data["order"] = self.order

        cod_amount = form.cleaned_data["cod_amount"]

        pickup_address = form.cleaned_data["pickup_address"]
        pickup_city = form.cleaned_data["pickup_city"]

        delivery_address = form.cleaned_data["delivery_address"]
        delivery_city = form.cleaned_data["delivery_city"]
        delivery_country_state_code = form.cleaned_data["delivery_country_state_code"]
        delivery_email = form.cleaned_data["delivery_email"]
        delivery_phone_number = form.cleaned_data["delivery_phone_number"]
        declared_value = form.cleaned_data["declared_value"]
        declared_value_currency = form.cleaned_data["declared_value_currency"]

        if cod_amount is None:
            cod_amount = 0
        if declared_value is None:
            declared_value = 0
        if declared_value_currency is None:
            declared_value_currency = "EUR"

        parcel_data = {
            "cod_amount": float(cod_amount),
            "use_liability": False,
            "pickup_address": {
                "address": {
                    "country_code": form.cleaned_data["pickup_country_code"],
                    "postal_code": form.cleaned_data["pickup_postal_code"],
                }
            },
            "delivery_address": {
                "address": {
                    "country_code": form.cleaned_data["delivery_country_code"],
                    "postal_code": form.cleaned_data["delivery_postal_code"],
                },
                "terminal": None,
            },
            "items": [
                {
                    "width": form.cleaned_data["item_width"],
                    "length": form.cleaned_data["item_length"],
                    "height": form.cleaned_data["item_length"],
                    "weight": float(form.cleaned_data["item_weight"]),
                    "units": form.cleaned_data["item_units"],
                }
            ],
            "value": float(declared_value),
            "value_currency": declared_value_currency,
        }

        zipas_go = ZipasGoApiClient(self.order.zipas_go_api_credentials_id)
        status_code, response, error_message = zipas_go.get_offers(json.dumps(parcel_data))

        if response:
            context_data["shipping_offers"] = response["offers"]
            context_data["parcel_data"] = parcel_data
            context_data["pickup_address"] = pickup_address
            context_data["pickup_city"] = pickup_city
            context_data["delivery_address"] = delivery_address
            context_data["delivery_city"] = delivery_city
            context_data["delivery_country_state_code"] = delivery_country_state_code
            context_data["delivery_email"] = delivery_email
            context_data["delivery_phone_number"] = delivery_phone_number
            context_data["cod_amount"] = cod_amount
            context_data["declared_value"] = str(declared_value)
            context_data["declared_value_currency"] = declared_value_currency
            if not response["offers"]:
                messages.info(self.request, _("System did not find any shipping offer"))
        if error_message:
            messages.error(
                self.request,
                _("System failed to get shipping offers. The failure reason: %s") % error_message,
            )
        return self.render_to_response(context_data)


class OrderZipasGoShippingOrderCreateView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        service_code = request.POST.get("service_code")
        carrier_name = request.POST.get("carrier_name")
        shipment_price = request.POST.get("shipment_price")

        pickup_country_code = request.POST.get("pickup_country_code")
        pickup_address = request.POST.get("pickup_address")
        pickup_city = request.POST.get("pickup_city")
        pickup_postal_code = request.POST.get("pickup_postal_code")

        delivery_country_code = request.POST.get("delivery_country_code")
        delivery_country_state_code = request.POST.get("delivery_country_state_code")
        delivery_address = request.POST.get("delivery_address")
        delivery_city = request.POST.get("delivery_city")
        delivery_postal_code = request.POST.get("delivery_postal_code")
        delivery_email = request.POST.get("delivery_email")
        delivery_phone_number = request.POST.get("delivery_phone_number")

        item_units = request.POST.get("item_units")
        item_weight = request.POST.get("item_weight")
        item_width = request.POST.get("item_width")
        item_length = request.POST.get("item_length")
        item_height = request.POST.get("item_height")
        cod_amount = request.POST.get("cod_amount")
        declared_value = request.POST.get("declared_value")
        declared_value_currency = request.POST.get("declared_value_currency")

        cod_amount = D(cod_amount) if cod_amount else D("0.00")

        data = {
            "option": 1,
            "cod_amount": float(cod_amount),
            "service_code": service_code,
            "use_liability": False,
            "importer_identifier": "",
            "pickup_address": {
                "address": {
                    "country_code": pickup_country_code,
                    "country_state_code": None,
                    "postal_code": pickup_postal_code,
                    "company_name": settings.ZIPAS_GO_DEFAULT_SENDER["company_name"],
                    "address": pickup_address,
                    "city": pickup_city,
                },
                "contact_name": settings.ZIPAS_GO_DEFAULT_SENDER["contact_name"],
                "phone_number": settings.ZIPAS_GO_DEFAULT_SENDER["phone_number"],
                "email": settings.ZIPAS_GO_DEFAULT_SENDER["email"],
                "eori": None,
                "vat": None,
            },
            "delivery_address": {
                "address": {
                    "country_code": delivery_country_code,
                    "country_state_code": delivery_country_state_code if delivery_country_state_code != '' else None,
                    "postal_code": delivery_postal_code,
                    "company_name": self.order.shipping_address.company_name,
                    "address": delivery_address,
                    "city": delivery_city,
                },
                "terminal": None,
                "contact_name": self.order.shipping_address.first_name,
                "phone_number": delivery_phone_number,
                "email": delivery_email,
                "eori": None,
                "vat": None,
            },
            "export_items": [],
            "reference": f"Order #{self.order.number}",
            "export_reason": 1,
            "items": [
                {
                    "width": float(item_width),
                    "length": float(item_length),
                    "height": float(item_height),
                    "weight": float(item_weight),
                    "units": int(item_units),
                }
            ],
        }

        order_value = sum([i.line_price_incl_tax for i in self.order.lines.all()])
        declared_value = declared_value or order_value

        for line in self.order.lines.all():
            item = {
                "origin_country_code": pickup_country_code,
                "description": line.title,
                "currency": declared_value_currency,
                "value": float(((line.unit_price_incl_tax / order_value) * D(declared_value)).quantize(D("0.00"))),
                "units": line.quantity,
                "hs_code": None,
            }
            data["export_items"].append(item)

        zipas_go = ZipasGoApiClient(self.order.zipas_go_api_credentials_id)

        status_code, response, error_message = zipas_go.create_shipment(json.dumps(data))

        if str(status_code) == "201" and response:
            if 'error_message' not in response:
                shipment_number = response["number"]
                messages.success(
                    request,
                    "Shipping order created successfully, shipment ID={}".format(shipment_number),
                )
                self.order.zipas_go_shipment_id = shipment_number
                self.order.save()

                tracking_number = ""
                label_base64 = ""

                package_number = response['packages'][0]['number']
                tracking_number = response['packages'][0]['external_number']

                _status_code, _response, _error_message = zipas_go.get_label(package_number)
                if str(_status_code) == "200" and _response:
                    label_base64 = _response['labels'][0]['content']

                ShippingOrder.objects.create(
                    order=self.order,
                    shipment_price=D(shipment_price),
                    shipment_id=shipment_number,
                    cod_amount=cod_amount,
                    tracking_number=tracking_number or settings.ZIPAS_GO_TRACKING_NUMBER_NOT_AVAILABLE_TEXT,
                    receiver_address=self.order.shipping_address.search_text,
                    label_base64=label_base64,
                    carrier=carrier_name,
                )
            else:
                error_message = response['error_message']
                messages.error(
                    request,
                    "Failed to create shipping order, failure reason: {}, Data: {}".format(error_message, data),
                )
        else:
            messages.error(
                request,
                "Failed to create shipping order, failure reason: {}".format(error_message),
            )
        return HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))


class OrderCancelZipasGoShippingOrderView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        zipas_go = ZipasGoApiClient(self.order.zipas_go_api_credentials_id)
        status_code, response, error_message = zipas_go.cancel_shipment(self.order.zipas_go_shipment_id)
        if response:
            self.order.zipas_go_shipment_id = ""
            self.order.save()
            self.order.shipping_orders.all().delete()
            messages.success(request, "Shipping order canceled successfully")
        else:
            messages.error(
                request,
                "Failed to cancel shipping order! Failure reason: {}".format(error_message),
            )
        response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderPrintZipasGoShipmentLabelView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        try:
            shipping_order = self.order.shipping_orders.all()[0]
        except IndexError:
            shipping_order = None

        if shipping_order:
            pdf_content = base64.b64decode(shipping_order.label_base64)
            response = HttpResponse(pdf_content, content_type="application/pdf")
            response["Content-Disposition"] = "attachment; filename=label.pdf"
        else:
            messages.error(request, "Failed to print shipment label!")
            response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderPrintDhlExpressShipmentLabelView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        try:
            shipping_order = self.order.shipping_orders.all()[0]
        except IndexError:
            shipping_order = None

        if shipping_order:
            pdf_content = base64.b64decode(shipping_order.label_base64)
            response = HttpResponse(pdf_content, content_type="application/pdf")
            response["Content-Disposition"] = "attachment; filename=label.pdf"
        else:
            messages.error(request, "Failed to print shipment label!")
            response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderCancelDhlExpressShippingOrderView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        self.order.dhl_express_shipment_id = ""
        self.order.save()
        self.order.shipping_orders.all().delete()
        messages.success(request, "Shipping order canceled successfully")
        response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderDhlExpressShippingOfferSearchView(FormView):
    form_class = OrderDhlExpressShippingOfferSearchForm
    template_name = "oscar/dashboard/orders/order_dhl_express_shipping_offers.html"
    success_url = None

    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["order"] = self.order
        return kwargs

    def form_valid(self, form):
        vat_rate = (D(settings.OSCAR_VAT_SIZE) / 100).quantize(D("0.00"))

        context_data = self.get_context_data(form=form)
        context_data["order"] = self.order

        receiver_address_line1 = form.cleaned_data["receiver_address_line1"]
        receiver_address_line2 = form.cleaned_data["receiver_address_line2"]

        if receiver_address_line2 is None:
            receiver_address_line2 = ""

        planned_shipping_date_and_time = form.cleaned_data["planned_shipping_date"]
        use_digital_invoice = form.cleaned_data["use_digital_invoice"]
        declared_value = form.cleaned_data["declared_value"]
        if declared_value is None:
            declared_value = ""

        if planned_shipping_date_and_time < timezone.now():
            planned_shipping_date_and_time = timezone.now() + datetime.timedelta(hours=1)

        planned_shipping_date_and_time = (
            f"{planned_shipping_date_and_time.strftime('%Y-%m-%dT%H:%M:%S GMT%z')[:-2]}:00"
        )

        shipment_data = {
            "customerDetails": {
                "shipperDetails": {
                    "postalCode": form.cleaned_data["shipper_postal_code"],
                    "cityName": form.cleaned_data["shipper_city_name"],
                    "countryCode": form.cleaned_data["shipper_country_code"],
                },
                "receiverDetails": {
                    "postalCode": form.cleaned_data["receiver_postal_code"],
                    "cityName": form.cleaned_data["receiver_city_name"],
                    "countryCode": form.cleaned_data["receiver_country_code"],
                },
            },
            "accounts": [{"number": "*********", "typeCode": "shipper"}],
            "plannedShippingDateAndTime": planned_shipping_date_and_time,
            "unitOfMeasurement": "metric",
            "isCustomsDeclarable": form.cleaned_data["is_customs_declarable"],
            "packages": [
                {
                    "weight": form.cleaned_data["package_weight"],
                    "dimensions": {
                        "length": form.cleaned_data["package_length"],
                        "width": form.cleaned_data["package_width"],
                        "height": form.cleaned_data["package_height"],
                    },
                },
            ],
        }

        dhl_express = DhlExpressApiClient()
        status_code, response, error_message = dhl_express.create_rates_request(json.dumps(shipment_data))

        if response:
            products = response["products"]

            _products = []
            for product in products:
                if product["productCode"] in ["P", "U", "H", "W", "D", "N"]:
                    try:
                        receiver_country = Country.objects.get(
                            iso_3166_1_a2=form.cleaned_data["receiver_country_code"]
                        )
                    except Country.DoesNotExist:
                        receiver_country = None
                    if receiver_country and receiver_country.eu:
                        product["totalPrice"][0]["price"] = (
                            D(product["totalPrice"][0]["price"]) / D(1 + vat_rate)
                        ).quantize(D("0.00"))
                    _products.append(product)

            context_data["shipping_offers"] = _products
            context_data["shipment_data"] = shipment_data
            context_data["receiver_phone_number"] = form.cleaned_data["receiver_phone_number"]
            context_data["receiver_address_line1"] = receiver_address_line1
            context_data["receiver_address_line2"] = receiver_address_line2

            context_data["planned_shipping_date_and_time"] = planned_shipping_date_and_time
            context_data["use_digital_invoice"] = use_digital_invoice
            context_data["declared_value"] = declared_value

            if not response["products"]:
                messages.info(self.request, _("System did not find any shipping offers"))
        else:
            messages.error(
                self.request,
                _("System failed to get shipping offers. The failure reason: %s") % error_message,
            )

        return self.render_to_response(context_data)


class OrderDhlExpressShippingOrderCreateView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        shipper_country_code = request.POST.get("shipper_country_code")
        shipper_city_name = request.POST.get("shipper_city_name")
        shipper_postal_code = request.POST.get("shipper_postal_code")
        receiver_country_code = request.POST.get("receiver_country_code")
        receiver_city_name = request.POST.get("receiver_city_name")
        receiver_address_line1 = request.POST.get("receiver_address_line1")
        receiver_address_line2 = request.POST.get("receiver_address_line2")
        receiver_postal_code = request.POST.get("receiver_postal_code")
        receiver_phone_number = request.POST.get("receiver_phone_number")
        is_customs_declarable = request.POST.get("is_customs_declarable")
        use_digital_invoice = request.POST.get("use_digital_invoice")
        declared_value = request.POST.get("declared_value")
        planned_shipping_date_and_time = request.POST.get("planned_shipping_date_and_time")
        product_code = request.POST.get("product_code")
        package_weight = request.POST.get("package_weight")
        package_width = request.POST.get("package_width")
        package_length = request.POST.get("package_length")
        package_height = request.POST.get("package_height")
        shipment_price = request.POST.get("shipment_price")

        dhl_express = DhlExpressApiClient()

        product_category_en = self.order.lines.all()[0].product.categories.first().name_en

        data = {
            "plannedShippingDateAndTime": planned_shipping_date_and_time,
            "pickup": {"isRequested": False},
            "productCode": product_code,
            "accounts": [{"number": "*********", "typeCode": "shipper"}],
            "customerReferences": [{"value": f"Order #{self.order.number}", "typeCode": "CU"}],
            "customerDetails": {
                "shipperDetails": {
                    "postalAddress": {
                        "postalCode": shipper_postal_code,
                        "cityName": shipper_city_name,
                        "countryCode": shipper_country_code,
                        "addressLine1": settings.DHL_EXPRESS_DEFAULT_SENDER["street_name"],
                    },
                    "contactInformation": {
                        "phone": settings.DHL_EXPRESS_DEFAULT_SENDER["phone_number"],
                        "companyName": settings.DHL_EXPRESS_DEFAULT_SENDER["company_name"],
                        "fullName": settings.DHL_EXPRESS_DEFAULT_SENDER["contact_name"],
                    },
                },
                "receiverDetails": {
                    "postalAddress": {
                        "postalCode": receiver_postal_code,
                        "cityName": receiver_city_name,
                        "countryCode": receiver_country_code,
                        "addressLine1": receiver_address_line1,
                        "addressLine2": (receiver_address_line2 if receiver_address_line2 else "."),
                    },
                    "contactInformation": {
                        "phone": receiver_phone_number,
                        "companyName": (
                            self.order.shipping_address.company_name
                            if self.order.shipping_address.company_name
                            else self.order.shipping_address.first_name
                        ),
                        "fullName": self.order.shipping_address.first_name,
                    },
                },
            },
            "content": {
                "packages": [
                    {
                        "weight": float(package_weight),
                        "dimensions": {
                            "length": int(package_length),
                            "width": int(package_width),
                            "height": int(package_height),
                        },
                    }
                ],
                "isCustomsDeclarable": is_customs_declarable == "True",
                "description": product_category_en,
                "incoterm": "DAP",
                "unitOfMeasurement": "metric",
            },
        }

        if is_customs_declarable == "True":
            try:
                invoice = self.order.invoice_set.first()
            except AttributeError:
                messages.error(
                    request,
                    "Failed to create shipping order. Order #%s has no invoice!" % self.order.number,
                )
                return HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))

            line_price = self.order.lines.all()[0].line_price_incl_tax
            line_description = self.order.lines.all()[0].product.title_en
            declared_value = declared_value or line_price

            if use_digital_invoice == "True":
                data["valueAddedServices"] = [{"serviceCode": "WY"}]

            data["outputImageProperties"] = {
                "encodingFormat": "pdf",
                "imageOptions": [
                    {
                        "typeCode": "invoice",
                        "isRequested": True,
                        "invoiceType": "commercial",
                    }
                ],
            }
            data["accounts"].append({"number": "*********", "typeCode": "payer"})
            data["content"]["declaredValue"] = float(declared_value)
            data["content"]["declaredValueCurrency"] = "EUR"
            data["content"]["exportDeclaration"] = {
                "lineItems": [
                    {
                        "number": 1,
                        "description": line_description,
                        "price": float(declared_value),
                        "quantity": {"value": 1, "unitOfMeasurement": "BOX"},
                        "manufacturerCountry": "DE",
                        "weight": {
                            "netValue": float(package_weight),
                            "grossValue": float(package_weight),
                        },
                    }
                ],
                "invoice": {
                    "number": invoice.get_number(),
                    "date": invoice.date_created.strftime("%Y-%m-%d"),
                },
                "exportReason": "Sale",
                "exportReasonType": "permanent",
            }

        status_code, response, error_message = dhl_express.create_shipment(json.dumps(data))

        if str(status_code) == "201" and response:
            shipment_id = response["shipmentTrackingNumber"]
            messages.success(
                request,
                "Shipping order created successfully, shipment ID={}".format(shipment_id),
            )
            self.order.dhl_express_shipment_id = shipment_id
            self.order.save()

            label_base64 = [i["content"] for i in response["documents"] if i["typeCode"] == "label"][0]

            ShippingOrder.objects.create(
                order=self.order,
                shipment_price=D(shipment_price),
                shipment_id=shipment_id,
                tracking_number=shipment_id,
                cod_amount=0,
                receiver_address=self.order.shipping_address.search_text,
                label_base64=label_base64,
                carrier="DHL",
            )
        else:
            digital_invoice_not_supported = (
                "7008: The requested Special Service Code  #/valueAddedServices/0/serviceCode 'WY' is not available"
            )

            messages.error(
                request,
                "Failed to create shipping order, failure reason: {}".format(error_message),
            )

            if digital_invoice_not_supported in error_message:
                messages.warning(
                    request,
                    'The destination country does not support the DHL digital invoice. Please uncheck the "Use DHL digital invoice?" checkbox below and try again.',
                )

            return HttpResponseRedirect(
                reverse(
                    "dashboard:order-dhl-express-shipping-offer-search",
                    args=[self.order.id],
                )
            )

        return HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))


class OrderDbSchekerShippingOrderCreateView(FormView):
    form_class = OrderDbSchenkerShippingForm
    template_name = "oscar/dashboard/orders/order_db_schenker_shipping_order.html"
    success_url = None

    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["order"] = self.order
        return kwargs

    def get_success_url(self):
        url = reverse("dashboard:order-detail", args=[self.order.number])
        return url

    def form_valid(self, form):
        form_data = form.cleaned_data
        db_schenker = DbSchenkerApiClient()
        data = {
            'request_id': timezone.now().timestamp,
            'order_number': self.order.number,
            'shipper_contact_email': form_data["shipper_contact_email"],
            'shipper_name': form_data["shipper_name"],
            'shipper_email': form_data["shipper_email"],
            'shipper_phone': form_data["shipper_phone"],
            'shipper_postal_code': form_data["shipper_postal_code"],
            'shipper_country_code': form_data["shipper_country_code"],
            'shipper_city': form_data["shipper_city"],
            'shipper_street': form_data["shipper_street"],
            'pickup_contact_email': form_data["pickup_contact_email"],
            'pickup_name': form_data["pickup_name"],
            'pickup_email': form_data["pickup_email"],
            'pickup_phone': form_data["pickup_phone"],
            'pickup_postal_code': form_data["pickup_postal_code"],
            'pickup_country_code': form_data["pickup_country_code"],
            'pickup_city': form_data["pickup_city"],
            'pickup_street': form_data["pickup_street"],
            'consignee_contact_email': form_data["consignee_contact_email"],
            'consignee_name': form_data["consignee_name"],
            'consignee_email': form_data["consignee_email"],
            'consignee_phone': form_data["consignee_phone"],
            'consignee_postal_code': form_data["consignee_postal_code"],
            'consignee_country_code': form_data["consignee_country_code"],
            'consignee_city': form_data["consignee_city"],
            'consignee_street': form_data["consignee_street"],
            'shipment_gross_weight': form_data["shipment_gross_weight"],
            'shipment_length': form_data["shipment_length"],
            'shipment_width': form_data["shipment_width"],
            'shipment_height': form_data["shipment_height"],
            'shipment_volume': form_data["shipment_volume"],
            'pickup_date_from': form_data["pickup_date_from"].strftime('%Y-%m-%dT%H:%M:%S%z')[:-2] + ':00',
            'pickup_date_to': form_data["pickup_date_to"].strftime('%Y-%m-%dT%H:%M:%S%z')[:-2] + ':00',
        }
        status_code, response, error_message = db_schenker.create_shipment(data)

        if str(status_code) == "200" and response:
            root = ET.fromstring(response)

            shipment_id = root[0][0].find('out/bookingId').text
            label_base64 = root[0][0].find('out/barcodeDocument').text

            organization = self.order.get_organization()
            price_calculator = DbSchenkerPriceCalculator(organization.id, data)
            shipment_price = price_calculator.calculate()

            messages.success(
                self.request,
                "Shipping order created successfully, shipment ID={}".format(shipment_id),
            )
            self.order.db_schenker_shipment_id = shipment_id
            self.order.save()

            tracking_number = shipment_id

            ShippingOrder.objects.create(
                order=self.order,
                shipment_price=D(shipment_price),
                shipment_id=shipment_id,
                tracking_number=tracking_number,
                cod_amount=0,
                receiver_address=self.order.shipping_address.search_text,
                label_base64=label_base64,
                carrier="DB Schenker",
            )
        else:
            messages.error(
                self.request,
                "Failed to create shipping order, failure reason: {}".format(error_message),
            )
            return self.form_invalid(form)

        return HttpResponseRedirect(self.get_success_url())


class OrderPrintDbSchenkerShipmentLabelView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        try:
            shipping_order = self.order.shipping_orders.all()[0]
        except IndexError:
            shipping_order = None

        if shipping_order:
            pdf_content = base64.b64decode(shipping_order.label_base64)
            response = HttpResponse(pdf_content, content_type="application/pdf")
            response["Content-Disposition"] = "attachment; filename=label.pdf"
        else:
            messages.error(request, "Failed to print shipment label!")
            response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderCancelDbSchenkerShippingOrderView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        db_schenker = DbSchenkerApiClient()
        data = {
            'request_id': timezone.now().timestamp,
            'booking_id': self.order.db_schenker_shipment_id
        }
        status_code, response, error_message = db_schenker.cancel_shipment(data)
        if response:
            self.order.db_schenker_shipment_id = ""
            self.order.save()
            self.order.shipping_orders.all().delete()
            messages.success(request, "Shipping order canceled successfully")
        else:
            messages.error(
                request,
                "Failed to cancel shipping order! Failure reason: {}".format(error_message),
            )
        response = HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))
        return response


class OrderMarkAsShippedView(View):
    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        try:
            shipping_order = self.order.shipping_orders.all().first()
        except ShippingOrder.DoesNotExist:
            messages.error(request, "Failed to find the related shipping order")
        else:
            if shipping_order.tracking_number and shipping_order.carrier:
                try:
                    shipping_event_type = ShippingEventType.objects.get(code=shipping_order.carrier)
                except ShippingEventType.DoesNotExist:
                    shipping_event_type = ShippingEventType.objects.create(
                        code=shipping_order.carrier,
                        name=shipping_order.carrier,
                        notes="https://www.17track.net/en",
                    )
                else:
                    self.order.shipping_events.create(
                        event_type=shipping_event_type,
                        notes=shipping_order.tracking_number,
                        date_created=timezone.now(),
                    )
                    messages.success(request, "The shipping event created successfully")
            else:
                messages.error(
                    request,
                    "The shipping order does not have the tracking number and/or the carrier name!",
                )

        return HttpResponseRedirect(reverse("dashboard:order-detail", args=[self.order.number]))


class OrderOtherShippingOrderCreateView(CreateView):
    form_class = OrderOtherShippingOrderForm
    template_name = "oscar/dashboard/orders/shipping_order.html"

    def dispatch(self, request, *args, **kwargs):
        try:
            self.order = Order.objects.get(id=kwargs["order_id"])
        except Order.DoesNotExist:
            raise Http404()

        if not request.user.is_superuser:
            owner, subowner = Account.get_owner(request.user)
            if self.order.owner != owner:
                raise Http404()

        return super().dispatch(request, *args, **kwargs)

    def get_success_url(self):
        url = reverse("dashboard:order-detail", args=[self.order.number])
        return url

    def form_valid(self, form):
        self.object = form.save(commit=False)
        self.object.order = self.order
        self.object.cod_amount = 0
        self.object.shipment_id = self.object.tracking_number
        self.object.save()
        self.order.other_shipment_id = self.object.shipment_id
        self.order.save()
        messages.success(self.request, "The shipping order created successfully")
        return HttpResponseRedirect(self.get_success_url())


class OrderListView(views.OrderListView):
    form_class = OrderSearchForm
    download_form_class = OrderDownloadForm
    paginate_by = 100
    actions = (
        "download_selected_orders",
        "download_selected_orders_advanced",
        "download_selected_orders_pdf",
        "download_selected_shipping_orders",
        "download_filtered_all_shipping_orders",
        "download_selected_cod_shipping_orders",
        "download_filtered_cod_shipping_orders",
    )

    def dispatch(self, request, *args, **kwargs):
        # base_queryset is equal to all orders the user is allowed to access
        self.base_queryset = queryset_orders_for_user(request.user).order_by("-date_placed")
        return super(views.OrderListView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        if not hasattr(self, 'form'):
            if self.request.GET:
                self.form = self.form_class(self.request.GET, request=self.request)
            else:
                self.form = self.form_class(request=self.request)

        queryset = self.base_queryset

        if self.form.is_valid():
            email = self.form.cleaned_data["email"]
            manager = self.form.cleaned_data["manager"]
            branch = self.form.cleaned_data["branch"]
            vehicle_part_id = self.form.cleaned_data["vehicle_part_id"]
            shipment_id = self.form.cleaned_data["shipment_id"]
            organization = self.form.cleaned_data.get("organization")
            shipping_method = self.form.cleaned_data.get("shipping_method")
            shop = self.form.cleaned_data.get("shop")
            status = self.form.cleaned_data.get("status")
            payment_method = self.form.cleaned_data.get("payment_method")
            product_title = self.form.cleaned_data.get("product_title")
            name = self.form.cleaned_data.get("name")
            order_number = self.form.cleaned_data.get("order_number")

            if email:
                queryset = queryset.filter(email__icontains=email)

            if order_number:
                queryset = queryset.filter(number__istartswith=order_number)

            if manager:
                queryset = queryset.filter(lines__product__subowner=manager).distinct()

            if name:
                # If the value is two words, then assume they are first name and last name
                parts = name.split()
                if len(parts) == 1:
                    parts = [name, name]
                else:
                    parts = [parts[0], parts[1:]]

                filter = Q(user__first_name__icontains=parts[0])
                filter |= Q(user__first_name__icontains=parts[1])
                filter |= Q(billing_address__first_name__icontains=parts[0])
                filter |= Q(shipping_address__first_name__icontains=parts[0])
                filter |= Q(billing_address__first_name__icontains=parts[1])
                filter |= Q(shipping_address__first_name__icontains=parts[1])
                queryset = queryset.filter(filter).distinct()

            if branch:
                queryset = queryset.filter(branch=branch)

            if vehicle_part_id:
                queryset = queryset.filter(lines__product__pap_product_id=vehicle_part_id)
                
            if shipment_id:
                # Search in all possible shipment ID fields and in shipping_orders
                shipment_filter = (
                    Q(shipping_orders__shipment_id__icontains=shipment_id) |
                    Q(parcel_stars_shipment_id__icontains=shipment_id) |
                    Q(zipas_go_shipment_id__icontains=shipment_id) |
                    Q(dhl_express_shipment_id__icontains=shipment_id) |
                    Q(db_schenker_shipment_id__icontains=shipment_id) |
                    Q(other_shipment_id__icontains=shipment_id)
                )
                queryset = queryset.filter(shipment_filter).distinct()

            if product_title:
                queryset = queryset.filter(lines__title__icontains=product_title).distinct()

            upc = self.form.cleaned_data.get('upc')
            if upc:
                queryset = queryset.filter(lines__product__upc=upc).distinct()

            if shipping_method:
                queryset = queryset.filter(shipping_code=shipping_method)

            if payment_method:
                queryset = queryset.filter(sources__source_type__code=payment_method).distinct()

            if shop:
                if shop == settings.SHOP_PARTAN:
                    queryset = queryset.exclude(
                        Q(number__icontains=settings.SHOP_EBAY.upper())
                        | Q(number__icontains=settings.SHOP_RRR.upper())
                        | Q(number__icontains=settings.SHOP_RECAR.upper())
                    )
                else:
                    queryset = queryset.filter(number__icontains=shop.upper())

            if status:
                queryset = queryset.filter(status=status)

            date_from = self.form.cleaned_data.get("date_from")
            date_to = self.form.cleaned_data.get("date_to")

            if date_from:
                date_from = datetime.datetime.combine(date_from, datetime.time(0, 0, 0)).replace(tzinfo=None)
            if date_to:
                date_to = datetime.datetime.combine(date_to, datetime.time(23, 59, 59)).replace(tzinfo=None)

            if date_from and date_to:
                queryset = queryset.filter(date_placed__gte=date_from)
                queryset = queryset.filter(date_placed__lte=date_to)
            elif date_from:
                queryset = queryset.filter(date_placed__gte=date_from)
            elif date_to:
                queryset = queryset.filter(date_placed__lte=date_to)

            if organization:
                # Get all managers from the selected organization
                org_managers = organization.users.all()
                # Show orders that have at least one product from the organization's managers
                queryset = queryset.filter(lines__product__subowner__in=org_managers).distinct()
            elif not self.request.user.is_superuser:
                # If no organization is selected and user is not superuser,
                # show orders containing products from user's organization
                organization = self.request.user.organizations.first()
                if organization:
                    org_managers = organization.users.all()
                    queryset = queryset.filter(lines__product__subowner__in=org_managers).distinct()

        return queryset

    def get_download_form(self):
        if self.request.method == "POST":
            return self.download_form_class(self.request.POST)
        else:
            return self.download_form_class()

    def get_context_data(self, **kwargs):
        context = super(OrderListView, self).get_context_data(**kwargs)
        context["download_form"] = self.get_download_form()

        # Add all active filters to search_filters
        if hasattr(self, 'form') and self.form.is_valid():
            search_filters = []
            for field_name, field_value in self.form.cleaned_data.items():
                # Skip response_format field and empty values
                if field_name == 'response_format':
                    continue
                if not field_value or field_value == "":
                    continue
                if field_name in ['shipping_method', 'shop'] and field_value in dict(
                    self.form_class.base_fields[field_name].choices
                ):
                    # For choice fields, get the display value
                    display_value = dict(self.form_class.base_fields[field_name].choices)[field_value]
                    search_filters.append(f"{self.form_class.base_fields[field_name].label}: {display_value}")
                elif field_name in ['date_from', 'date_to']:
                    # Format dates
                    search_filters.append(
                        f"{self.form_class.base_fields[field_name].label}: {field_value.strftime('%Y-%m-%d')}"
                    )
                else:
                    # For all other fields, use the value directly
                    search_filters.append(f"{self.form_class.base_fields[field_name].label}: {field_value}")
            context['search_filters'] = search_filters

        if context["page_obj"]:
            context["min_page"] = context["page_obj"].number - 4
            context["max_page"] = context["page_obj"].number + 4
        return context

    def _download_cod_shipping_orders(self, request, orders):
        response = HttpResponse(content_type="application/vnd.ms-excel")
        response["Content-Disposition"] = "attachment; filename=shipping-orders-cod.xls"

        file_content = ""

        wb = xlwt.Workbook()
        ws = wb.add_sheet("Shipping orders")

        headers = [
            _("Order nr."),
            _("Order date"),
            _("Shipping date"),
            _("COD"),
            _("Shipping price"),
            _("Tracking number"),
            _("Shipping address"),
        ]
        for column, header in enumerate(headers):
            ws.write(0, column, str(header))

        row = 0
        for order in orders:
            try:
                currency = Currency.objects.get(currency__startswith=order.shipping_address.country_id).currency
            except (Currency.DoesNotExist, AttributeError):
                currency = order.user_currency

            for shipping_order in order.shipping_orders.all():
                export_values = [
                    order.number,
                    order.date_placed.strftime("%Y-%m-%d"),
                    shipping_order.shipment_date.strftime("%Y-%m-%d"),
                    "{} {}".format(shipping_order.cod_amount, currency),
                    shipping_order.shipment_price,
                    shipping_order.tracking_number,
                    shipping_order.receiver_address,
                ]
                for column, value in enumerate(export_values):
                    ws.write(row + 1, column, str(value))
                row += 1

        output = BytesIO()
        wb.save(output)
        response.write(output.getvalue())
        return response

    def _download_all_shipping_orders(self, request, orders):
        response = HttpResponse(content_type="application/vnd.ms-excel")
        response["Content-Disposition"] = "attachment; filename=shipping-orders.xls"

        wb = xlwt.Workbook()
        ws = wb.add_sheet("Shipping orders")

        headers = [
            _("Order nr."),
            _("Vehicle"),
            _("Vehicle part"),
            _("Year"),
            _("Warehouse code"),
            _("Part price"),
            _("Received shipping price"),
            _("Actual shipping price"),
            _("Platform"),
            _("Payment method"),
            _("Order created at"),
            _("Order status changed at"),
        ]
        for column, header in enumerate(headers):
            ws.write(0, column, str(header))

        # Optimize queryset to prefetch related data
        orders = orders.prefetch_related(
            'lines__product',
            'lines__product__subowner',
            'lines__product__owner',
            Prefetch(
                'status_changes',
                queryset=OrderStatusChange.objects.order_by('-date_created'),
                to_attr='prefetched_status_changes'
            )
        ).select_related(
            'user',
            'billing_address',
            'billing_address__country',
            'shipping_address',
            'shipping_address__country'
        ).annotate(
            shipping_price=Subquery(
                ShippingOrder.objects.filter(
                    order_id=OuterRef('id')
                ).values('shipment_price')[:1]
            )
        )

        # Get all product IDs and prefetch vehicle data
        product_ids = list(orders.values_list(
            'lines__product__pap_product_id', 
            flat=True
        ).distinct().exclude(lines__product__pap_product_id__isnull=True))

        # Get vehicle parts and codes in one query
        vehicle_parts = {
            vp.id: vp for vp in VehiclePart.objects.filter(id__in=product_ids)
        }

        vehicle_data = VehiclePartLocationAllocation.objects.filter(
            vehicle_part_location__vehicle_part_id__in=product_ids
        ).values('vehicle_part_location__vehicle_part_id', 'vehicle_part_location__vehicle__code').distinct()
        
        vehicle_codes = {}
        for item in vehicle_data:
            vp_id = item['vehicle_part_location__vehicle_part_id']
            vehicle_code = item['vehicle_part_location__vehicle__code']
            if vp_id not in vehicle_codes:
                vehicle_codes[vp_id] = []
            if vehicle_code:
                vehicle_codes[vp_id].append(vehicle_code)

        row = 0
        # Materialize queryset once to avoid multiple DB hits
        orders_list = list(orders)
        
        for order in orders_list:
            actual_shipping_price = order.shipping_price
            
            # Filter lines by manager if not superuser
            if not request.user.is_superuser:
                lines = [line for line in order.lines.all() 
                        if line.product and line.product.subowner == request.user]
            else:
                lines = list(order.lines.all())
                
            if not lines:  # Skip order if no lines match the manager filter
                continue
                
            num_lines = len(lines)

            # Get status change date once per order
            status_changed_at = ""
            if hasattr(order, 'prefetched_status_changes') and order.prefetched_status_changes:
                status_changed_at = order.prefetched_status_changes[0].date_created.strftime("%Y-%m-%d %H:%M")

            for line in lines:
                try:
                    vehicle_part = vehicle_parts.get(line.product.pap_product_id)
                    vehicle_codes_list = vehicle_codes.get(line.product.pap_product_id, [])
                    vehicle_code = vehicle_codes_list[0] if vehicle_codes_list else ""
                except (AttributeError, KeyError, IndexError):
                    vehicle_part = None
                    vehicle_code = ""

                export_values = [
                    order.number,
                    vehicle_code,
                    line.product.title if line.product else "",
                    vehicle_part.year if vehicle_part else "",
                    vehicle_part.warehouse_code if vehicle_part else "",
                    line.line_price_incl_tax,
                    (order.shipping_incl_tax / num_lines).quantize(D("0.00")),
                    (actual_shipping_price / num_lines).quantize(D("0.00")) if actual_shipping_price else "0.00",
                    order.platform,
                    order.payment_source_code,
                    order.date_placed.strftime("%Y-%m-%d %H:%M"),
                    status_changed_at,
                ]
                for column, value in enumerate(export_values):
                    ws.write(row + 1, column, str(value))
                row += 1

        output = BytesIO()
        wb.save(output)
        response.write(output.getvalue())
        return response

    def download_filtered_all_shipping_orders(self, request, orders):
        """Download all orders from the current filtered queryset"""
        if not hasattr(self, 'form'):
            if request.GET:
                self.form = self.form_class(request.GET, request=request)
                self.form.is_valid()  # Validate form if we have GET data
            else:
                self.form = self.form_class(request=request)
            
        # Use the current queryset
        queryset = self.get_queryset()
        return self._download_all_shipping_orders(request, queryset)

    def download_selected_cod_shipping_orders(self, request, orders):
        return self._download_cod_shipping_orders(request, orders)

    def download_filtered_cod_shipping_orders(self, request, orders):
        """Download all COD orders from the current filtered queryset"""
        if not hasattr(self, 'form'):
            if request.GET:
                self.form = self.form_class(request.GET, request=request)
                self.form.is_valid()  # Validate form if we have GET data
            else:
                self.form = self.form_class(request=request)
            
        # Use the current queryset
        queryset = self.get_queryset()
        return self._download_cod_shipping_orders(request, queryset)

    def download_selected_orders(self, request, orders):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=%s" % self.get_download_filename(request)
        writer = CsvUnicodeWriter(open_file=response)

        meta_data = (
            ("number", _("Order number")),
            ("value", _("Order value")),
            ("date", _("Date of purchase")),
            ("num_items", _("Number of items")),
            ("status", _("Order status")),
            ("customer", _("Customer email address")),
            ("shipping_address_name", _("Deliver to name")),
            ("billing_address_name", _("Bill to name")),
        )
        columns = OrderedDict()
        for k, v in meta_data:
            columns[k] = v

        writer.writerow(list(columns.values()))
        for order in orders:
            row = columns.copy()
            row["number"] = order.number
            row["value"] = order.total_incl_tax
            row["date"] = format_datetime(order.date_placed, "DATETIME_FORMAT")
            row["num_items"] = order.num_items
            row["status"] = order.status
            row["customer"] = order.email
            if order.shipping_address:
                row["shipping_address_name"] = order.shipping_address.name
            else:
                row["shipping_address_name"] = ""
            if order.billing_address:
                row["billing_address_name"] = order.billing_address.name
            else:
                row["billing_address_name"] = ""

            # encoded_values = [str(value).encode('utf8') for value in row.values()]
            writer.writerow(list(row.values()))
        return response

    def download_selected_orders_advanced(self, request, orders):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=%s" % self.get_download_filename(request)
        writer = CsvUnicodeWriter(open_file=response)

        meta_data = []
        download_form = self.get_download_form()
        if download_form.is_valid():
            for field_name, field in list(download_form.fields.items()):
                if download_form.cleaned_data.get(field_name, None):
                    meta_data.append((field_name, field.label))

            columns = OrderedDict()
            for k, v in meta_data:
                columns[k] = v

            writer.writerow(list(columns.values()))
            for order in orders:
                line_counter = 0
                for line in order.lines.all():
                    line_counter += 1
                    row = columns.copy()
                    download_lines = False
                    for key in list(row.keys()):
                        if key == "created_at":
                            row[key] = format_datetime(order.date_placed, "DATETIME_FORMAT")
                        elif key.startswith("line__"):
                            download_lines = True
                            if key == "line__line_price_incl_tax":
                                row[key] = (line.line_price_incl_tax / line.price_factor).quantize(D("0.00"))
                            if key in [
                                "line__product__pap_vehicle_part__warehouse_code",
                                "line__product__pap_vehicle_part__vehicle__code",
                            ]:
                                if not line.product:
                                    if key == "line__product__pap_vehicle_part__warehouse_code":
                                        row[key] = 'not available'
                                    if key == "line__product__pap_vehicle_part__vehicle__code":
                                        row[key] = 'not available'
                                else:
                                    try:
                                        vehicle_part = VehiclePart.objects.get(id=line.product.pap_product_id)
                                    except VehiclePart.DoesNotExist:
                                        vehicle_part = None

                                    vehicle_codes = list(
                                        VehiclePartLocationAllocation.objects.filter(
                                            shop_order_id=order.id,
                                            vehicle_part_location__vehicle_part_id=line.product.pap_product_id,
                                        )
                                        .values_list('vehicle_part_location__vehicle__code', flat=True)
                                        .distinct()
                                    )

                                    if key == "line__product__pap_vehicle_part__warehouse_code":
                                        row[key] = vehicle_part.warehouse_code if vehicle_part else ''
                                    if key == "line__product__pap_vehicle_part__vehicle__code":
                                        row[key] = ', '.join([i for i in vehicle_codes if i])
                            else:
                                row[key] = getattr(line, key[len("line__") :])
                        else:
                            attr = getattr(order, key)
                            row[key] = attr() if callable(attr) else attr

                    if download_lines or line_counter == 1:
                        # encoded_values = [str(value).encode('utf8') for value in row.values()]
                        writer.writerow(list(row.values()))
        return response

    def download_selected_orders_pdf(self, request, orders):
        """
        Generates a single PDF document containing the data for all selected orders.
        """

        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=20,
            leftMargin=20,
            topMargin=20,
            bottomMargin=15,
        )
        story = []

        for i, order in enumerate(orders):
            pdf_info = order.render_pdf()
            elements = pdf_info[3]  # Get the elements from the order's PDF

            # Add elements to the story
            story.extend(elements)

            # Add a page break between orders, but not after the last order
            if i < len(orders) - 1:
                story.append(PageBreak())

        try:
            doc.build(story)
        except Exception as e:
            # If there's an error building the PDF, try with adjusted spacing
            story = []
            for i, order in enumerate(orders):
                pdf_info = order.render_pdf()
                elements = pdf_info[3]

                # Remove any Spacer elements that might be too large
                elements = [
                    elem
                    for elem in elements
                    if not isinstance(elem, Spacer) or (isinstance(elem, Spacer) and elem.height <= 20)
                ]
                story.extend(elements)

                if i < len(orders) - 1:
                    story.append(PageBreak())

            doc.build(story)

        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="selected_orders.pdf"'
        buffer.close()

        return response

    def post(self, request, *args, **kwargs):
        action = request.POST.get('action', '')
        
        if action in ['download_filtered_all_shipping_orders', 'download_filtered_cod_shipping_orders']:
            return getattr(self, action)(request, None)
            
        selected_order_ids = request.POST.getlist('selected_order')
        
        if not selected_order_ids:
            messages.error(self.request, _("Please select some orders"))
            return redirect('dashboard:order-list')
            
        orders = self.get_queryset().filter(id__in=selected_order_ids)
        return getattr(self, action)(request, orders)


class OrderDetailView(views.OrderDetailView):
    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_superuser:
            obj = self.get_object()
            owner, subowner = Account.get_owner(request.user)
            if obj and obj.owner != owner:
                raise Http404()
        return super(OrderDetailView, self).dispatch(request, *args, **kwargs)

    def get_object(self, queryset=None):
        if not self.request.user.is_superuser:
            return get_order_for_user_or_404(self.request.user, self.kwargs["number"])
        else:
            return Order._default_manager.get(number=self.kwargs["number"])

    def change_line_statuses(self, request, order, lines, quantities):
        new_status = request.POST["new_status"].strip()
        if not new_status:
            messages.error(request, _("The new status '%s' is not valid") % new_status)
            return self.reload_page()

        if new_status == "Shipped" and order.is_shipping_order_required and not order.get_shipment_id():
            messages.error(
                request,
                _(
                    "The status 'Shipped' is not allowed for orders without shipment Id. Please create the shipment and provide the tracking information first."
                ),
            )
            return self.reload_page()

        errors = []
        location_msgs = []

        for line in lines:
            if new_status not in line.available_statuses():
                errors.append(
                    _("'%(status)s' is not a valid new status for line %(line_id)d")
                    % {"status": new_status, "line_id": line.id}
                )

        if errors:
            messages.error(request, "\n".join(errors))
            return self.reload_page()

        msgs = []
        for line in lines:
            msg = _("Status of line #%(line_id)d changed from '%(old_status)s' to '%(new_status)s'") % {
                "line_id": line.id,
                "old_status": line.status,
                "new_status": new_status,
            }
            msgs.append(msg)
            line.set_status(new_status)
        message = "\n".join(msgs)
        messages.info(request, message)

        order.notes.create(user=request.user, message=message, note_type=OrderNote.SYSTEM)

        handler = EventHandler()
        old_status = order.status
        try:
            handler.handle_order_status_change(order, new_status)
        except PaymentError as e:
            messages.error(request, _("Unable to change order status due to payment error: %s") % e)
        else:
            msg = _("Order status changed from '%(old_status)s' to '%(new_status)s'") % {
                "old_status": old_status,
                "new_status": new_status,
            }
            messages.info(request, msg)
            order.notes.create(user=request.user, message=msg, note_type=OrderNote.SYSTEM)

        if location_msgs:
            messages.info(
                request,
                _("You can take products for shipping from following locations:"),
            )
            for message in location_msgs:
                messages.info(request, message)

        if new_status == "Cancelled":
            if old_status in ["Shipped", "Paid"]:
                if order.invoice_set.all().count() == 1:
                    order.invoice_set.all()[0].create_credit_invoice()
            if old_status != "Shipped":
                handler.cancel_stock_allocations(order, lines, quantities)

                try:
                    order.cancel_pap_allocation()
                except Exception as ex:
                    logger.info(
                        "Failed to cancel pap allocation for order %s. Reason: %s",
                        order.number,
                        ex,
                    )

        if new_status == "Paid":
            order.create_invoice()
            
            # Send email to subowner if exists
            if order.manager:
                subject = f"Order #{order.number} has been paid"
                message = f"""
                <p>Order #{order.number} has been paid.</p>
                <p>Order details:</p>
                <ul>
                    <li>Total: {order.total_incl_tax} {order.currency}</li>
                    <li>Customer: {order.guest_email if order.is_anonymous else order.user.email}</li>
                </ul>
                """
                order.message_owner(subject, message)

        if new_status == "Shipped":
            handler.consume_stock_allocations(order, lines, quantities)

            try:
                order.consume_pap_allocation()
            except Exception as ex:
                logger.info(
                    "Failed to consume pap allocation for order %s. Reason: %s",
                    order.number,
                    ex,
                )

            if "RRR" not in order.number:
                order.inform_about_shipped_order()

        return self.reload_page()
