from django.urls import path

from oscar.apps.dashboard.orders import apps
from oscar.core.loading import get_class


class OrdersDashboardConfig(apps.OrdersDashboardConfig):
    name = 'project.apps.dashboard.orders'

    def ready(self):
        super().ready()
        self.order_list_view = get_class('dashboard.orders.views', 'OrderListView')
        self.order_detail_view = get_class('dashboard.orders.views', 'OrderDetailView')
        self.order_print_view = get_class('dashboard.orders.views', 'OrderPrintView')
        self.order_mark_as_shipped_view = get_class('dashboard.orders.views', 'OrderMarkAsShippedView')
        self.order_print_zipas_go_shipment_label_view = get_class(
            'dashboard.orders.views', 'OrderPrintZipasGoShipmentLabelView'
        )
        self.order_print_dhl_express_shipment_label_view = get_class(
            'dashboard.orders.views', 'OrderPrintDhlExpressShipmentLabelView'
        )
        self.order_print_db_schenker_shipment_label_view = get_class(
            'dashboard.orders.views', 'OrderPrintDbSchenkerShipmentLabelView'
        )
        self.order_cancel_zipas_go_shipping_order_view = get_class(
            'dashboard.orders.views', 'OrderCancelZipasGoShippingOrderView'
        )
        self.order_cancel_dhl_express_shipping_order_view = get_class(
            'dashboard.orders.views', 'OrderCancelDhlExpressShippingOrderView'
        )
        self.order_cancel_db_schenker_shipping_order_view = get_class(
            'dashboard.orders.views', 'OrderCancelDbSchenkerShippingOrderView'
        )
        self.order_zipas_go_shipping_offer_search_view = get_class(
            'dashboard.orders.views', 'OrderZipasGoShippingOfferSearchView'
        )
        self.order_dhl_express_shipping_offer_search_view = get_class(
            'dashboard.orders.views', 'OrderDhlExpressShippingOfferSearchView'
        )
        self.order_zipas_go_shipping_order_create_view = get_class(
            'dashboard.orders.views', 'OrderZipasGoShippingOrderCreateView'
        )
        self.order_dhl_express_shipping_order_create_view = get_class(
            'dashboard.orders.views', 'OrderDhlExpressShippingOrderCreateView'
        )
        self.order_db_schenker_shipping_order_create_view = get_class(
            'dashboard.orders.views', 'OrderDbSchekerShippingOrderCreateView'
        )
        self.order_other_shipping_order_create_view = get_class(
            'dashboard.orders.views', 'OrderOtherShippingOrderCreateView'
        )

    def get_urls(self):
        urls = [
            path('', self.order_list_view.as_view(), name='order-list'),
            path('statistics/', self.order_stats_view.as_view(), name='order-stats'),
            path('<number>/', self.order_detail_view.as_view(), name='order-detail'),
            path(
                '<number>/notes/<int:note_id>/',
                self.order_detail_view.as_view(),
                name='order-detail-note',
            ),
            path(
                '<number>/lines/<int:line_id>/',
                self.line_detail_view.as_view(),
                name='order-line-detail',
            ),
            path(
                '<number>/shipping-address/',
                self.shipping_address_view.as_view(),
                name='order-shipping-address',
            ),
            path('<int:order_id>/print/', self.order_print_view.as_view(), name='order-print'),
            path(
                '<int:order_id>/print-zipas-go-shipment-label/',
                self.order_print_zipas_go_shipment_label_view.as_view(),
                name='order-print-zipas-go-shipment-label',
            ),
            path(
                '<int:order_id>/print-dhl-express-shipment-label/',
                self.order_print_dhl_express_shipment_label_view.as_view(),
                name='order-print-dhl-express-shipment-label',
            ),
            path(
                '<int:order_id>/print-db-schenker-shipment-label/',
                self.order_print_db_schenker_shipment_label_view.as_view(),
                name='order-print-db-schenker-shipment-label',
            ),
            path(
                '<int:order_id>/cancel-zipas-go-shipping-order/',
                self.order_cancel_zipas_go_shipping_order_view.as_view(),
                name='order-cancel-zipas-go-shipping-order',
            ),
            path(
                '<int:order_id>/cancel-dhl-express-shipping-order/',
                self.order_cancel_dhl_express_shipping_order_view.as_view(),
                name='order-cancel-dhl-express-shipping-order',
            ),
            path(
                '<int:order_id>/cancel-db-schenker-shipping-order/',
                self.order_cancel_db_schenker_shipping_order_view.as_view(),
                name='order-cancel-db-schenker-shipping-order',
            ),
            path(
                '<int:order_id>/zipas-go-shipping-offer-search/',
                self.order_zipas_go_shipping_offer_search_view.as_view(),
                name='order-zipas-go-shipping-offer-search',
            ),
            path(
                '<int:order_id>/dhl-express-shipping-offer-search/',
                self.order_dhl_express_shipping_offer_search_view.as_view(),
                name='order-dhl-express-shipping-offer-search',
            ),
            path(
                '<int:order_id>/zipas-go-shipping-order-create/',
                self.order_zipas_go_shipping_order_create_view.as_view(),
                name='order-zipas-go-shipping-order-create',
            ),
            path(
                '<int:order_id>/dhl-express-shipping-order-create/',
                self.order_dhl_express_shipping_order_create_view.as_view(),
                name='order-dhl-express-shipping-order-create',
            ),
            path(
                '<int:order_id>/db-schenker-shipping-order-create/',
                self.order_db_schenker_shipping_order_create_view.as_view(),
                name='order-db-schenker-shipping-order-create',
            ),
            path(
                '<int:order_id>/other-shipping-order-create/',
                self.order_other_shipping_order_create_view.as_view(),
                name='order-other-shipping-order-create',
            ),
            path(
                '<int:order_id>/order-mark-as-shipped/',
                self.order_mark_as_shipped_view.as_view(),
                name='order-mark-as-shipped',
            ),
        ]
        return self.post_process_urls(urls)
