from django.apps import apps
from django.urls import path, include

from oscar.apps.dashboard.apps import DashboardConfig as CoreDashboardConfig
from oscar.core.loading import get_class


class DashboardConfig(CoreDashboardConfig):
    name = 'project.apps.dashboard'

    def ready(self):
        super().ready()
        self.pap_app = apps.get_app_config('pap_dashboard')

    def get_urls(self):
        from django.contrib.auth import views as auth_views

        urls = [
            path('', self.index_view.as_view(), name='index'),
            path('catalogue/', include(self.catalogue_app.urls[0])),
            path('reports/', include(apps.get_app_config('reports_dashboard').urls[0])),
            path('shipping/', include(self.shipping_app.urls[0])),
            path('orders/', include(self.orders_app.urls[0])),
            path('users/', include(self.users_app.urls[0])),
            path('pap/', include(self.pap_app.urls[0])),
            path('reviews/', include(self.reviews_app.urls[0])),
            path('login/', self.login_view.as_view(), name='login'),
            path('logout/', auth_views.LogoutView.as_view(next_page='/'), name='logout'),
        ]
        return self.post_process_urls(urls)
