from django.urls import path

from oscar.apps.dashboard.users import apps
from oscar.core.loading import get_class


class UsersDashboardConfig(apps.UsersDashboardConfig):

    name = 'project.apps.dashboard.users'

    def ready(self):
        super().ready()

        self.index_view = get_class('dashboard.users.views', 'IndexView')

        self.user_create_view = get_class('dashboard.users.views', 'UserCreateView')
        self.user_detail_view = get_class('dashboard.users.views', 'UserDetailView')

        self.branch_list_view = get_class('dashboard.users.views', 'BranchListView')
        # branch_create_view = get_class('dashboard.users.views', 'BranchCreateView')
        self.branch_update_view = get_class('dashboard.users.views', 'BranchUpdateView')
        # branch_delete_view = get_class('dashboard.users.views', 'BranchDeleteView')

    def get_urls(self):
        urls = [
            path('', self.index_view.as_view(), name='users-index'),
            path('add/', self.user_create_view.as_view(), name='user-add'),
            path('<int:pk>/', self.user_detail_view.as_view(), name='user-detail'),
            path('<int:pk>/password-reset/', self.password_reset_view.as_view(), name='user-password-reset'),
            path('list-branches/', self.branch_list_view.as_view(), name='branch-list'),
            path('edit-branch/<int:pk>/', self.branch_update_view.as_view(), name='branch-edit'),
        ]
        return self.post_process_urls(urls)
