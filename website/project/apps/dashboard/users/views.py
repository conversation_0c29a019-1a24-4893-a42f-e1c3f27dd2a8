from django.db.models import Q
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponseRedirect, Http404
from django.urls import reverse, reverse_lazy
from django.views.generic import ListView, DetailView, DeleteView, UpdateView, FormView, CreateView
from django.views.generic.detail import SingleObjectMixin

from oscar.apps.customer.utils import normalise_email
from oscar.views.generic import BulkEditMixin
from oscar.core.compat import get_user_model
from oscar.core.loading import get_model, get_class, get_classes

from project.accounts.models import Branch
from project.apps.shop_settings.models import ShopSettings

UserSearchForm, ProductAlertSearchForm, ProductAlertUpdateForm = get_classes(
    'dashboard.users.forms', ('UserSearchForm', 'ProductAlertSearchForm', 'ProductAlertUpdateForm')
)
PasswordResetForm = get_class('customer.forms', 'PasswordResetForm')
ProductAlert = get_model('customer', 'ProductAlert')
User = get_user_model()

from .forms import UserCreationForm, BranchForm


class IndexView(BulkEditMixin, ListView):
    template_name = 'oscar/dashboard/users/index.html'
    paginate_by = 25
    model = User
    actions = (
        'make_active',
        'make_inactive',
    )
    current_view = 'dashboard:users-index'
    form_class = UserSearchForm
    desc_template = _('%(main_filter)s %(email_filter)s %(name_filter)s')
    description = ''
    context_object_name = 'user_list'

    def dispatch(self, request, *args, **kwargs):
        if request.user.account.owner:
            messages.error(self.request, _("Permission denied. Only main vendor account can manage users (managers)"))
            return HttpResponseRedirect(reverse('dashboard:index'))
        return super(IndexView, self).dispatch(request, *args, **kwargs)

    def get_queryset(self):
        owner = self.request.user.account
        queryset = self.model.objects.filter(account__owner=owner).order_by('-date_joined')
        self.desc_ctx = {
            'main_filter': _('All managers'),
            'email_filter': '',
            'name_filter': '',
        }

        if 'email' not in self.request.GET:
            self.form = self.form_class()
            return queryset

        self.form = self.form_class(self.request.GET)

        if not self.form.is_valid():
            return queryset

        data = self.form.cleaned_data

        if data['email']:
            email = normalise_email(data['email'])
            queryset = queryset.filter(email__startswith=email)
            self.desc_ctx['email_filter'] = _(" with email matching '%s'") % email
        if data['name']:
            # If the value is two words, then assume they are first name and
            # last name
            parts = data['name'].split()
            if len(parts) == 2:
                condition = Q(first_name__istartswith=parts[0]) | Q(last_name__istartswith=parts[1])
            else:
                condition = Q(first_name__istartswith=data['name']) | Q(last_name__istartswith=data['name'])
            queryset = queryset.filter(condition).distinct()
            self.desc_ctx['name_filter'] = _(" with name matching '%s'") % data['name']

        return queryset

    def get_context_data(self, **kwargs):
        context = super(IndexView, self).get_context_data(**kwargs)
        context['form'] = self.form
        context['queryset_description'] = self.desc_template % self.desc_ctx
        return context

    def make_inactive(self, request, users):
        return self._change_users_active_status(users, False)

    def make_active(self, request, users):
        return self._change_users_active_status(users, True)

    def _change_users_active_status(self, users, value):
        for user in users:
            if not user.is_superuser:
                user.is_active = value
                user.save()
        messages.info(self.request, _("Users' status successfully changed"))
        return HttpResponseRedirect(reverse(self.current_view))


class UserCreateView(CreateView):
    template_name = 'oscar/dashboard/users/form.html'
    model = User
    form_class = UserCreationForm

    def dispatch(self, request, *args, **kwargs):
        if request.user.account.owner:
            messages.error(self.request, _("Permission denied. Only main vendor account can manage users (managers)"))
            return HttpResponseRedirect(reverse('dashboard:index'))
        return super(UserCreateView, self).dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        user = form.save()
        user.is_staff = True
        user.save()
        user.account.owner = self.request.user.account
        user.account.save()
        return HttpResponseRedirect(self.get_success_url())

    def get_success_url(self):
        messages.success(self.request, _("New manager created"))
        return reverse('dashboard:users-index')


class UserDetailView(DetailView):
    template_name = 'oscar/dashboard/users/detail.html'
    model = User
    context_object_name = 'manager'

    def dispatch(self, request, *args, **kwargs):
        obj = self.get_object()
        if obj.account.owner != request.user.account:
            raise Http404
        return super(UserDetailView, self).dispatch(request, *args, **kwargs)


class BranchMixin(object):
    model = Branch

    def dispatch(self, request, *args, **kwargs):
        if request.user.account.owner:
            messages.error(self.request, _("Permission denied. Only main vendor account can manage branches"))
            return HttpResponseRedirect(reverse('dashboard:index'))
        return super(BranchMixin, self).dispatch(request, *args, **kwargs)


class BranchListView(BranchMixin, ListView):
    context_object_name = 'branch_list'
    template_name = 'oscar/dashboard/users/branch_list.html'

    def get_queryset(self):
        owner = self.request.user.account
        queryset = self.model.objects.filter(owner=owner)
        return queryset


class BranchCreateView(BranchMixin, CreateView):
    form_class = BranchForm
    template_name = 'oscar/dashboard/users/branch_form.html'
    success_url = reverse_lazy('dashboard:branch-list')

    def form_valid(self, form):
        branch = form.save(commit=False)
        branch.owner = self.request.user.account
        branch.save()
        return super(BranchCreateView, self).form_valid(form)


class BranchUpdateView(BranchMixin, UpdateView):
    form_class = BranchForm
    template_name = 'oscar/dashboard/users/branch_form.html'
    success_url = reverse_lazy('dashboard:branch-list')


class BranchDeleteView(BranchMixin, DeleteView):
    template_name = 'oscar/dashboard/users/branch_delete.html'
    success_url = reverse_lazy('dashboard:branch-list')
