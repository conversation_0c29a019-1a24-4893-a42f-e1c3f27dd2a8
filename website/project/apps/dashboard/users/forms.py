from django import forms
from django.contrib.auth.models import User

from project.accounts.models import Branch
from project.apps.customer.forms import CustomUserCreationForm


class UserCreationForm(CustomUserCreationForm):
    class Meta:
        model = User
        fields = ('first_name', 'email')

    def __init__(self, *args, **kwargs):
        super(UserCreationForm, self).__init__(*args, **kwargs)
        del self.fields['agree_with_terms']


class BranchForm(forms.ModelForm):
    class Meta:
        model = Branch
        fields = (
            'name',
            'company_name',
            'company_code',
            'company_vat_code',
            'contact_email',
            'contact_address',
            'contact_phone',
            'contact_fax',
            'contact_skype',
            'contact_info',
            'bank_info',
            'shop_name',
            'shop_tagline',
            'shop_description',
            'shop_keywords',
        )
