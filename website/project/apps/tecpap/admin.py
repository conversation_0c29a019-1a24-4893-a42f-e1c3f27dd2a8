from django.contrib import admin
from django import forms
from django.conf import settings
from django.utils.translation import gettext_lazy as _
import os

from project.apps.tecpap.models import (
    Color,
    Fuel,
    Transmission,
    Body,
    Manufacturer,
    Model,
    Type,
    Engine,
    TypeEngine,
)


class ColorAdmin(admin.ModelAdmin):
    search_fields = ["name"]


class FuelAdmin(admin.ModelAdmin):
    search_fields = ["name"]


class TransmissionAdmin(admin.ModelAdmin):
    search_fields = ["name"]


class BodyAdmin(admin.ModelAdmin):
    search_fields = ["name"]


class ManufacturerAdminForm(forms.ModelForm):
    logo = forms.ImageField(
        label=_("Logo"),
        required=False,
        help_text=_("Upload manufacturer logo. It will be saved as logo_[slug].png and logo_[slug].webp in the media/manufacturers directory. Logo will be resized to fit within 120x120px while maintaining aspect ratio.")
    )
    remove_background = forms.BooleanField(
        label=_("Remove background"),
        required=False,
        initial=True,
        help_text=_("Attempt to remove the background from the logo (works best with logos on white/solid backgrounds).")
    )

    class Meta:
        model = Manufacturer
        fields = ['brand', 'slug', 'active', 'logo', 'remove_background']

    def save(self, commit=True):
        from PIL import Image

        manufacturer = super().save(commit=False)

        logo = self.cleaned_data.get('logo')
        if logo:
            # Ensure slug is set
            if not manufacturer.slug:
                from oscar.core.utils import slugify
                manufacturer.slug = slugify(manufacturer.brand)

            # Define the path where the logo will be saved
            logo_filename = f"logo_{manufacturer.slug.lower()}.png"
            media_manufacturers_dir = os.path.join(settings.MEDIA_ROOT, "manufacturers")

            # Create directory if it doesn't exist
            if not os.path.exists(media_manufacturers_dir):
                os.makedirs(media_manufacturers_dir)

            # Save the uploaded file temporarily
            logo_temp_path = os.path.join(media_manufacturers_dir, f"temp_{logo_filename}")
            with open(logo_temp_path, 'wb+') as destination:
                for chunk in logo.chunks():
                    destination.write(chunk)

            # Process the image with Pillow
            img = Image.open(logo_temp_path)

            # Convert to RGBA if it's not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')

            # Remove background if requested
            if self.cleaned_data.get('remove_background'):
                # Process the image to make white/light backgrounds transparent
                data = img.getdata()
                new_data = []

                for item in data:
                    # If pixel is light (close to white), make it transparent
                    if item[0] > 220 and item[1] > 220 and item[2] > 220:
                        new_data.append((255, 255, 255, 0))
                    else:
                        new_data.append(item)

                img.putdata(new_data)

            # Resize the image to fit within 120x120px box while maintaining aspect ratio
            max_size = 120

            # Determine which dimension (width or height) is larger relative to the target size
            width_ratio = img.width / max_size
            height_ratio = img.height / max_size

            # Use the larger ratio to ensure the image fits within the box
            if width_ratio > height_ratio:
                # Image is wider than tall relative to target
                new_width = max_size
                new_height = int(img.height / width_ratio)
            else:
                # Image is taller than wide relative to target
                new_height = max_size
                new_width = int(img.width / height_ratio)

            img = img.resize((new_width, new_height), Image.LANCZOS)

            # Save the processed image to media directory
            logo_path = os.path.join(media_manufacturers_dir, logo_filename)
            img.save(logo_path, 'PNG')

            # Also save WebP version for better performance
            webp_filename = f"logo_{manufacturer.slug.lower()}.webp"
            webp_path = os.path.join(media_manufacturers_dir, webp_filename)
            img.save(webp_path, 'WEBP', quality=85)

            # Remove temporary file
            if os.path.exists(logo_temp_path):
                os.remove(logo_temp_path)

        if commit:
            manufacturer.save()

        return manufacturer


class ManufacturerAdmin(admin.ModelAdmin):
    form = ManufacturerAdminForm
    search_fields = ["brand"]
    list_display = ["brand", "slug", "active", "has_logo"]

    def has_logo(self, obj):
        """Check if the manufacturer has a logo file (PNG or WebP)."""
        png_path = os.path.join(settings.MEDIA_ROOT, "manufacturers", f"logo_{obj.slug.lower()}.png")
        webp_path = os.path.join(settings.MEDIA_ROOT, "manufacturers", f"logo_{obj.slug.lower()}.webp")
        return os.path.exists(png_path) or os.path.exists(webp_path)

    has_logo.boolean = True
    has_logo.short_description = _("Has Logo")


class ModelAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["manufacturer"]
    autocomplete_fields = ["manufacturer", "rrr_model", "recar_model"]


class TypeAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["model__manufacturer"]
    autocomplete_fields = ["recar_modification"]


class EngineAdmin(admin.ModelAdmin):
    search_fields = ["code"]


class TypeEngineAdmin(admin.ModelAdmin):
    search_fields = ["typ__name", "eng__code"]
    list_display = ["typ", "eng"]
    autocomplete_fields = ["typ", "eng"]


admin.site.register(Color, ColorAdmin)
admin.site.register(Fuel, FuelAdmin)
admin.site.register(Transmission, TransmissionAdmin)
admin.site.register(Body, BodyAdmin)
admin.site.register(Manufacturer, ManufacturerAdmin)
admin.site.register(Model, ModelAdmin)
admin.site.register(Type, TypeAdmin)
admin.site.register(Engine, EngineAdmin)
admin.site.register(TypeEngine, TypeEngineAdmin)
