# -*- coding: utf-8 -*-


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('address', '0002_auto_20220713_1735'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='useraddress',
            options={'ordering': ['-num_orders_as_shipping_address'], 'verbose_name': 'User address', 'verbose_name_plural': 'User addresses'},
        ),
        migrations.RemoveField(
            model_name='useraddress',
            name='num_orders',
        ),
        migrations.AddField(
            model_name='useraddress',
            name='num_orders_as_billing_address',
            field=models.PositiveIntegerField(default=0, verbose_name='Number of Orders as Billing Address'),
        ),
        migrations.AddField(
            model_name='useraddress',
            name='num_orders_as_shipping_address',
            field=models.PositiveIntegerField(default=0, verbose_name='Number of Orders as Shipping Address'),
        ),
    ]
