# -*- coding: utf-8 -*-


from django.db import models, migrations
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        ('address', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('code', models.CharField(max_length=100, verbose_name='State code')),
                ('name', models.CharField(max_length=255, verbose_name='State name')),
                ('country', models.ForeignKey(to='address.Country', on_delete=models.CASCADE)),
            ],
            options={
                'verbose_name': 'State',
                'verbose_name_plural': 'States',
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='country',
            name='eu',
            field=models.BooleanField(default=False, verbose_name='EU country'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='country',
            name='vat_size',
            field=models.DecimalField(
                default=Decimal('0.00'), verbose_name='VAT size', max_digits=10, decimal_places=2
            ),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='useraddress',
            name='company_code',
            field=models.CharField(default=b'', max_length=100, verbose_name='Company code', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='useraddress',
            name='company_name',
            field=models.CharField(default=b'', max_length=100, verbose_name='Company name', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='useraddress',
            name='vat_number',
            field=models.CharField(default=b'', max_length=100, verbose_name='VAT number', blank=True),
            preserve_default=True,
        ),
    ]
