from decimal import Decimal
from operator import mod

from django.db import models
from django.utils.translation import gettext_lazy as _

from oscar.apps.address import abstract_models


class UserAddress(abstract_models.AbstractUserAddress):
    company_name = models.CharField(_('Company name'), max_length=100, blank=True, default='')
    company_code = models.CharField(_('Company code'), max_length=100, blank=True, default='')
    vat_number = models.CharField(_('VAT number'), max_length=100, blank=True, default='')

    def active_address_fields(self):
        fields = []
        if self.company_name:
            fields.append(self.company_name)
        if self.company_code:
            fields.append(self.company_code)
        if self.vat_number:
            fields.append(self.vat_number)
        fields.extend(super().active_address_fields())
        return fields


class Country(abstract_models.AbstractCountry):
    eu = models.BooleanField(_('EU country'), default=False)
    vat_size = models.DecimalField(_('VAT size'), max_digits=10, decimal_places=2, default=Decimal('0.00'))


class State(models.Model):
    code = models.CharField(_('State code'), max_length=100)
    name = models.CharField(_('State name'), max_length=255)
    country = models.ForeignKey('address.Country', on_delete=models.CASCADE)

    class Meta:
        verbose_name = _('State')
        verbose_name_plural = _('States')

    def __str__(self):
        return '%s (%s)' % (self.code, self.name)


from oscar.apps.address.models import *
