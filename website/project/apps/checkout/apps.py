from django.urls import path

from oscar.apps.checkout import apps
from oscar.core.loading import get_class


class CheckoutConfig(apps.CheckoutConfig):
    name = 'project.apps.checkout'

    def ready(self):
        super().ready()
        self.shipping_address_view = get_class('checkout.views', 'ShippingAddressView')
        self.user_address_update_view = get_class('checkout.views', 'UserAddressUpdateView')
        # self.shipping_method_view = get_class('checkout.views', 'ShippingMethodView')
        self.address_change_status_view = get_class('checkout.views', 'AddressChangeStatusView')
        self.payment_details_view = get_class('checkout.views', 'PaymentDetailsView')

    def get_urls(self):
        urls = [
            path('', self.index_view.as_view(), name='index'),
            # Shipping/user address views
            path('shipping-address/', self.shipping_address_view.as_view(), name='shipping-address'),
            path('user-address/edit/<int:pk>/', self.user_address_update_view.as_view(), name='user-address-update'),
            path(
                'user-address/delete/<int:pk>/',
                self.user_address_delete_view.as_view(),
                name='user-address-delete',
            ),
            path(
                'user-address/<int:pk>/action/<str:action>/',
                self.address_change_status_view.as_view(),
                name='address-change-status'
            ),
            # Shipping method views
            path('shipping-method/', self.shipping_method_view.as_view(), name='shipping-method'),
            # Payment method views
            path('payment-method/', self.payment_method_view.as_view(), name='payment-method'),
            path('payment-details/', self.payment_details_view.as_view(), name='payment-details'),
            # Preview and thankyou
            path('preview/', self.payment_details_view.as_view(preview=True), name='preview'),
            path('thank-you/', self.thankyou_view.as_view(), name='thank-you'),
        ]
        return self.post_process_urls(urls)
