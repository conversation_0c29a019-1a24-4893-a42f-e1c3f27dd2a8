import logging

from oscar.core.loading import get_model
from oscar.apps.checkout.mixins import OrderPlacementMixin

UserAddress = get_model('address', 'UserAddress')
ShippingAddress = get_model('order', 'ShippingAddress')
BillingAddress = get_model('order', 'BillingAddress')

# Configure logger for checkout - this will now use checkout.log file
logger = logging.getLogger('project.apps.checkout')

class CustomOrderPlacementMixin(OrderPlacementMixin):
    """
    Custom mixin which extends <PERSON>'s OrderPlacementMixin.
    This ensures that our custom methods properly override <PERSON>'s methods.
    """
    def create_shipping_address(self, user, shipping_address, **kwargs):
        """
        Create and return the shipping address for the current order.

        Compared to the core Oscar implementation, this version:
        1. <PERSON><PERSON>ly handles existing addresses
        2. Prevents duplicate address creation
        3. Has better error handling and logging
        
        Args:
            user: The user creating the order
            shipping_address: The shipping address instance
            **kwargs: Additional arguments that might be passed by <PERSON>
        """
        # Adjust shipping_address so it has the same fields as <PERSON>r<PERSON><PERSON><PERSON>
        # before we check if we already have an address in the db
        addr_attrs = {
            'first_name': shipping_address.first_name,
            'last_name': shipping_address.last_name,
            'line1': shipping_address.line1,
            'line2': shipping_address.line2,
            'line3': shipping_address.line3,
            'line4': shipping_address.line4,
            'state': shipping_address.state,
            'postcode': shipping_address.postcode,
            'country': shipping_address.country,
            'phone_number': shipping_address.phone_number,
            'notes': shipping_address.notes if hasattr(shipping_address, 'notes') else '',
            'company_name': shipping_address.company_name if hasattr(shipping_address, 'company_name') else '',
            'company_code': shipping_address.company_code if hasattr(shipping_address, 'company_code') else '',
            'vat_number': shipping_address.vat_number if hasattr(shipping_address, 'vat_number') else '',
        }

        if user and user.is_authenticated:
            try:
                # Get address hash before saving to check for duplicates
                user_address = UserAddress(**addr_attrs)
                address_hash = user_address.generate_hash()
                logger.debug(f"Generated address hash: {address_hash}")

                # Look for existing address with same hash
                existing_address = UserAddress.objects.filter(
                    user=user,
                    hash=address_hash
                ).first()

                if existing_address:
                    logger.debug(f"Found existing address with hash {address_hash}")
                    # Convert UserAddress to ShippingAddress
                    shipping_address = ShippingAddress(**addr_attrs)
                    shipping_address.save()
                    return shipping_address

                # Create new address if no match found
                logger.debug(f"Creating new address with hash {address_hash}")
                user_address.user = user
                user_address.save()
                
                # Set as default shipping address only if it's the first address
                if not user.addresses.exists():
                    user_address.is_default_for_shipping = True
                    user_address.save()

                # Convert UserAddress to ShippingAddress
                shipping_address = ShippingAddress(**addr_attrs)
                shipping_address.save()
                return shipping_address

            except Exception as e:
                logger.error(f"Error saving shipping address: {str(e)}")
                # If anything goes wrong, just save and return the original shipping address
                shipping_address.save()
                return shipping_address
        else:
            logger.debug("User not authenticated - saving address without user association")
            shipping_address.save()
            return shipping_address

    def create_billing_address(self, user, billing_address, shipping_address=None, **kwargs):
        """
        Create and return the billing address for the current order.
        
        This method follows the same pattern as create_shipping_address but for billing addresses.
        If the billing address is the same as shipping, we use that instead of creating a new one.
        
        Args:
            user: The user creating the order
            billing_address: The billing address instance
            shipping_address: Optional shipping address to use if billing is same as shipping
            **kwargs: Additional arguments (like guest_email) that might be passed by Oscar
        """
        # If billing address is same as shipping address, just return shipping address
        if not billing_address and shipping_address:
            logger.debug("Using shipping address as billing address")
            return shipping_address

        # Adjust billing_address so it has the same fields as UserAddress
        addr_attrs = {
            'first_name': billing_address.first_name,
            'last_name': billing_address.last_name,
            'line1': billing_address.line1,
            'line2': billing_address.line2,
            'line3': billing_address.line3,
            'line4': billing_address.line4,
            'state': billing_address.state,
            'postcode': billing_address.postcode,
            'country': billing_address.country,
            'phone_number': billing_address.phone_number,
            'notes': billing_address.notes if hasattr(billing_address, 'notes') else '',
            'company_name': billing_address.company_name if hasattr(billing_address, 'company_name') else '',
            'company_code': billing_address.company_code if hasattr(billing_address, 'company_code') else '',
            'vat_number': billing_address.vat_number if hasattr(billing_address, 'vat_number') else '',
        }

        if user and user.is_authenticated:
            try:
                # Get address hash before saving to check for duplicates
                user_address = UserAddress(**addr_attrs)
                address_hash = user_address.generate_hash()
                logger.debug(f"Generated billing address hash: {address_hash}")

                # Look for existing address with same hash
                existing_address = UserAddress.objects.filter(
                    user=user,
                    hash=address_hash
                ).first()

                if existing_address:
                    logger.debug(f"Found existing billing address with hash {address_hash}")
                    # Convert UserAddress to BillingAddress
                    billing_address = BillingAddress(**addr_attrs)
                    billing_address.save()
                    return billing_address

                # Create new address if no match found
                logger.debug(f"Creating new billing address with hash {address_hash}")
                user_address.user = user
                user_address.save()
                
                # Set as default billing address only if it's the first address
                if not user.addresses.exists():
                    user_address.is_default_for_billing = True
                    user_address.save()

                # Convert UserAddress to BillingAddress
                billing_address = BillingAddress(**addr_attrs)
                billing_address.save()
                return billing_address

            except Exception as e:
                logger.error(f"Error saving billing address: {str(e)}")
                # If anything goes wrong, just save and return the original billing address
                billing_address.save()
                return billing_address
        else:
            logger.debug("User not authenticated - saving billing address without user association")
            billing_address.save()
            return billing_address 

    def update_address_book(self, user, shipping_address):
        """
        Update the user's address book with the shipping address.
        """
        logger.info(f"CUSTOM update_address_book called for order with user {user.email if user and user.is_authenticated else 'anonymous'}")
        
        if not user.is_authenticated:
            logger.info("User not authenticated, skipping address book update")
            return

        # Generate hash for the new address
        address_hash = shipping_address.generate_hash()
        logger.info(f"Generated hash for update_address_book: {address_hash}")
        
        # Check if address with same hash already exists
        existing_address = UserAddress.objects.filter(
            user=user,
            hash=address_hash
        ).first()
        
        if existing_address:
            logger.info(f"Found existing address with hash {address_hash} in update_address_book, updating it")
            # If address exists, just update the existing one
            for field_name, value in shipping_address.__dict__.items():
                if not field_name.startswith('_'):
                    setattr(existing_address, field_name, value)
            existing_address.save()
            return

        # If no existing address found, create new one
        logger.info(f"No existing address found with hash {address_hash} in update_address_book, creating new one")
        user_addr = UserAddress.objects.create(
            user=user,
            **dict(
                (k, v)
                for (k, v) in shipping_address.__dict__.items()
                if not k.startswith('_')
            )
        )

        # Set as default shipping address if this is the first address
        if not user.addresses.exists():
            logger.info("Setting as default shipping and billing address")
            user_addr.is_default_for_shipping = True
            user_addr.is_default_for_billing = True
            user_addr.save()