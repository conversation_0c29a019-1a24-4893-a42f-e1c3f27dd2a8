import logging
from decimal import Decimal as D
from datetime import datetime

from django import http
from django.conf import settings
from django.contrib import messages
from django.contrib.auth import authenticate, login as auth_login
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from django.urls import reverse
from django.http import HttpResponseRedirect, HttpResponse
from django.middleware.csrf import get_token
from django.template import loader
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.utils.translation import gettext as _, get_language
from django.views.generic import FormView, TemplateView

from ipware import get_client_ip
from oscar.apps.checkout import views
from oscar.apps.customer.forms import generate_username
from oscar.apps.customer.mixins import RegisterUserMixin
from oscar.apps.payment import models
from oscar.apps.shipping.methods import NoShippingRequired
from oscar.core.loading import get_class, get_classes, get_model

from project.apps.shop_settings.models import ShopSettings

from project.everypay.helpers import EveryPayHelper

from .forms import RegistrationForm, PaymentForm, ShippingAddressForm, UserAddressForm
from webtopay.forms import WebToPaymentForm

pre_payment, post_payment = get_classes("checkout.signals", ["pre_payment", "post_payment"])
RedirectRequired, UnableToTakePayment, PaymentError = get_classes(
    "payment.exceptions", ["RedirectRequired", "UnableToTakePayment", "PaymentError"]
)
OrderPlacementMixin = get_class("checkout.mixins", "OrderPlacementMixin")
UnableToPlaceOrder = get_class("order.exceptions", "UnableToPlaceOrder")
CheckoutSessionMixin = get_class("checkout.session", "CheckoutSessionMixin")
CheckoutSessionData = get_class("checkout.utils", "CheckoutSessionData")

ShippingAddress = get_model("order", "ShippingAddress")
UserAddress = get_model("address", "UserAddress")
Country = get_model("address", "Country")
Selector = get_class("partner.strategy", "Selector")

# Standard logger for checkout events
logger = logging.getLogger("oscar.checkout")


class PaymentFormMixin(object):
    """Payment form mixin"""

    def dispatch(self, request, *args, **kwargs):
        self.payment_form = self.get_payment_form()
        return super(PaymentFormMixin, self).dispatch(request, *args, **kwargs)

    def get_payment_form(self):
        if self.request.method == "POST":
            data = self.request.POST
        else:
            data = None

        basket = self.request.basket
        shipping_address = self.get_shipping_address(basket)
        shipping_method = self.get_shipping_method(basket, shipping_address)
        if data:
            payment_form = PaymentForm(
                data,
                request=self.request,
                basket=basket,
                shipping_method=shipping_method,
                shipping_address=shipping_address,
            )
        else:
            payment_form = PaymentForm(
                # initial={'payment_method': payment_method if payment_method else 'paypal'},
                basket=basket,
                request=self.request,
                shipping_method=shipping_method,
                shipping_address=shipping_address,
            )
        return payment_form

    # def get_context_data(self, **kwargs):
    #     ctx = super(PaymentFormMixin, self).get_context_data(**kwargs)
    #     ctx["payment_form"] = kwargs.get("payment_form", self.get_payment_form())
    #     return ctx


class ShippingAddressView(CheckoutSessionMixin, PaymentFormMixin, RegisterUserMixin, FormView):
    """Shipping address view"""

    template_name = "oscar/checkout/shipping_address.html"
    form_class = ShippingAddressForm

    def dispatch(self, request, *args, **kwargs):
        self.checkout_session = CheckoutSessionData(request)

        if request.method == "POST":
            self.registration_form = RegistrationForm(request.POST)
        else:
            self.registration_form = RegistrationForm(
                initial={
                    "email": self.checkout_session.get_guest_email(),
                    "password": "",
                }
            )
        return super(ShippingAddressView, self).dispatch(request, *args, **kwargs)

    def register_account(self, form):
        success = True
        if form.is_valid():
            email = form.cleaned_data["email"]
            password = form.cleaned_data["password"]
            if email and password:
                user = User.objects.create(username=generate_username(), email=email)
                user.set_password(password)
                user.save()

                if getattr(settings, "OSCAR_SEND_REGISTRATION_EMAIL", True):
                    self.send_registration_email(user)

                user = authenticate(username=email, password=password)

                old_token = get_token(self.request)

                auth_login(self.request, user)

                # restore an old token
                self.request.META.update(
                    {
                        "CSRF_COOKIE_USED": True,
                        "CSRF_COOKIE": old_token,
                    }
                )

                messages.success(self.request, _("Your account successfully created"))
        else:
            success = False
        return success

    def get_form_kwargs(self):
        kwargs = super(ShippingAddressView, self).get_form_kwargs()
        if "initial" in kwargs and kwargs["initial"]:
            try:
                kwargs["initial"]["country"] = Country.objects.get(iso_3166_1_a2=kwargs["initial"]["country_id"])
                del kwargs["initial"]["country_id"]
            except (KeyError, Country.DoesNotExist):
                pass
        kwargs.update({"request": self.request})
        return kwargs

    def get(self, request, *args, **kwargs):
        # Check that the user's basket is not empty
        if request.basket.is_empty:
            messages.error(request, _("You need to add some items to your" " basket to checkout"))
            return HttpResponseRedirect(reverse("basket:summary"))
        return super(ShippingAddressView, self).get(request, *args, **kwargs)

    def get_initial(self):
        # merge shipping address details with billing address details
        new_shipping_address_fields = self.checkout_session.new_shipping_address_fields()
        new_billing_address_fields = self.checkout_session.new_billing_address_fields()

        if new_shipping_address_fields:
            new_shipping_address_fields = new_shipping_address_fields.copy()
            try:
                new_shipping_address_fields["country"] = Country.objects.get(
                    iso_3166_1_a2=new_shipping_address_fields.pop("country_id")
                )
            except Country.DoesNotExist:
                pass

        if new_billing_address_fields:
            new_billing_address_fields = new_billing_address_fields.copy()
            try:
                new_billing_address_fields["country"] = Country.objects.get(
                    iso_3166_1_a2=new_billing_address_fields.pop("country_id")
                )
            except Country.DoesNotExist:
                pass

            for k in list(new_billing_address_fields.keys()):
                new_shipping_address_fields["billing_{}".format(k)] = new_billing_address_fields[k]

        return new_shipping_address_fields

    def build_submission(self, **kwargs):
        """
        Return a dict of data to submitted to pay for, and create an order
        """
        basket = kwargs.get("basket", self.request.basket)
        shipping_address = self.get_shipping_address(basket)
        shipping_method = self.get_shipping_method(basket, shipping_address)
        billing_address = self.get_billing_address(shipping_address)

        if not shipping_method:
            total = shipping_charge = None
        else:
            shipping_charge = shipping_method.calculate(basket)
            total = self.get_order_totals(basket, shipping_charge=shipping_charge)

        payment_method = self.checkout_session.payment_method()
        if payment_method:
            if payment_method.find("__") > -1:
                pm_parts = payment_method.split("__")
                payment_method = "{} ({})".format(str(self.payment_form.PAYMENT_METHOD_MAP[pm_parts[0]]), pm_parts[1])
            else:
                payment_method = self.payment_form.PAYMENT_METHOD_MAP.get(payment_method)

        submission = {
            "user": self.request.user,
            "basket": basket,
            "shipping_address": shipping_address,
            "shipping_method": shipping_method,
            "shipping_charge": shipping_charge,
            "billing_address": billing_address,
            "order_total": total,
            "order_kwargs": {"language": get_language()[:2]},
            "payment_kwargs": {
                "payment_form": self.payment_form,
                "payment_method": payment_method,
            },
        }

        # If there is a billing address, add it to the payment kwargs as calls
        # to payment gateways generally require the billing address. Note, that
        # it normally makes sense to pass the form instance that captures the
        # billing address information. That way, if payment fails, you can
        # render bound forms in the template to make re-submission easier.
        if billing_address:
            submission["payment_kwargs"]["billing_address"] = billing_address

        # Allow overrides to be passed in
        submission.update(kwargs)

        # Set guest email after overrides as we need to update the order_kwargs
        # entry.
        if not submission["user"].is_authenticated and "guest_email" not in submission["order_kwargs"]:
            email = self.checkout_session.get_guest_email()
            submission["order_kwargs"]["guest_email"] = email
        return submission

    def get_context_data(self, **kwargs):
        context = super(ShippingAddressView, self).get_context_data(**kwargs)
        context["payment_form"] = self.payment_form
        if self.request.user.is_authenticated:
            context["addresses"] = self.get_available_addresses()
        else:
            context["registration_form"] = self.registration_form
        return context

    def get_available_addresses(self):
        return self.request.user.addresses.filter(country__is_shipping_country=True).order_by(
            "-is_default_for_shipping"
        )

    def split_shipping_and_billing_address_details(self, address_details):
        shipping_address_details = {}
        billing_address_details = {}
        for k in list(address_details.keys()):
            if k != "is_billing_address_same_as_shipping":
                if k.startswith("billing_"):
                    billing_address_details[k.replace("billing_", "")] = address_details[k]
                else:
                    shipping_address_details[k] = address_details[k]

        # replace a country instance with the country code to be able to store address information
        shipping_address_details["country_id"] = shipping_address_details["country"].code
        del shipping_address_details["country"]

        billing_address_details["country_id"] = billing_address_details["country"].code
        del billing_address_details["country"]

        return shipping_address_details, billing_address_details

    def form_valid(self, form):
        ctx = self.get_context_data()
        ctx["request"] = self.request
        ctx.update(self.build_submission())
        ctx.update(ctx["order_kwargs"])
        ctx.update(ctx["payment_kwargs"])

        template = loader.get_template("oscar/checkout/preview.html")

        return HttpResponse(template.render(ctx, self.request))

    def post(self, request, *args, **kwargs):
        # Check if a shipping address was selected directly (eg no form was filled in)
        if self.request.user.is_authenticated and "address_id" in self.request.POST:
            address = UserAddress._default_manager.get(pk=self.request.POST["address_id"], user=self.request.user)
            action = self.request.POST.get("action", None)
            if action == "ship_to":
                # User has selected a previous address to ship to
                # self.checkout_session.ship_to_user_address(address)
                # return HttpResponseRedirect(self.get_success_url())
                shipping_addr = ShippingAddress()
                address.populate_alternative_model(shipping_addr)
                form_class = self.get_form_class()
                form = form_class(instance=shipping_addr, initial={}, request=request)
                return self.render_to_response(self.get_context_data(form=form))
            elif action == "delete":
                # Delete the selected address
                address.delete()
                messages.info(self.request, _("Address deleted from your address book"))
                return HttpResponseRedirect(reverse("checkout:shipping-address"))
            else:
                return HttpResponseRedirect(reverse("checkout:shipping-address"))
        else:
            form_class = self.get_form_class()
            form = self.get_form(form_class)

            if form.is_valid():
                return self.form_valid(form)
            else:
                return self.form_invalid(form)

    def get_success_url(self):
        return reverse("checkout:shipping-method")


class AJAXShippingAddressView(ShippingAddressView):
    template_name = "oscar/checkout/ajax/shipping_address.html"

    def form_valid(self, form):
        address_details = form.cleaned_data

        # Split shipping and billing address details before storing into session
        (shipping_address_details, billing_address_details) = self.split_shipping_and_billing_address_details(
            address_details
        )
        self.checkout_session.ship_to_new_address(shipping_address_details)

        if address_details["is_billing_address_same_as_shipping"]:
            self.checkout_session.bill_to_shipping_address()
        else:
            self.checkout_session.bill_to_new_address(billing_address_details)
            self.checkout_session._unset("billing", "billing_address_same_as_shipping")

        return self.form_invalid(form)

    def post(self, request, *args, **kwargs):
        if request.user.is_authenticated and "address_id" in request.POST:
            address = UserAddress._default_manager.get(pk=request.POST["address_id"], user=request.user)
            action = request.POST.get("action", None)
            if action == "ship_to":
                shipping_addr = ShippingAddress()
                address.populate_alternative_model(shipping_addr)
                form_class = self.get_form_class()
                form = form_class(instance=shipping_addr, initial={}, request=request)
                return self.render_to_response(self.get_context_data(form=form))
        else:
            form_class = self.get_form_class()
            form = self.get_form(form_class)

            if form.is_valid():
                return self.form_valid(form)
            else:
                return self.form_invalid(form)


class AJAXRegisterAccountView(ShippingAddressView):
    template_name = "oscar/checkout/ajax/account_registration.html"
    create_account = False

    def get_context_data(self, **kwargs):
        context = super(AJAXRegisterAccountView, self).get_context_data(**kwargs)
        context["create_account"] = self.create_account
        return context

    def post(self, request, *args, **kwargs):
        form_class = self.get_form_class()
        form = self.get_form(form_class)
        create_account = request.POST.get("create_account", "")
        email = request.POST.get("email", "")

        if create_account:
            self.create_account = True
            success = self.register_account(self.registration_form)
            if success:
                return HttpResponse("ok")
            else:
                return self.form_invalid(form)
        elif email:
            self.checkout_session.set_guest_email(email)
            return HttpResponse("ok")
        else:
            return self.form_invalid(form)


class AJAXOrderTotalView(CheckoutSessionMixin, TemplateView):
    template_name = "oscar/checkout/ajax/order_total.html"


class AJAXShippingTotalView(CheckoutSessionMixin, TemplateView):
    template_name = "oscar/checkout/ajax/shipping_total.html"


class ShippingMethodView(views.ShippingMethodView):
    """Shipping method view"""

    template_name = "oscar/checkout/shipping_methods.html"

    pre_conditions = []

    def get(self, request, *args, **kwargs):
        # These pre-conditions can't easily be factored out into the normal
        # pre-conditions as they do more than run a test and then raise an
        # exception on failure.

        # Check that shipping is required at all
        if not request.basket.is_shipping_required():
            self.checkout_session.use_shipping_method(NoShippingRequired().code)
            return self.get_success_response()

        # Check that shipping address has been completed
        if not self.checkout_session.is_shipping_address_set():
            messages.error(request, _("Please choose a shipping address"))
            return HttpResponseRedirect(reverse("checkout:shipping-address"))

        # Save shipping methods as instance var as we need them both here
        # and when setting the context vars.
        self._methods = self.get_available_shipping_methods()
        if len(self._methods) == 0:
            # No shipping methods available for given address
            vendor_url = reverse("contact-vendor", args=(request.basket.lines.all()[0].product_id,))
            messages.warning(
                request,
                mark_safe(
                    _(
                        "Shipping to your country is not available yet, <a href='%(vendor_url)s' target='_blank'>contact vendor</a>"
                    )
                    % {"vendor_url": vendor_url}
                ),
            )
            return HttpResponseRedirect(reverse("checkout:shipping-address"))
        elif len(self._methods) == 1:
            # Only one shipping method - set this and redirect onto the next
            # step
            if self._methods[0].code == "not-available":
                vendor_url = reverse("contact-vendor", args=(request.basket.lines.all()[0].product_id,))
                messages.warning(
                    request,
                    mark_safe(
                        _(
                            "Shipping to your country is not available yet, <a href='%(vendor_url)s' target='_blank'>contact vendor</a>"
                        )
                        % {"vendor_url": vendor_url}
                    ),
                )
                return HttpResponseRedirect(reverse("checkout:shipping-address"))
            self.checkout_session.use_shipping_method(self._methods[0].code)
            return self.get_success_response()

        # Must be more than one available shipping method, we present them to
        # the user to make a choice.
        return super(ShippingMethodView, self).get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        kwargs = super(ShippingMethodView, self).get_context_data(**kwargs)
        shipping_address = self.get_shipping_address(self.request.basket)
        if shipping_address:
            shipping_delay = self.request.basket.get_shipping_delay(shipping_address.country.iso_3166_1_a2)
        else:
            shipping_delay = None
        kwargs["methods"] = self._methods
        kwargs["shipping_delay"] = shipping_delay
        return kwargs


class AJAXShippingMethodView(ShippingMethodView):
    template_name = "oscar/checkout/ajax/shipping_methods.html"

    def get(self, request, *args, **kwargs):
        # Check that shipping is required at all
        if not request.basket.is_shipping_required():
            self.checkout_session.use_shipping_method(NoShippingRequired().code)
            return HttpResponse("")

        #        # Check that shipping address has been completed
        #        if not self.checkout_session.is_shipping_address_set():
        #            msg = _("Please choose a shipping address")
        #            return HttpResponse(msg)

        self._methods = self.get_available_shipping_methods()

        vendor_url = reverse("contact-vendor", args=(request.basket.lines.all()[0].product_id,))
        msg = _(
            "Shipping to your country is not available yet, <a href='%(vendor_url)s' target='_blank'>contact vendor</a>"
        ) % {"vendor_url": vendor_url}
        if len(self._methods) == 0:
            # No shipping methods available for given address
            return HttpResponse(msg)
        elif len(self._methods) == 1:
            # Only one shipping method - set this and redirect onto the next
            # step
            if self._methods[0].code == "not-available":
                return HttpResponse(msg)
            self.checkout_session.use_shipping_method(self._methods[0].code)
        # Must be more than one available shipping method, we present them to
        # the user to make a choice.

        # set shipping method based on basket in case shipping method is not set yet
        basket = request.basket
        shipping_address = self.get_shipping_address(basket)
        shipping_method = self.get_shipping_method(basket, shipping_address)
        basket_shipping_method = basket.shipping_method
        if not shipping_method and basket_shipping_method:
            for method in self._methods:
                if method.code == basket_shipping_method:
                    self.checkout_session.use_shipping_method(basket_shipping_method)

        context = self.get_context_data(**kwargs)
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        # Need to check that this code is valid for this user
        method_code = request.POST.get("method_code", None)
        is_valid = False
        for method in self.get_available_shipping_methods():
            if method.code == method_code:
                is_valid = True
        # Save the code for the chosen shipping method in the session
        # and continue to the next step.
        if is_valid:
            self.checkout_session.use_shipping_method(method_code)
        self._methods = self.get_available_shipping_methods()
        context = self.get_context_data(**kwargs)
        return self.render_to_response(context)


class UserAddressUpdateView(views.UserAddressUpdateView):
    """Update a user address"""

    template_name = "oscar/checkout/user_address_form.html"

    form_class = UserAddressForm


class PaymentDetailsView(OrderPlacementMixin, PaymentFormMixin, TemplateView):
    """Payment details view"""

    template_name = "oscar/checkout/payment_details.html"
    template_name_preview = "oscar/checkout/preview.html"

    # These conditions are extended at runtime depending on whether we are in
    # 'preview' mode or not.
    pre_conditions = [
        "check_basket_is_not_empty",
        "check_basket_is_valid",
        "check_user_email_is_captured",
        "check_shipping_data_is_captured",
    ]

    # If preview=True, then we render a preview template that shows all order
    # details ready for submission.
    preview = False

    no_redirect_payments = [
        PaymentForm.PAYMENT_METHOD_CASH,
        PaymentForm.PAYMENT_METHOD_DPD_CASH,
        PaymentForm.PAYMENT_METHOD_BANK_TRANSFER,
    ]

    def get_pre_conditions(self, request):
        if self.preview:
            # The preview view needs to ensure payment information has been
            # correctly captured.
            return self.pre_conditions + ["check_payment_data_is_captured"]
        return super(PaymentDetailsView, self).get_pre_conditions(request)

    def get_skip_conditions(self, request):
        if not self.preview:
            # Payment details should only be collected if necessary
            return ["skip_unless_payment_is_required"]
        return super(PaymentDetailsView, self).get_skip_conditions(request)

    def post(self, request, *args, **kwargs):
        # Posting to payment-details isn't the right thing to do.  Form
        # submissions should use the preview URL.
        if not self.preview:
            return http.HttpResponseBadRequest()

        # We use a custom parameter to indicate if this is an attempt to place
        # an order (normally from the preview page).  Without this, we assume a
        # payment form is being submitted from the payment details view. In
        # this case, the form needs validating and the order preview shown.
        if request.POST.get("action", "") == "place_order":
            return self.handle_place_order_submission(request)
        return self.handle_payment_details_submission(request)

    def handle_place_order_submission(self, request):
        """
        Handle a request to place an order.

        This method is normally called after the customer has clicked "place
        order" on the preview page. It's responsible for (re-)validating any
        form information then building the submission dict to pass to the
        `submit` method.

        If forms are submitted on your payment details view, you should
        override this method to ensure they are valid before extracting their
        data into the submission dict and passing it onto `submit`.
        """
        payment_form = self.get_payment_form()
        if not payment_form.is_valid():
            messages.error(request, "Invalid submission")
            return HttpResponseRedirect(reverse("checkout:shipping-address"))

        return self.submit(**self.build_submission(payment_form=payment_form))

    def handle_payment_details_submission(self, request):
        """
        Handle a request to submit payment details.

        This method will need to be overridden by projects that require forms
        to be submitted on the payment details view.  The new version of this
        method should validate the submitted form data and:

        - If the form data is valid, show the preview view with the forms
          re-rendered in the page
        - If the form data is invalid, show the payment details view with
          the form errors showing.

        """
        payment_form = self.get_payment_form()
        if not payment_form.is_valid():
            return self.render_payment_details(request, payment_form=payment_form)
        else:
            payment_method = payment_form.cleaned_data["payment_method"]

            return self.render_preview(
                request,
                payment_form=payment_form,
                payment_method=payment_form.get_payment_method_map()[payment_method],
            )

    def render_preview(self, request, **kwargs):
        """
        Show a preview of the order.

        If sensitive data was submitted on the payment details page, you will
        need to pass it back to the view here so it can be stored in hidden
        form inputs.  This avoids ever writing the sensitive data to disk.
        """
        self.preview = True
        ctx = self.get_context_data(**kwargs)
        return self.render_to_response(ctx)

    def render_payment_details(self, request, **kwargs):
        """
        Show the payment details page

        This method is useful if the submission from the payment details view
        is invalid and needs to be re-rendered with form errors showing.
        """
        self.preview = False
        ctx = self.get_context_data(**kwargs)
        return self.render_to_response(ctx)

    def get_default_billing_address(self):
        """
        Return default billing address for user

        This is useful when the payment details view includes a billing address
        form - you can use this helper method to prepopulate the form.

        Note, this isn't used in core oscar as there is no billing address form
        by default.
        """
        if not self.request.user.is_authenticated:
            return None
        try:
            return self.request.user.addresses.get(is_default_for_billing=True)
        except UserAddress.DoesNotExist:
            return None

    def handle_successful_order(self, order):
        response = super().handle_successful_order(order)

        if order.status == "Paid":
            for line in order.lines.all():
                line.set_status("Paid")
            order.create_invoice()

        return response

    def build_submission(self, **kwargs):
        """
        Return a dict of data to submitted to pay for, and create an order
        """
        basket = kwargs.get("basket", self.request.basket)
        payment_form = kwargs.get("payment_form")
        payment_kwargs = kwargs.get("payment_kwargs", {})
        shipping_address = self.get_shipping_address(basket)
        billing_address = self.get_billing_address(shipping_address)
        shipping_method = self.get_shipping_method(basket, shipping_address)
        shipping_charge = shipping_method.calculate(basket)
        if not shipping_method:
            total = shipping_charge = None
        else:
            shipping_charge = shipping_method.calculate(basket)
            total = self.get_order_totals(basket, shipping_charge=shipping_charge)
        payment_kwargs["shipping_address"] = shipping_address
        payment_kwargs["billing_address"] = billing_address
        submission = {
            "user": self.request.user,
            "basket": basket,
            "shipping_address": shipping_address,
            "shipping_method": shipping_method,
            "shipping_charge": shipping_charge,
            "billing_address": billing_address,
            "order_total": total,
            "order_kwargs": {"language": get_language()[:2]},
            "payment_kwargs": payment_kwargs,
        }
        if payment_form:
            submission["payment_kwargs"]["payment_method"] = payment_form.cleaned_data["payment_method"]
        if not submission["user"].is_authenticated:
            email = self.checkout_session.get_guest_email()
            submission["order_kwargs"]["guest_email"] = email
        return submission

    def submit(
        self,
        user,
        basket,
        shipping_address,
        shipping_method,
        shipping_charge,
        billing_address,
        order_total,
        payment_kwargs=None,
        order_kwargs=None,
    ):
        """
        Submit a basket for order placement.

        The process runs as follows:
         * Generate an order number
         * Freeze the basket so it cannot be modified any more (important when
           redirecting the user to another site for payment as it prevents the
           basket being manipulated during the payment process).
         * Attempt to take payment for the order
           - If payment is successful, place the order
           - If a redirect is required (eg PayPal, 3DSecure), redirect
           - If payment is unsuccessful, show an appropriate error message

        :basket: The basket to submit.
        :payment_kwargs: Additional kwargs to pass to the handle_payment method.
        :order_kwargs: Additional kwargs to pass to the place_order method.
        """

        # check stock information before starting order placing process
        if basket.is_missing_stock:
            logger.info(
                "Rejecting order process for basket #%d due to low stock level",
                basket.id,
            )
            url = reverse("basket:summary")
            return HttpResponseRedirect(url)

        if not shipping_method:
            logger.info(
                "Rejecting order process for basket #%d due to missing shipping method",
                basket.id,
            )
            messages.error(
                self.request,
                _("Sorry. This order cannot be submitted. Please try again."),
            )
            url = self.request.META.get("HTTP_REFERER", reverse("basket:summary"))
            return HttpResponseRedirect(url)

        if payment_kwargs is None:
            payment_kwargs = {}
        if order_kwargs is None:
            order_kwargs = {}

        # Taxes must be known at this point
        assert basket.is_tax_known, "Basket tax must be set before a user can place an order"
        assert shipping_charge.is_tax_known, "Shipping charge tax must be set before a user can place an order"

        payment_method = payment_kwargs["payment_method"]

        # Next, check that basket isn't empty
        if basket.is_empty:
            messages.error(self.request, _("This order cannot be submitted as the basket is empty"))
            url = self.request.META.get("HTTP_REFERER", reverse("basket:summary"))
            return HttpResponseRedirect(url)

        # We generate the order number first as this will be used
        # in payment requests (ie before the order model has been
        # created).  We also save it in the session for multi-stage
        # checkouts (eg where we redirect to a 3rd party site and place
        # the order on a different request).
        order_number = self.generate_order_number(basket)
        logger.info(
            "Order #%s: beginning submission process for basket #%d",
            order_number,
            basket.id,
        )

        # Freeze the basket so it cannot be manipulated while the customer is
        # completing payment on a 3rd party site.  Also, store a reference to
        # the basket in the session so that we know which basket to thaw if we
        # get an unsuccessful payment response when redirecting to a 3rd party
        # site.
        basket.save_temp_order_info(
            **{
                "order_number": order_number,
                "shipping_address": self.create_shipping_address(self.request.user, shipping_address),
                "billing_address": self.create_billing_address(user, billing_address, shipping_address),
                "shipping_method": shipping_method,
                "shipping_charge": shipping_charge,
                "order_total": order_total,
                "payment_kwargs": payment_kwargs,
                "order_kwargs": order_kwargs,
                "branch": self.request.branch.branch,
            }
        )
        self.freeze_basket(basket)
        self.checkout_session.set_submitted_basket(basket)

        # Handle payment.  Any payment problems should be handled by the
        # handle_payment method raise an exception, which should be caught
        # within handle_POST and the appropriate forms redisplayed.
        error_msg = _(
            "A problem occurred while processing payment for this "
            "order. Please contact customer services if this problem persists"
        )
        pre_payment.send_robust(sender=self, view=self)

        payment_response = None
        # payment_response = self.handle_payment(basket, order_number, order_total.incl_tax, **payment_kwargs)
        try:
            payment_response = self.handle_payment(basket, order_number, order_total.incl_tax, **payment_kwargs)
        except Exception as e:
            msg = str(e)
            logger.exception("Order #%s: payment error (%s)", order_number, msg)
            self.restore_frozen_basket()
            messages.error(self.request, error_msg)
            url = self.request.META.get("HTTP_REFERER", self.request.path)
            return HttpResponseRedirect(url)
            # return self.render_to_response(
            #    self.get_context_data(error=error_msg))
        post_payment.send_robust(sender=self, view=self)

        # do redirect for PayPal, PaySera, Bitcoin
        if payment_response and payment_method not in self.no_redirect_payments:
            return payment_response

        # If all is ok with payment, try and place order
        logger.info("Order #%s: payment successful, placing order", order_number)

        try:
            return self.handle_order_placement(
                order_number,
                user,
                basket,
                shipping_address,
                shipping_method,
                shipping_charge,
                billing_address,
                order_total,
                **order_kwargs
            )
        except UnableToPlaceOrder as e:
            # It's possible that something will go wrong while trying to
            # actually place an order.  Not a good situation to be in, but needs
            # to be handled gracefully.
            logger.error("Order #%s: unable to place order - %s", order_number, e)
            msg = str(e)
            self.restore_frozen_basket()
            # return self.render_to_response(self.get_context_data(error=msg))
            messages.error(self.request, error_msg)
            url = self.request.META.get("HTTP_REFERER", self.request.path)
            return HttpResponseRedirect(url)

    def handle_payment(self, basket, order_number, total_incl_tax, **kwargs):
        shipping_address = kwargs["shipping_address"]
        billing_address = kwargs["billing_address"]
        payment_method = kwargs["payment_method"]

        shipping_method = self.get_shipping_method(basket, shipping_address)
        shipping_charge = shipping_method.calculate(basket)

        customer_ip, is_routable = get_client_ip(self.request)

        basket.order_payment_method = payment_method
        basket.save()

        payment_source_name = payment_method
        payment_source_type = payment_method
        vendor_profile = basket.get_vendor().account
        customer_email = ""
        if self.request.user.is_authenticated:
            customer_email = self.request.user.email
        else:
            customer_email = self.checkout_session.get_guest_email()
        site = Site.objects.get(domain=self.request.get_host())
        secure = "s" if self.request.is_secure() else ""
        shop_settings = ShopSettings.objects.all()[0]
        user_currency = basket.get_user_currency()
        response = None

        if payment_method.startswith(PaymentForm.PAYMENT_METHOD_WTP):
            payment = payment_method.rpartition("__")[-1]
            form = WebToPaymentForm(
                dict(
                    projectid=settings.WEB_TO_PAY_ID,
                    orderid=order_number,
                    accepturl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-success")),
                    cancelurl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-cancel")),
                    callbackurl="http%s://%s%s" % (secure, site.domain, reverse("webtopay-makro")),
                    lang=settings.WEB_TO_PAY_LANGUAGES.get(get_language()[:2], "ENG"),
                    payment=payment,
                    currency=user_currency["currency"],
                    paytext=_("Payment for products"),
                    p_firstname="",
                    p_lastname="",
                    p_email=customer_email,
                    amount=int((total_incl_tax * D(user_currency["rate"])).quantize(D("0.00")) * 100),
                    test=int(shop_settings.test_payments),
                ),
                button_html="<input type='submit' value='Pay!'/>",
                password=settings.WEB_TO_PAY_PASSWORD,
            )
            template = loader.get_template("webtopay/pay.html")
            ctx = {"form": form}
            response = HttpResponse(template.render(ctx, self.request))

        elif payment_method in ["swedbank", PaymentForm.PAYMENT_METHOD_EVERYPAY]:
            everypay = EveryPayHelper()
            payment_data = {
                "amount": float(total_incl_tax),
                "customer_url": "http{}://{}{}".format(secure, site.domain, reverse("everypay:return")),
                "order_reference": order_number,
                "nonce": timezone.now().timestamp(),
                "email": customer_email,
                "customer_ip": customer_ip,
                "billing_city": billing_address.line4,
                "billing_country": billing_address.country_id,
                "billing_line1": billing_address.line1,
                "billing_postcode": billing_address.postcode,
                "shipping_city": shipping_address.line4,
                "shipping_country": shipping_address.country_id,
                "shipping_line1": shipping_address.line1,
                "shipping_postcode": shipping_address.postcode,
                "locale": (self.request.branch.branch if self.request.branch.branch != "eu" else "en"),
                "timestamp": timezone.now().isoformat(),
            }

            redirect_url, error = everypay.initiate_payment(payment_data)

            if redirect_url:
                return HttpResponseRedirect(redirect_url)
            else:
                raise Exception("Payment error {}".format(error))

        elif payment_method.startswith(PaymentForm.PAYMENT_METHOD_PAYPAL):
            # if self.request.branch.branch == "de":
            #     response = HttpResponseRedirect(reverse("paypal_checkout:checkout"))
            # else:
            #     from project.paypal.express.views import RedirectView

            #     response = RedirectView.as_view(as_payment_method=True)(self.request)
            response = HttpResponseRedirect(reverse("paypal_checkout:checkout"))
        else:
            response = True

        if response and payment_method in self.no_redirect_payments:
            # Payment successful! Record payment source
            source_type, __ = models.SourceType.objects.get_or_create(
                name=payment_source_name, code=payment_source_type
            )
            source = models.Source(source_type=source_type, amount_allocated=total_incl_tax)
            self.add_payment_source(source)
            self.add_payment_event("Authorised", total_incl_tax)

        return response

    def get_template_names(self):
        return [self.template_name_preview] if self.preview else [self.template_name]


class AJAXPaymentDetailsView(CheckoutSessionMixin, PaymentFormMixin, TemplateView):
    template_name = "oscar/checkout/ajax/payment_details.html"

    def get(self, request, *args, **kwargs):
        # Check that the user's basket is not empty
        if request.basket.is_empty:
            msg = _("You need to add some items to your basket to checkout")
            return HttpResponse(msg)

        kwargs["payment_form"] = self.payment_form
        context = self.get_context_data(**kwargs)
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        if self.payment_form.is_valid():
            payment_method = self.payment_form.cleaned_data["payment_method"]
            self.checkout_session.pay_by(payment_method)

        kwargs["payment_form"] = self.payment_form
        context = self.get_context_data(**kwargs)
        return self.render_to_response(context)
