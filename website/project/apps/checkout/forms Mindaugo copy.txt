from copy import copy

from django import forms
from django.conf import settings
from django.contrib.auth.models import User
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _, get_language

from ipware import get_client_ip
from oscar.apps.checkout import forms as checkout_forms
from oscar.apps.address import forms as address_forms
from webtopay.forms import Helpers

from project.apps.address.models import UserAddress, State
from project.apps.order.models import ShippingAddress
from project.utils import get_country_code_from_ip_address


class RegistrationForm(forms.Form):
    email = forms.EmailField(label=_("E-mail address"))
    password = forms.CharField(label=_("Enter password"), widget=forms.PasswordInput)
    confirm_password = forms.CharField(label=_("Confirm password"), widget=forms.PasswordInput)

    def clean(self):
        cleaned_data = super(RegistrationForm, self).clean()
        email = cleaned_data.get("email", "")
        if email and User._default_manager.filter(email__iexact=email).exists():
            raise forms.ValidationError(
                _("A user with your email address already exists. New account is not created.")
            )
        return cleaned_data

    def clean_confirm_password(self):
        password = self.cleaned_data.get("password", "")
        confirm_password = self.cleaned_data.get("confirm_password", "")
        if password and password != confirm_password:
            raise forms.ValidationError(_("Passwords do not match"))
        return password

    def __init__(self, *args, **kwargs):
        super(RegistrationForm, self).__init__(*args, **kwargs)
        self.fields["password"].widget.attrs["autocomplete"] = "off"
        for name, field in list(self.fields.items()):
            self.fields[name].widget.attrs["class"] = "form-control"


class PaymentMethodRadioSelect(forms.RadioSelect):
    template_name = "oscar/checkout/forms/widgets/payment_method_radio.html"
    option_template_name = "oscar/checkout/forms/widgets/payment_method_radio_option.html"


class PaymentForm(forms.Form):
    PAYMENT_METHOD_WTP = "webtopay"
    PAYMENT_METHOD_PAYPAL = "paypal"
    PAYMENT_METHOD_EVERYPAY = "everypay"
    PAYMENT_METHOD_BANK_TRANSFER = "bank_transfer"
    PAYMENT_METHOD_CASH = "cash"
    PAYMENT_METHOD_DPD_CASH = "dpd_cash"

    PAYMENT_METHOD_CHOICES = (
        (PAYMENT_METHOD_CASH, _("Cash")),
        (PAYMENT_METHOD_DPD_CASH, _("Cash on delivery")),
        (PAYMENT_METHOD_BANK_TRANSFER, _("Bank transfer")),
        (PAYMENT_METHOD_PAYPAL, _("PayPal")),
        # (PAYMENT_METHOD_EVERYPAY, _("Bank cards")),
        (PAYMENT_METHOD_WTP, _("WebToPay")),
    )

    PAYMENT_METHOD_MAP = {
        PAYMENT_METHOD_WTP: _("WebToPay"),
        PAYMENT_METHOD_BANK_TRANSFER: _("Bank transfer"),
        PAYMENT_METHOD_CASH: _("Cash"),
        PAYMENT_METHOD_DPD_CASH: _("Cash on delivery"),
        PAYMENT_METHOD_PAYPAL: _("PayPal"),
        PAYMENT_METHOD_EVERYPAY: _("Bank cards"),
        "swedbank": _(
            "Bank cards"
        ),  # temp. hack to avoid system crash, some old sessions store 'swedbank' as payment option
    }

    payment_method = forms.ChoiceField(
        label=_("Payment method"),
        choices=PAYMENT_METHOD_CHOICES,
        widget=PaymentMethodRadioSelect,
    )

    def __init__(self, *args, **kwargs):
        self.shippint_method = ""
        if "basket" in kwargs:
            self.basket = kwargs["basket"]
            del kwargs["basket"]
        if "request" in kwargs:
            self.request = kwargs["request"]
            del kwargs["request"]
        if "shipping_method" in kwargs:
            self.shipping_method = getattr(kwargs["shipping_method"], "code", "")
            del kwargs["shipping_method"]
        if "shipping_address" in kwargs:
            self.shipping_address = kwargs["shipping_address"]
            del kwargs["shipping_address"]
        super(PaymentForm, self).__init__(*args, **kwargs)

        self.country_code = self.get_country_code()
        self.vendor = self.basket.get_vendor()

        basket_total_weight = self.get_basket_total_weight()
        language = get_language()[:2]

        if self.vendor:
            if self.request.branch.branch == "eu":
                vendor_profile = self.vendor.account
            else:
                vendor_profile = self.request.branch
            all_choices = self.PAYMENT_METHOD_CHOICES
            available_choices = []
            for code, label in all_choices:
                if vendor_profile.payment_methods.filter(code=code).count() > 0:
                    if code == self.PAYMENT_METHOD_PAYPAL:
                        label = '<img src="/static/img/ico_paypal.png" alt="PayPal">'
                        available_choices.append((code, mark_safe(label)))
                        label = '<img src="/static/img/ico_cards.png" alt="PayPal Cards">'
                        available_choices.append((f"{code}__cards", mark_safe(label)))
                    # elif code == self.PAYMENT_METHOD_EVERYPAY:
                    #    label = '<img src="/static/img/ico_cards.png" alt="Bank cards">'
                    #    available_choices.append(
                    #        (self.PAYMENT_METHOD_EVERYPAY, mark_safe(label))
                    #    )
                    elif code == self.PAYMENT_METHOD_CASH:
                        if self.country_code == "LT":
                            label = '<img src="/static/img/ico_cash.png" alt="Cash">'
                            available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_BANK_TRANSFER:
                        label = '<img src="/static/img/ico_bank_transfer_{}.png" alt="Bank transfer">'.format(language)
                        available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_DPD_CASH:
                        dpd_cash_countries = self.get_dpd_cash_countries()
                        if (
                            self.country_code in dpd_cash_countries
                            and basket_total_weight <= 30
                            and "express" not in self.shipping_method
                        ):
                            label = str(_("Cash on delivery"))
                            label = "%(img)s (%(text)s)" % {
                                "text": label,
                                "img": '<img src="/static/img/COD_{}.png" height="20px" alt="Cash on delivery">'.format(
                                    language
                                ),
                            }
                            available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_WTP:
                        payment_methods = self.get_webtopay_methods()
                        for payment_method in payment_methods:
                            if payment_method["key"] == "lv_lpb":
                                continue
                            label = '<img src="%s" alt="%s" height="20px">' % (
                                payment_method["logo_url"],
                                payment_method["title"],
                            )
                            code = "webtopay__%s" % payment_method["key"]
                            available_choices.append((code, mark_safe(label)))
                    else:
                        available_choices.append((code, label))

            self.fields["payment_method"].choices = available_choices

            if self.request.branch.branch == settings.BRANCH_DE:
                self.fields["payment_method"].initial = self.PAYMENT_METHOD_PAYPAL

    def get_dpd_cash_countries(self):
        countries = self.basket.lines.all()[0].product.get_dpd_cash_countries()
        return countries

    def get_basket_total_weight(self):
        try:
            total_weight = sum([line.product.package_weight for line in self.basket.lines.all()])
        except ValueError:
            total_weight = 0
        return total_weight

    def get_country_code(self):
        if self.shipping_address:
            country_code = self.shipping_address.country.iso_3166_1_a2
        else:
            if self.request.branch.branch == "eu":
                client_ip, is_routable = get_client_ip(self.request)
                country_code = get_country_code_from_ip_address(client_ip)
                if not country_code:
                    country_code = "LT"
            else:
                country_code = self.request.branch.country
        return country_code

    def get_webtopay_methods(self):
        from django.core.cache import cache

        language = get_language()[:2]
        project_id = settings.WEB_TO_PAY_ID
        currency = self.basket.get_user_currency()
        branch = self.request.branch.branch
        cache_key = "wp_{}_{}_{}_{}_{}".format(
            project_id,
            self.country_code.lower(),
            currency["currency"].lower(),
            language,
            branch,
        )
        payment_methods = cache.get(cache_key)
        if not payment_methods:
            payment_methods = Helpers.get_payment_methods(
                project_id, self.country_code.lower(), currency["currency"], language
            )
            cache.set(cache_key, payment_methods, 1800)
        return payment_methods

    def get_payment_method_map(self):
        payment_method_map = self.PAYMENT_METHOD_MAP
        payment_methods = self.get_webtopay_methods()
        for payment_method in payment_methods:
            payment_method_map["webtopay__%s" % payment_method["key"]] = payment_method["title"]
        return payment_method_map


class ShippingAddressForm(checkout_forms.ShippingAddressForm):
    is_billing_address_same_as_shipping = forms.BooleanField(
        label=_("Shipping address is my billing address"), required=False, initial=True
    )

    class Meta:
        model = ShippingAddress
        fields = (
            "first_name",
            "company_name",
            "company_code",
            "line1",
            "line2",
            "line4",
            "country",
            "state",
            "postcode",
            "phone_number",
            "vat_number",
        )

    def __init__(self, *args, **kwargs):
        if "request" in kwargs:
            self.request = kwargs["request"]
            del kwargs["request"]
        super(ShippingAddressForm, self).__init__(*args, **kwargs)

        state_choices = [("", "----------")] + list(State.objects.values_list("code", "name"))
        self.fields["state"] = forms.ChoiceField(label=_("State/County"), choices=state_choices, required=False)
        self.fields["state"].help_text = _("Required for USA and India")
        self.fields["first_name"].label = _("Name and surname")
        self.fields["line1"].label = _("Street")
        self.fields["line1"].help_text = _("Enter street name or full address")
        self.fields["line2"].label = _("House number")
        self.fields["line2"].help_text = _("Enter 1 if does not exist")

        for name, field in list(self.fields.items()):
            self.fields[name].widget.attrs["class"] = "form-control"

        self.fields["is_billing_address_same_as_shipping"].widget.attrs["class"] = "form-check-input"

        if not kwargs["initial"] and self.request:
            if self.request.branch.branch == "eu":
                client_ip, is_routable = get_client_ip(self.request)
                country_code = get_country_code_from_ip_address(client_ip)
                if not country_code:
                    country_code = "LT"
            else:
                country_code = self.request.branch.country
            self.fields["country"].initial = country_code

        for bf_name in list(self.fields.keys()):
            bf_key = "billing_%s" % bf_name
            if bf_name != "is_billing_address_same_as_shipping":
                self.fields[bf_key] = copy(self.fields[bf_name])
                if "data" in kwargs:
                    if kwargs["data"].get("is_billing_address_same_as_shipping", "") == "on":
                        if self.fields[bf_key].required:
                            self.fields[bf_key].required = False


class UserAddressForm(address_forms.UserAddressForm):
    class Meta:
        model = UserAddress
        fields = (
            "first_name",
            "company_name",
            "company_code",
            "line1",
            "line2",
            "line4",
            "country",
            "state",
            "postcode",
            "phone_number",
            "vat_number",
        )

    def __init__(self, *args, **kwargs):
        super(UserAddressForm, self).__init__(*args, **kwargs)
        self.fields["first_name"].label = _("Name and surname")
        self.fields["line1"].label = _("Street")
        self.fields["line1"].help_text = _("Enter street name or full address")
        self.fields["line2"].label = _("House number")
        self.fields["line2"].help_text = _("Enter 1 if does not exist")
        self.fields["line4"].required = True
        self.fields["country"].initial = "LT"

        state_choices = [("", "----------")] + list(State.objects.values_list("code", "name"))
        self.fields["state"] = forms.ChoiceField(label=_("State/County"), choices=state_choices, required=False)
        self.fields["state"].help_text = _("Required for USA and India")

        for name, field in list(self.fields.items()):
            self.fields[name].widget.attrs["class"] = "form-control"
