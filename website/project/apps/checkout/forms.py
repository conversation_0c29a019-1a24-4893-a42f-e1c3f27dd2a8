from copy import copy

from django import forms
from django.conf import settings
from django.contrib.auth.models import User
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _, get_language

from ipware import get_client_ip
from oscar.apps.checkout import forms as checkout_forms
from oscar.apps.address import forms as address_forms
from webtopay.forms import Helpers

from project.apps.address.models import UserAddress, State, Country
from project.apps.order.models import ShippingAddress
from project.utils import get_country_code_from_ip_address
from project.apps.shop_settings.models import ShopSettings
from project.neopay.helpers import NeoPayHelper

import logging
logger = logging.getLogger(__name__)


class RegistrationForm(forms.Form):
    email = forms.EmailField(label=_("E-mail address"))
    password = forms.CharField(label=_("Enter password"), widget=forms.PasswordInput)
    confirm_password = forms.CharField(label=_("Confirm password"), widget=forms.PasswordInput)

    def clean(self):
        cleaned_data = super(RegistrationForm, self).clean()
        email = cleaned_data.get("email", "")
        if email and User._default_manager.filter(email__iexact=email).exists():
            raise forms.ValidationError(
                _("A user with your email address already exists. New account is not created.")
            )
        return cleaned_data

    def clean_confirm_password(self):
        password = self.cleaned_data.get("password", "")
        confirm_password = self.cleaned_data.get("confirm_password", "")
        if password and password != confirm_password:
            raise forms.ValidationError(_("Passwords do not match"))
        return password

    def __init__(self, *args, **kwargs):
        super(RegistrationForm, self).__init__(*args, **kwargs)
        self.fields["password"].widget.attrs["autocomplete"] = "off"
        for name, field in list(self.fields.items()):
            self.fields[name].widget.attrs["class"] = "form-control"


class PaymentMethodRadioSelect(forms.RadioSelect):
    template_name = "oscar/checkout/forms/widgets/payment_method_radio.html"
    option_template_name = "oscar/checkout/forms/widgets/payment_method_radio_option.html"


class PaymentForm(forms.Form):
    PAYMENT_METHOD_WTP = "webtopay"
    PAYMENT_METHOD_PAYPAL = "paypal"
    PAYMENT_METHOD_EVERYPAY = "everypay"
    PAYMENT_METHOD_BANK_TRANSFER = "bank_transfer"
    PAYMENT_METHOD_CASH = "cash"
    PAYMENT_METHOD_DPD_CASH = "dpd_cash"
    PAYMENT_METHOD_NEOPAY = "neopay"

    PAYMENT_METHOD_CHOICES = (
        (PAYMENT_METHOD_CASH, _("Cash")),
        (PAYMENT_METHOD_DPD_CASH, _("Cash on delivery")),
        (PAYMENT_METHOD_BANK_TRANSFER, _("Bank transfer")),
        (PAYMENT_METHOD_PAYPAL, _("PayPal")),
        (PAYMENT_METHOD_WTP, _("WebToPay")),
        (PAYMENT_METHOD_NEOPAY, _("NeoPay")),
    )

    PAYMENT_METHOD_MAP = {
        PAYMENT_METHOD_WTP: _("WebToPay"),
        PAYMENT_METHOD_BANK_TRANSFER: _("Bank transfer"),
        PAYMENT_METHOD_CASH: _("Cash"),
        PAYMENT_METHOD_DPD_CASH: _("Cash on delivery"),
        PAYMENT_METHOD_PAYPAL: _("PayPal"),
        PAYMENT_METHOD_EVERYPAY: _("Bank cards"),
        PAYMENT_METHOD_NEOPAY: _("NeoPay"),
        "swedbank": _(
            "Bank cards"
        ),  # temp. hack to avoid system crash, some old sessions store 'swedbank' as payment option
        "paypal__cards": _("Credit/Debit cards"),  # PayPal card payment method
    }

    payment_method = forms.ChoiceField(
        label=_("Payment method"),
        choices=PAYMENT_METHOD_CHOICES,
        widget=PaymentMethodRadioSelect(attrs={'class': 'payment-method-selector'})
    )

    def __init__(self, *args, **kwargs):
        self.shippint_method = ""
        if "basket" in kwargs:
            self.basket = kwargs["basket"]
            del kwargs["basket"]
        if "request" in kwargs:
            self.request = kwargs["request"]
            del kwargs["request"]
        if "shipping_method" in kwargs:
            self.shipping_method = getattr(kwargs["shipping_method"], "code", "")
            del kwargs["shipping_method"]
        if "shipping_address" in kwargs:
            self.shipping_address = kwargs["shipping_address"]
            del kwargs["shipping_address"]
        super(PaymentForm, self).__init__(*args, **kwargs)

        try:
            self.country_code = self.basket.country_id if self.basket else None
        except Exception as e:
            logger.error(
                f"Error getting country code in PaymentForm: {str(e)} | "
                f"Basket: {self.basket} | "
                f"Basket country_id: {getattr(self.basket, 'country_id', None)} | "
                f"Basket country: {getattr(getattr(self.basket, 'country', None), 'code', None)} | "
                f"Shipping address: {self.shipping_address} | "
                f"Shipping address country: {getattr(self.shipping_address, 'country', None)}"
            )
            self.country_code = None

        self.vendor = self.basket.get_vendor()
        
        # Log vendor information
        logger.info(f"Vendor: {self.vendor}")
        
        basket_total_weight = self.get_basket_total_weight()
        language = get_language()[:2]

        if self.vendor:
            if self.request.branch.branch == "eu":
                vendor_profile = self.vendor.account
            else:
                vendor_profile = self.request.branch
                
            # Log available payment methods
            payment_methods = vendor_profile.payment_methods.all()
            logger.info(f"Available payment methods: {[pm.code for pm in payment_methods]}")
            
            # Check if NeoPay is enabled in shop settings
            shop_settings = ShopSettings.get_settings()
            logger.info(f"NeoPay enabled in shop settings: {shop_settings.enable_neopay}")
            
            all_choices = self.PAYMENT_METHOD_CHOICES

            available_choices = []
            for code, label in all_choices:
                # Log each payment method check
                has_method = vendor_profile.payment_methods.filter(code=code).count() > 0
                logger.info(f"Checking payment method {code}: {has_method}")
                
                if has_method:
                    if code == self.PAYMENT_METHOD_PAYPAL:
                        label = '<img src="/static/img/ico_paypal.png" alt="PayPal">'
                        available_choices.append((code, mark_safe(label)))
                        cards_label = '<img src="/static/img/paypal_cards.png" alt="PayPal Cards">'
                        available_choices.append((f"{code}__cards", mark_safe(cards_label)))
                    elif code == self.PAYMENT_METHOD_CASH:
                        if self.country_code == "LT":
                            label = '<img src="/static/img/ico_cash.png" alt="Cash">'
                            available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_BANK_TRANSFER:
                        label = '<img src="/static/img/ico_bank_transfer_{}.png" alt="Bank transfer">'.format(language)
                        available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_DPD_CASH:
                        dpd_cash_countries = self.get_dpd_cash_countries()
                        if (
                            self.country_code in dpd_cash_countries
                            and basket_total_weight <= 30
                            and "express" not in self.shipping_method
                        ):
                            label = str(_("Cash on delivery"))
                            label = "%(img)s (%(text)s)" % {
                                "text": label,
                                "img": '<img src="/static/img/COD_{}.png" height="20px" alt="Cash on delivery">'.format(
                                    language
                                ),
                            }
                            available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_WTP:
                        # Check if WebToPay payments are enabled in shop settings
                        if not shop_settings.enable_webtopay:
                            logger.info("WebToPay payments are disabled in shop settings")
                            continue  # Skip this payment method if disabled
                            
                        payment_methods = self.get_webtopay_methods()
                        logger.info(f"Got {len(payment_methods)} WebToPay methods")
                        
                        for payment_method in payment_methods:
                            if payment_method["key"] == "lv_lpb":
                                continue
                            label = '<img src="%s" alt="%s" height="20px">' % (
                                payment_method["logo_url"],
                                payment_method["title"],
                            )
                            code = "webtopay__%s" % payment_method["key"]
                            available_choices.append((code, mark_safe(label)))
                    elif code == self.PAYMENT_METHOD_NEOPAY:
                        # Check if NeoPay payments are enabled in shop settings
                        if not shop_settings.enable_neopay:
                            logger.info("NeoPay payments are disabled in shop settings")
                            continue  # Skip this payment method if disabled
                            
                        logger.info("Getting NeoPay methods...")
                        # Get NeoPay bank list and add individual bank options
                        neopay_methods = self.get_neopay_methods()
                        logger.info(f"Got {len(neopay_methods)} NeoPay methods")
                        
                        if not neopay_methods:
                            # If no banks returned, at least show the generic NeoPay option
                            logger.info("No NeoPay banks found, adding generic NeoPay option")
                            label = '<img src="/static/img/neopay_logo.png" alt="NeoPay" height="20px">'
                            available_choices.append((code, mark_safe(label)))
                        else:
                            # Add each bank as a payment option
                            for payment_method in neopay_methods:
                                label = '<img src="%s" alt="%s" height="20px">' % (
                                    payment_method.get("logo_url", "/static/img/neopay_logo.png"),
                                    payment_method.get("name", "NeoPay")
                                )
                                code = "neopay__%s" % payment_method["code"]
                                available_choices.append((code, mark_safe(label)))
                    else:
                        available_choices.append((code, label))

            self.fields["payment_method"].choices = available_choices
            logger.info(f"Final payment choices: {[choice[0] for choice in available_choices]}")

            if self.request.branch.branch == settings.BRANCH_DE:
                self.fields["payment_method"].initial = self.PAYMENT_METHOD_PAYPAL

    def get_dpd_cash_countries(self):
        countries = self.basket.lines.all()[0].product.get_dpd_cash_countries()
        return countries

    def get_basket_total_weight(self):
        try:
            total_weight = sum([line.product.package_weight for line in self.basket.lines.all()])
        except ValueError:
            total_weight = 0
        return total_weight

    def get_webtopay_methods(self):
        from django.core.cache import cache

        language = get_language()[:2]
        project_id = settings.WEB_TO_PAY_ID
        currency = self.basket.get_user_currency()
        branch = self.request.branch.branch
        cache_key = "wp_{}_{}_{}_{}_{}".format(
            project_id,
            self.country_code.lower(),
            currency["currency"].lower(),
            language,
            branch,
        )
        payment_methods = cache.get(cache_key)
        if not payment_methods:
            payment_methods = Helpers.get_payment_methods(
                project_id, self.country_code.lower(), currency["currency"], language
            )
            cache.set(cache_key, payment_methods, 1800)
        return payment_methods

    def get_neopay_methods(self):
        from django.core.cache import cache
        from project.neopay.helpers import NeoPayHelper

        # Check if NEOPAY_PROJECT_ID is defined
        if not hasattr(settings, 'NEOPAY_PROJECT_ID'):
            logger.error("NEOPAY_PROJECT_ID is not defined in settings")
            return []
            
        language = get_language()[:2]
        project_id = settings.NEOPAY_PROJECT_ID
        currency = self.basket.get_user_currency()
        
        # Log the settings we're using
        logger.info(f"NeoPay settings: project_id={project_id}, country={self.country_code}, currency={currency['currency']}, language={language}")
        
        cache_key = f"neopay_{project_id}_{self.country_code.lower()}_{currency['currency'].lower()}_{language}"
        
        # Clear cache for testing
        cache.delete(cache_key)
        logger.info(f"Cleared NeoPay cache (for testing) with key: {cache_key}")
        
        payment_methods = cache.get(cache_key)
        if not payment_methods:
            logger.info(f"No cached NeoPay methods, fetching from API")
            payment_methods = NeoPayHelper.get_banks(
                project_id, self.country_code, currency["currency"], language
            )
            if payment_methods:
                logger.info(f"Got {len(payment_methods)} NeoPay methods")
                # Log first few banks for debugging
                if len(payment_methods) > 0:
                    sample = payment_methods[:3] if len(payment_methods) > 3 else payment_methods
                    logger.debug(f"Sample banks: {sample}")
                cache.set(cache_key, payment_methods, 1800)
            else:
                logger.warning(f"No NeoPay methods returned from API")
        else:
            logger.info(f"Using {len(payment_methods)} cached NeoPay methods")
        
        return payment_methods

    def get_neopay_rules(self):
        """Get NeoPay rules for the current language and country"""
        from project.neopay.helpers import NeoPayHelper
        language = get_language()[:2]
        return NeoPayHelper.get_neopay_rules(language, self.country_code)

    def get_payment_method_map(self):
        payment_method_map = self.PAYMENT_METHOD_MAP.copy()
        
        # Add WebToPay payment methods
        payment_methods = self.get_webtopay_methods()
        for payment_method in payment_methods:
            payment_method_map["webtopay__%s" % payment_method["key"]] = payment_method["title"]
            
        # Add NeoPay payment methods
        neopay_methods = self.get_neopay_methods()
        for payment_method in neopay_methods:
            payment_method_map["neopay__%s" % payment_method["code"]] = payment_method.get("name", "NeoPay")
            
        return payment_method_map


class ShippingAddressForm(checkout_forms.ShippingAddressForm):
    is_billing_address_same_as_shipping = forms.BooleanField(
        label=_("Shipping address is my billing address"), 
        required=False, 
        initial=True
    )

    class Meta:
        model = ShippingAddress
        fields = (
            "first_name",
            "company_name",
            "line1",
            "line2",
            "line4",
            "country",
            "state",
            "postcode",
            "phone_number",
            "company_code",
            "vat_number",
            "notes",
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Make company_code field not required
        self.fields["company_code"].required = False
        
        # Leiskime Oscar'ui tvarkyti country lauką
        self.adjust_country_field()
        
        # Add State field
        state_choices = [("", "----------")] + list(State.objects.values_list("code", "name"))
        self.fields["state"] = forms.ChoiceField(
            label=_("State/County"), 
            choices=state_choices, 
            required=False
        )
        self.fields["state"].help_text = _("Required for USA, Argentina and India")

        # Modify field labels
        self.fields["first_name"].label = _("Name and surname")
        self.fields["line1"].label = _("Street")
        self.fields["line1"].help_text = _("Enter street name or full address")
        self.fields["line2"].label = _("House number")
        self.fields["line2"].help_text = _("Enter 1 if does not exist")
        self.fields["notes"].widget.attrs.update({'class': 'form-control', 'rows': 3})

        # Add CSS classes
        for name, field in self.fields.items():
            field.widget.attrs["class"] = "form-control"
            if name in ["line1", "line2", "line4"]:
                field.widget.attrs["maxlength"] = "45"

        self.fields["is_billing_address_same_as_shipping"].widget.attrs["class"] = "form-check-input"

        # Add billing address fields in the same order as shipping fields
        billing_fields = {}
        for field_name in self.Meta.fields:
            if field_name != "is_billing_address_same_as_shipping":
                billing_field_name = f"billing_{field_name}"
                billing_fields[billing_field_name] = copy(self.fields[field_name])

                # Make billing fields not required if shipping address is same as billing
                if "data" in kwargs and kwargs["data"].get("is_billing_address_same_as_shipping", "") == "on":
                    billing_fields[billing_field_name].required = False
        
        # Update form fields with billing fields in correct order
        self.fields.update(billing_fields)


class UserAddressForm(address_forms.UserAddressForm):
    class Meta:
        model = UserAddress
        fields = (
            "first_name",
            "company_name",
            "line1",
            "line2",
            "line4",
            "country",
            "state",
            "postcode",
            "phone_number",
            "company_code",
            "vat_number",
            "notes",
        )

    def __init__(self, *args, **kwargs):
        super(UserAddressForm, self).__init__(*args, **kwargs)
        # Make company_code field not required
        self.fields["company_code"].required = False
        
        self.fields["first_name"].label = _("Name and surname")
        self.fields["line1"].label = _("Street")
        self.fields["line1"].help_text = _("Enter street name or full address")
        self.fields["line2"].label = _("House number")
        self.fields["line2"].help_text = _("Enter 1 if does not exist")
        self.fields["line4"].required = True
        self.fields["notes"].widget.attrs.update({'class': 'form-control', 'rows': 3})

        state_choices = [("", "----------")] + list(State.objects.values_list("code", "name"))
        self.fields["state"] = forms.ChoiceField(label=_("State/County"), choices=state_choices, required=False)
        self.fields["state"].help_text = _("Required for USA, Argentina and India")

        for name, field in list(self.fields.items()):
            self.fields[name].widget.attrs["class"] = "form-control"
