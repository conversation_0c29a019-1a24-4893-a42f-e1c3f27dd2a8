# Generated by Django 4.2.9 on 2025-05-08 17:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0027_dbschenkerfuelsurcharge'),
    ]

    operations = [
        migrations.CreateModel(
            name='DbSchenkerPriceParts',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sent_from', models.CharField(max_length=100, verbose_name='Sent from')),
                ('country', models.CharField(max_length=100, verbose_name='Country')),
                ('transport_type', models.CharField(max_length=100, verbose_name='Transport type')),
                ('cubic_ratio', models.DecimalField(decimal_places=3, max_digits=7, verbose_name='Cubic ratio')),
                ('loading_meter_ratio', models.DecimalField(decimal_places=3, max_digits=7, verbose_name='Loading meter ratio')),
                ('valid_from', models.DateField(verbose_name='Valid from')),
                ('valid_until', models.DateField(verbose_name='Valid until')),
            ],
        ),
        migrations.AlterField(
            model_name='dbschenkerfuelsurcharge',
            name='price_percentage',
            field=models.DecimalField(decimal_places=3, max_digits=7, verbose_name='Price'),
        ),
        migrations.CreateModel(
            name='DbSchenkerPricePartsLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_zone', models.CharField(max_length=100, verbose_name='From zone')),
                ('to_zone', models.CharField(max_length=100, verbose_name='To zone')),
                ('Incoterms_type', models.CharField(max_length=100, verbose_name='Incoterms type')),
                ('currency', models.CharField(max_length=3, verbose_name='Currency')),
                ('rate_code', models.CharField(max_length=100, verbose_name='Rate code')),
                ('description', models.CharField(max_length=100, verbose_name='Description')),
                ('key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='key', to='order.dbschenkerpriceparts')),
            ],
        ),
        migrations.CreateModel(
            name='DbSchenkerPricePartsInterval',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight_interval', models.CharField(max_length=100, verbose_name='Interval weight')),
                ('price_per_type', models.CharField(max_length=100, verbose_name='Price per type')),
                ('price', models.DecimalField(decimal_places=3, max_digits=7, verbose_name='Price')),
                ('zone_codes', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='zone_codes', to='order.dbschenkerpricepartsline')),
            ],
        ),
        migrations.CreateModel(
            name='DbSchenkerPricePartsCountriesTo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_post_code', models.CharField(max_length=100, verbose_name='From postcode')),
                ('to_post_code', models.CharField(max_length=100, verbose_name='To postcode')),
                ('zone', models.CharField(max_length=100, verbose_name='Zone')),
                ('country_key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='country_key', to='order.dbschenkerpriceparts')),
            ],
        ),
        migrations.CreateModel(
            name='DbSchenkerPricePartsCountriesFrom',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_post_code', models.CharField(max_length=100, verbose_name='From postcode')),
                ('to_post_code', models.CharField(max_length=100, verbose_name='To postcode')),
                ('zone', models.CharField(max_length=100, verbose_name='Zone')),
                ('order_country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_country', to='order.dbschenkerpriceparts')),
            ],
        ),
    ]
