from django.db.models import IntegerField
from django.db.models.functions import Cast

from project.apps.order.models import (
    DbSchenkerPricePartsInterval,
    DbSchenkerFuelSurcharge,
    DbSchenkerPricePartsCountriesTo,
    DbSchenkerPriceParts,
    DbSchenkerPricePartsLine,
)
import datetime
from logging import getLogger

logger = getLogger(__name__)

class DbSchenkerPriceCalculator:

    def __init__(self, org_id, form_data):
        self.org_id = org_id
        self.data = form_data

    def calculate(self):

        fuel_surcharge = DbSchenkerFuelSurcharge.objects.order_by('-date').first()

        fuel_surcharge = fuel_surcharge.price_percentage / 100 if fuel_surcharge else 0
        country_code = self.data["consignee_country_code"]
        postal_code = ''.join(filter(str.isdigit, self.data["consignee_postal_code"]))

        if self.org_id == 22:
            sent_from = "SIA"
        else:
            sent_from = "PAP"

        price_part = DbSchenkerPriceParts.objects.filter(
            country=country_code,
            sent_from=sent_from
        ).first()

        zone = price_part.zones.annotate(
            from_post_code_as_int=Cast('from_post_code', IntegerField()),
            to_post_code_as_int=Cast('to_post_code', IntegerField()),
        ).filter(
            from_post_code_as_int__lte=postal_code,
            to_post_code_as_int__gte=postal_code
        ).first()

        line = price_part.lines.filter(to_zone=zone.zone).first()

        interval_list = line.weight_intervals.all()

        shipment_gross_weight = self.data["shipment_gross_weight"]
        shipment_volumetric_weight = (self.data["shipment_length"] * self.data["shipment_width"] * self.data["shipment_height"]) / 3000
        shipment_weight = shipment_gross_weight if shipment_gross_weight > shipment_volumetric_weight else shipment_volumetric_weight

        price_of_shipment = 0  # Default value in case of error.

        for interval in interval_list:
            logger.info("DbSchenkerPriceCalculator: Checking interval %s [%s]", sent_from, interval.weight_interval)
            interval_1, interval_2 = interval.weight_interval.split(" ")
            if shipment_weight >= float(interval_1) and shipment_weight <= float(interval_2):
                price_of_shipment = interval.price
                break

        if price_of_shipment == 0:
            logger.info("DbSchenkerPriceCalculator: No matching interval found for the shipment weight. Country code: %s, Shipment weight: %s", country_code, shipment_weight)

        final_price = price_of_shipment + (price_of_shipment * fuel_surcharge)
        return final_price
