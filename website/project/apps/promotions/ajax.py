from django.http import JsonResponse
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _

from oscar.core.loading import get_model

OrderReview = get_model('reviews', 'OrderReview')


def load_more_reviews(request):
    """
    AJAX view to load more reviews.

    Expected GET parameters:
    - offset: The number of reviews already loaded
    - limit: The number of additional reviews to load

    Returns:
    - JSON with HTML for the reviews and a flag indicating if there are more reviews
    """
    offset = int(request.GET.get('offset', 0))
    limit = int(request.GET.get('limit', 6))

    # Get the next batch of reviews
    reviews = OrderReview.approved.all().order_by('-date_created', '-score')[offset:offset+limit]

    # Check if there are more reviews after this batch
    total_reviews = OrderReview.approved.count()
    has_more = (offset + limit) < total_reviews

    # Render the reviews to HTML
    html = render_to_string(
        'oscar/promotions/partials/review_cards.html',
        {'reviews': reviews, 'start_index': offset}
    )

    return JsonResponse({
        'html': html,
        'has_more': has_more,
        'total': total_reviews
    })
