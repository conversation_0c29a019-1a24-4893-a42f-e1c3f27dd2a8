from django.core.management.base import BaseCommand
import requests
from bs4 import BeautifulSoup
import logging
from project.apps.order.models import DbSchenkerFuelSurcharge
import datetime
import re

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Fetch and update prices from DB Schenker fuel surcharge"

    def handle(self, *args, **kwargs):
        url = "https://www.dbschenker.com/lt-en/business/transport/land-transport/fuel-surcharge"
        
        try:
            response = requests.get(url)
            response.raise_for_status() 
        except requests.RequestException as e:
            logger.error(f"Error fetching data for fuel surcharge from DBSchenker website: {e}")
            return

        soup = BeautifulSoup(response.content, 'html.parser')
        price_element = soup.find(class_='rte-section').findChild('strong', class_='c3-font-id')
        text = price_element.text

        match = re.search(r'([0-9]+)[.,]([0-9]+)%', text)
        if match:
            number = f"{match.group(1)}.{match.group(2)}"
            percentage_value = float(number)

        DbSchenkerFuelSurcharge.objects.create(
            price_percentage=percentage_value,
            date=datetime.datetime.now()
        )
        logger.info(f"Updated fuel surcharge price to be: {percentage_value}%")
