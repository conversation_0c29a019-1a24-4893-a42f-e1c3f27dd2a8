from django.core.management.base import BaseCommand
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter, column_index_from_string
from django.utils.dateparse import parse_date
from project.apps.order.models import (
    DbSchenkerPriceParts,
    DbSchenkerPricePartsLine,
    DbSchenkerPricePartsInterval,
    DbSchenkerPricePartsCountriesFrom,
    DbSchenkerPricePartsCountriesTo,
)
import os
import re
from logging import getLogger

logger = getLogger(__name__)

class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        current_dir = os.path.dirname(__file__)
        file_path_1 = os.path.join(current_dir, "Pabaltijo automobiliu prekyba EX to EU 2025.xlsx")
        file_path_2 = os.path.join(current_dir, "Pabaltijo Automobiliu Prekyba UAB Siauliai ( LT- 76167 ) - EU.xlsx")

        wb1 = load_workbook(file_path_1, data_only=True)
        wb2 = load_workbook(file_path_2, data_only=True)

        countries_lt_to_iso = {
            'Airija': 'IE',
            'Austrija': 'AT',
            'Belgija': 'BE',
            'Bulgarija': 'BG',
            'Čekija': 'CZ',
            'Danija': 'DK',
            'Didžioji Britanija': 'GB',
            'Estija': 'EE',
            'Graikija': 'GR',
            'Ispanija': 'ES',
            'Italija': 'IT',
            'Kroatija': 'HR',
            'Latvija': 'LV',
            'Lenkija': 'PL',
            'Norvegija': 'NO',
            'Olandija': 'NL',
            'Portugalija': 'PT',
            'Prancūzija': 'FR',
            'Rumunija': 'RO',
            'Slovakija': 'SK',
            'Slovėnija': 'SI',
            'Suomija': 'FI',
            'Švedija': 'SE',
            'Šveicarija': 'CH',
            'Vengrija': 'HU',
            'Vokietija': 'DE',
            'Turkija': 'TR',
        }

        for wb, sent_from in [(wb1, 'PAP'), (wb2, 'SIA')]:
            for sheet in wb.sheetnames:
                if sheet != "CALC":
                    sheet_obj = wb[sheet]

                    value = sheet_obj["B14"].value
                    match = re.search(r"[\d.]+", value)

                    ratio = sheet_obj["B15"].value
                    ratio_match = re.search(r"[\d.]+", ratio)

                    order_key = DbSchenkerPriceParts.objects.get_or_create(
                        sent_from = sent_from,
                        country=countries_lt_to_iso[sheet_obj["B12"].value],
                        transport_type=sheet_obj["B13"].value,
                        cubic_ratio=float(match.group()) if match else None,
                        loading_meter_ratio=float(ratio_match.group()) if ratio_match else None,
                        valid_from=parse_date(sheet_obj["B16"].value),
                        valid_until=parse_date(sheet_obj["B17"].value),
                    )

                    col_index = column_index_from_string('D')
                    while True:
                        col_letter = get_column_letter(col_index)
                        cell_value = sheet_obj[f"{col_letter}12"].value

                        if cell_value is None or str(cell_value).strip() == "":
                            break

                        line, created = DbSchenkerPricePartsLine.objects.get_or_create(
                            key = order_key[0],
                            from_zone=sheet_obj[f"{col_letter}12"].value,
                            to_zone=sheet_obj[f"{col_letter}13"].value,
                            Incoterms_type=sheet_obj[f"{col_letter}14"].value,
                            currency=sheet_obj[f"{col_letter}15"].value,
                            rate_code=sheet_obj[f"{col_letter}16"].value,
                            description=sheet_obj[f"{col_letter}17"].value,
                        )

                        for row in range(19, 41):
                            price_value = sheet_obj[f"{col_letter}{row}"].value
                            if not isinstance(price_value, (int, float)):
                                price_value = -1
                            
                            DbSchenkerPricePartsInterval.objects.create(
                                zone_codes=line,
                                weight_interval=f"{sheet_obj[f'A{row}'].value} {sheet_obj[f'B{row}'].value}",
                                price_per_type=sheet_obj[f'C{row}'].value,
                                price=price_value,
                            )

                        col_index += 1

                    for row in range(45, 47):
                        DbSchenkerPricePartsCountriesFrom.objects.create(
                            order_country = order_key[0],
                            from_post_code=sheet_obj[f'A{row}'].value,
                            to_post_code=sheet_obj[f'B{row}'].value,
                            zone=sheet_obj[f'C{row}'].value,
                        )

                    starting_row = 45
                    while True:
                        from_post = sheet_obj[f'E{starting_row}'].value
                        to_post = sheet_obj[f'F{starting_row}'].value
                        zone = sheet_obj[f'G{starting_row}'].value

                        if from_post is None and to_post is None and zone is None:
                            break

                        DbSchenkerPricePartsCountriesTo.objects.create(
                            country_key = order_key[0],
                            from_post_code=from_post,
                            to_post_code=to_post,
                            zone=zone,
                        )
                        starting_row += 1
                        
            logger.info("All data has been processed and saved to the database.")
            wb.close()