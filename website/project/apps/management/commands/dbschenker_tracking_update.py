import datetime
import logging

from django.core.management.base import BaseCommand
from django.utils import timezone

import xml.etree.ElementTree as ET

from project.apps.order.models import ShippingOrder
from project.db_schenker.helpers import DbSchenkerApiClient

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Fetch and update tracking numbers for DB Schenker shipments"

    def handle(self, *args, **kwargs):

        shipments = ShippingOrder.objects.filter(
            carrier='DB Schenker',
            shipment_date__gte=timezone.now() - datetime.timedelta(days=30)
        ).exclude(
            order__db_schenker_shipment_id=''
        ).exclude(tracking_number__contains='VNO')

        db_schenker = DbSchenkerApiClient()

        for shipment in shipments:
            data = {"booking_id": shipment.shipment_id}

            status_code, response, error_message = db_schenker.track_shipment(data)

            if str(status_code) == "200" and response:
                namespaces = {"sgi": "http://www.schenker.com/SGI/v4_0"}
                root = ET.fromstring(response)

                tracking_number = root.findtext(
                    ".//sgi:Shipment/sgi:ShipmentInfo/sgi:ShipmentBasicInfo/sgi:UniqueID",
                    namespaces=namespaces,
                )

                if tracking_number:
                    shipment.tracking_number = tracking_number
                    shipment.save()

                    logger.info(
                        f"Updated shipment {shipment.id} with tracking number {tracking_number}"
                    )
            else:
                logger.error(
                    f"Failed to fetch tracking number for shipment {shipment.id}: {error_message}"
                )
