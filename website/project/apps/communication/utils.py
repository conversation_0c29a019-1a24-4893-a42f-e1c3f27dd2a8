import logging

from django.conf import settings
from django.contrib.sites.models import Site
from django.core.mail import EmailMessage, EmailMultiAlternatives

from oscar.apps.communication.utils import Dispatcher as CoreDispatcher

from project.accounts import get_branch


class Dispatcher(CoreDispatcher):

    # Public API methods

    def dispatch_direct_messages(self, recipient_email, messages, attachments=None, bcc=None, branch=None):
        """
        Dispatch one-off messages to explicitly specified recipient email.
        """
        if messages['subject'] and (messages['body'] or messages['html']):
            return self.send_email_messages(recipient_email, messages, attachments=attachments, bcc=bcc, branch=branch)

    def dispatch_anonymous_messages(self, email, messages, attachments=None, bcc=None, branch=None):
        dispatched_messages = {}
        if email:
            dispatched_messages['email'] = (
                self.send_email_messages(email, messages, attachments=attachments, bcc=bcc, branch=branch),
                None,
            )
        return dispatched_messages

    def dispatch_user_messages(self, user, messages, attachments=None, bcc=None, branch=None):
        """
        Send messages to a site user
        """
        dispatched_messages = {}
        if messages['subject'] and (messages['body'] or messages['html']):
            dispatched_messages['email'] = self.send_user_email_messages(user, messages, attachments, bcc, branch)
        if messages['sms']:
            dispatched_messages['sms'] = self.send_text_message(user, messages['sms'])
        return dispatched_messages

    def send_user_email_messages(self, user, messages, attachments=None, bcc=None, branch=None):
        """
        Send message to the registered user / customer and collect data in database.
        """
        if not user.email:
            self.logger.warning("Unable to send email messages as user #%d has" " no email address", user.id)
            return None

        email = self.send_email_messages(user.email, messages, attachments=attachments, bcc=bcc, branch=branch)

        if settings.OSCAR_SAVE_SENT_EMAILS_TO_DB:
            self.create_email(user, messages, email)

        return email

    def send_email_messages(self, recipient_email, messages, from_email=None, attachments=None, bcc=None, branch=None):
        """
        Send email to recipient, HTML attachment optional.
        """
        from_email = from_email or settings.OSCAR_FROM_EMAIL

        if not from_email:
            branch = branch or get_branch()
            if branch == 'eu':
                from_email = getattr(settings, 'OSCAR_FROM_EMAIL', None)
            else:
                from_email = settings.BRANCH_SETTINGS[branch]['email_from']

        content_attachments, file_attachments = self.prepare_attachments(attachments)

        # Determine whether we are sending a HTML version too
        if messages['html']:
            email = EmailMultiAlternatives(
                messages['subject'],
                messages['body'],
                from_email=from_email,
                to=[recipient_email],
                attachments=content_attachments,
            )
            email.attach_alternative(messages['html'], "text/html")
        else:
            email = EmailMessage(
                messages['subject'],
                messages['body'],
                from_email=from_email,
                to=[recipient_email],
                attachments=content_attachments,
            )
        if bcc:
            email.bcc = bcc
        for attachment in file_attachments:
            email.attach_file(attachment)

        self.logger.info("Sending email to %s" % recipient_email)

        if self.mail_connection:
            self.mail_connection.send_messages([email])
        else:
            email.send()

        return email

    # def get_base_context(self):
    #     """
    #     Return context that is common to all emails
    #     """
    #     return {'site': Site.objects.get_current()}
