# Generated by Django 2.0.13 on 2019-04-30 16:35

from django.db import migrations


class Migration(migrations.Migration):

    atomic = False

    dependencies = [
        ('communication', '0001_initial'),
        ('customer', '0007_auto_20220820_1733'),
        ('order', '0001_initial'),
    ]

    operations = [
        # Alter the names of the tables migrated from the customer app
        # to the Django defaults.
        migrations.AlterModelTable(
            name='communicationeventtype',
            table=None,
        ),
        migrations.AlterModelTable(
            name='email',
            table=None,
        ),
        migrations.AlterModelTable(
            name='notification',
            table=None,
        ),
    ]
