{% extends "oscar/layout_storefront.html" %}

{% load product_tags %}
{% load i18n %}
{% load compress %}
{% block title %}
    Catalogue | {{ block.super }}
{% endblock %}

{% block headertext %}
{% endblock %}

{% comment %} {% block breadcrumbs %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ homepage_url }}">{% trans "Home" %}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ summary }}</li>
        </ol>
    </nav>
{% endblock breadcrumbs %} {% endcomment %}


{% block extrahead %}
    {{ block.super }}

    <!-- Disable HTMX history via meta tag - must be before any HTMX script loads -->
    <meta name="htmx-config" content='{"refreshOnHistoryMiss":true}'>

    <!-- Pagination links for SEO -->
    {% if is_paginated %}
        {% if page_obj.has_previous %}
            <link rel="prev" href="{{ request.path }}?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
        {% endif %}
        {% if page_obj.has_next %}
            <link rel="next" href="{{ request.path }}?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
        {% endif %}
    {% endif %}

    {% compress js inline%}
    <script>
        // Global filter store
        document.addEventListener('alpine:init', () => {
            Alpine.store('filters', {
                state: {
                    categories: [],
                    manufacturer: '',
                    models: [],
                    types: [],
                    extra_attributes: [],
                    search: '',
                    sort: '',
                    per_page: '20',
                    expanded: {},
                    viewMode: 'grid',
                    year_range: {
                        from: '',
                        to: ''
                    },
                    price_range: {
                        from: '',
                        to: ''
                    }
                },
                metadata: {
                    manufacturers: [],
                    allModels: [],
                    allTypes: [],
                    filteredModels: [],
                    filteredTypes: []
                },
                dataLoaded: false,
                init() {
                    // Initialize with URL parameters first (fast)
                    this.initFromUrl();

                    // Then load JSON data asynchronously (deferred)
                    this.loadJsonData();

                    // Initialize category tree in collapsed state
                    this.initCategoryTree();

                    // Listen for browser back/forward button events
                    window.addEventListener('popstate', () => {
                        this.initFromUrl();
                    });
                },

                initCategoryTree() {
                    // Start with all categories collapsed
                    // This maximizes performance with large category trees
                    this.state.expanded = {};
                },

                initFromUrl() {
                    // Get URL parameters
                    const urlParams = new URLSearchParams(window.location.search);

                    // Simple parameters
                    this.state.manufacturer = urlParams.get('manufacturer') || '';
                    this.state.search = urlParams.get('q') || '';
                    this.state.sort = urlParams.get('sort_by') || 'date_desc';
                    this.state.per_page = urlParams.get('per_page') || '20';
                    this.state.year_range.from = urlParams.get('year_from') || '';
                    this.state.year_range.to = urlParams.get('year_to') || '';
                    this.state.price_range.from = urlParams.get('price_from') || '';
                    this.state.price_range.to = urlParams.get('price_to') || '';

                    // Array parameters - use Set to ensure uniqueness
                    this.state.categories = [...new Set(urlParams.getAll('category_ids').map(id => parseInt(id)))];
                    this.state.models = [...new Set(urlParams.getAll('models'))];
                    this.state.types = [...new Set(urlParams.getAll('types'))];
                    this.state.extra_attributes = [...new Set(urlParams.getAll('extra_attributes'))];

                    // Get view mode from localStorage
                    this.state.viewMode = localStorage.getItem('view_mode') || 'grid';

                    // Initialize expanded state as empty - we'll expand only what's needed
                    // in the initCategoryTree method
                    this.state.expanded = {};
                },
                loadJsonData() {
                    // Use a single requestAnimationFrame to defer loading until after initial render
                    requestAnimationFrame(() => {
                        // Load initial filters data
                        this.loadInitialFilters();

                        // Use a single setTimeout with a callback chain instead of nested timeouts
                        setTimeout(() => this.loadMetadata(), 20);
                    });
                },

                loadInitialFilters() {
                    const initialFiltersEl = document.getElementById('initial-filters');
                    if (!initialFiltersEl) return;

                    try {
                        const initialFilters = JSON.parse(initialFiltersEl.textContent || '{}');

                        // Only update state properties that aren't already set from URL
                        if (!this.state.manufacturer && initialFilters.manufacturer) {
                            this.state.manufacturer = initialFilters.manufacturer;
                        }
                        if (this.state.categories.length === 0 && initialFilters.categories) {
                            this.state.categories = initialFilters.categories;
                        }
                        if (this.state.models.length === 0 && initialFilters.models) {
                            this.state.models = initialFilters.models;
                        }
                        if (this.state.types.length === 0 && initialFilters.types) {
                            this.state.types = initialFilters.types;
                        }
                        if (this.state.extra_attributes.length === 0 && initialFilters.extra_attributes) {
                            this.state.extra_attributes = initialFilters.extra_attributes;
                        }
                    } catch (e) {
                        console.error('Error parsing initial filters:', e);
                    }
                },

                loadMetadata() {
                    // Load manufacturers
                    const manufacturersEl = document.getElementById('manufacturers-data');
                    if (manufacturersEl) {
                        try {
                            this.metadata.manufacturers = JSON.parse(manufacturersEl.textContent || '[]');
                        } catch (e) {
                            console.error('Error parsing manufacturers data:', e);
                        }
                    }

                    // Models and types are now loaded on-demand via API
                    // when a manufacturer or model is selected in the vehicle filter
                    this.dataLoaded = true;
                },
                
                updateFilteredLists() {
                    // Update filtered models and types based on current selections
                    // This is a placeholder method that ensures the clearFilters function works properly
                    // The actual filtering logic would be implemented based on API responses
                    this.metadata.filteredModels = this.metadata.allModels.filter(model => 
                        !this.state.manufacturer || model.manufacturer === this.state.manufacturer
                    );
                    
                    this.metadata.filteredTypes = this.metadata.allTypes.filter(type =>
                        (!this.state.manufacturer || type.manufacturer === this.state.manufacturer) &&
                        (!this.state.models.length || this.state.models.includes(type.model))
                    );
                    
                    // Dispatch events to notify any components that depend on these lists
                    window.dispatchEvent(new CustomEvent('filters-updated'));
                },

                toggleExpanded(id) {
                    // Toggle expanded state
                    this.state.expanded[id] = !this.state.expanded[id];

                    // If expanding, ensure children are loaded
                    if (this.state.expanded[id]) {
                        this.ensureCategoryChildrenLoaded(id);
                    }
                },

                ensureCategoryChildrenLoaded(categoryId) {
                    // Find the category element
                    const categoryEl = document.querySelector(`[data-category-id="${categoryId}"]`);
                    if (!categoryEl) return;

                    const categoryNode = categoryEl.closest('.category-node');
                    if (!categoryNode) return;

                    // Make sure the category node is visible
                    categoryNode.style.display = '';

                    // Find the subcategories container
                    const subcategoriesContainer = categoryNode.querySelector('.subcategories-container');

                    // If there's a subcategories container and it has the 'lazy-load' class,
                    // we need to load the subcategories
                    if (subcategoriesContainer && subcategoriesContainer.classList.contains('lazy-load')) {
                        // Remove the lazy-load class to prevent loading again
                        subcategoriesContainer.classList.remove('lazy-load');

                        // Here you would typically make an AJAX request to load subcategories
                        // For now, we'll just show what's already in the DOM

                        // Show all child nodes
                        const childNodes = subcategoriesContainer.querySelectorAll('.category-node');
                        childNodes.forEach(node => {
                            node.style.display = '';
                        });
                    }
                },
                setManufacturer(value) {
                    this.state.manufacturer = value;
                    // Clear models, types, and extra_attributes when manufacturer changes
                    this.state.models = [];
                    this.state.types = [];
                    this.state.extra_attributes = [];
                    this.updateFilteredLists();
                    // Dispatch custom event for manufacturer change
                    window.dispatchEvent(new CustomEvent('manufacturer-changed'));
                    this.submitFilters();
                },
                toggleCategory(id) {
                    const index = this.state.categories.indexOf(id);
                    if (index !== -1) {
                        // Remove category
                        this.state.categories.splice(index, 1);
                    } else {
                        // Add category
                        this.state.categories.push(id);
                        // We no longer auto-expand parent categories
                    }
                    // Reset extra_attributes when categories change
                    this.state.extra_attributes = [];
                    // Dispatch event to notify components about cleared extra attributes
                    window.dispatchEvent(new CustomEvent('extra-attributes-cleared'));
                    this.submitFilters();
                },
                toggleModel(id) {
                    const index = this.state.models.indexOf(id);
                    if (index !== -1) {
                        this.state.models.splice(index, 1);
                    } else {
                        this.state.models.push(id);
                    }
                    // Reset extra_attributes when models change
                    this.state.extra_attributes = [];
                    // Dispatch event to notify components about cleared extra attributes
                    window.dispatchEvent(new CustomEvent('extra-attributes-cleared'));
                    this.updateFilteredLists();
                    this.submitFilters();
                },
                toggleType(id) {
                    const index = this.state.types.indexOf(id);
                    if (index !== -1) {
                        this.state.types.splice(index, 1);
                    } else {
                        this.state.types.push(id);
                    }
                    // Reset extra_attributes when types change
                    this.state.extra_attributes = [];
                    // Dispatch event to notify components about cleared extra attributes
                    window.dispatchEvent(new CustomEvent('extra-attributes-cleared'));
                    this.submitFilters();
                },
                toggleExtraAttribute(id) {
                    const idx = this.state.extra_attributes.indexOf(id);
                    if (idx !== -1) {
                        this.state.extra_attributes.splice(idx, 1);
                    } else {
                        this.state.extra_attributes.push(id);
                    }
                    this.submitFilters();
                },
                clearFilters() {
                    this.state = {
                        categories: [],
                        manufacturer: '',
                        models: [],
                        types: [],
                        extra_attributes: [],
                        search: '',
                        sort: 'date_desc',
                        per_page: '20',
                        expanded: {},
                        viewMode: this.state.viewMode,
                        year_range: {
                            from: '',
                            to: ''
                        },
                        price_range: {
                            from: '',
                            to: ''
                        }
                    };
                    this.updateFilteredLists();
                    // Dispatch a custom event to notify other components
                    window.dispatchEvent(new CustomEvent('filters-cleared'));
                    this.submitFilters();
                },
                buildUrlParams() {
                    // Create a fresh URLSearchParams object
                    const params = new URLSearchParams();

                    // Simple filters (non-array values)
                    if (this.state.search) params.set('q', this.state.search);
                    if (this.state.sort) params.set('sort_by', this.state.sort);
                    if (this.state.per_page) params.set('per_page', this.state.per_page);
                    if (this.state.manufacturer) params.set('manufacturer', this.state.manufacturer);

                    // Range filters
                    if (this.state.year_range.from) params.set('year_from', this.state.year_range.from);
                    if (this.state.year_range.to) params.set('year_to', this.state.year_range.to);
                    if (this.state.price_range.from) params.set('price_from', this.state.price_range.from);
                    if (this.state.price_range.to) params.set('price_to', this.state.price_range.to);

                    // Array filters
                    this.state.categories.forEach(id => params.append('category_ids', id));
                    this.state.models.forEach(id => params.append('models', id));
                    this.state.types.forEach(id => params.append('types', id));
                    this.state.extra_attributes.forEach(id => params.append('extra_attributes', id));

                    return params;
                },
                submitFilters: debounce(function() {
                    // Build URL parameters once
                    const urlParams = this.buildUrlParams();
                    const baseUrl = document.getElementById('catalogue-base-url').textContent || '/';
                    const url = `${baseUrl}?${urlParams.toString()}`;

                    // Use HTMX request with minimal options and explicitly disable history
                    htmx.ajax('GET', url, {
                        target: '#products-container',
                        swap: 'innerHTML',
                        headers: {
                            'HX-Request': 'true',
                            'HX-Trigger': 'filters-changed',
                            'HX-Push-Url': 'true',
                            'HX-History': 'false' // Explicitly disable history for this request
                        }
                    });
                }, 400), // Increased debounce time for better performance
                handlePopState() {
                    // Reuse the same logic as initFromUrl
                    this.initFromUrl();
                    // Update filtered lists based on new state
                    this.updateFilteredLists();
                },
                toggleViewMode(mode) {
                    this.state.viewMode = mode;
                    localStorage.setItem('view_mode', mode);
                },
                setYearRange(from, to) {
                    this.state.year_range.from = from;
                    this.state.year_range.to = to;
                    this.submitFilters();
                },
                clearYearRange() {
                    this.state.year_range.from = '';
                    this.state.year_range.to = '';
                    this.submitFilters();
                },
                setPriceRange(from, to) {
                    this.state.price_range.from = from;
                    this.state.price_range.to = to;
                    this.submitFilters();
                },
                clearPriceRange() {
                    this.state.price_range.from = '';
                    this.state.price_range.to = '';
                    this.submitFilters();
                }
            });
        });

        // Debounce function to limit frequent calls
        function debounce(func, wait) {
            let timeout;
            return function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, arguments), wait);
            };
        }
    </script>

    <!-- Component templates -->
    <script>
        document.addEventListener('alpine:init', () => {
            // Product Card Component
            Alpine.data('productCard', () => ({
                loaded: false,
                imageError: false
            }));

            // Gallery Modal Component
            Alpine.data('productGalleryModal', () => ({
                showGallery: false,
                currentProduct: null,
                currentImageIndex: 0,
                images: [],
                isLoading: false,

                openGalleryFromEvent(event) {
                    // Get product info from event
                    const productId = event.detail.productId;
                    const productTitle = event.detail.productTitle;

                    // Find the button that triggered the event
                    const button = document.querySelector(`.quick-view-btn[data-product-id="${productId}"]`);

                    if (button) {
                        try {
                            // Get images from data attribute
                            const images = JSON.parse(button.dataset.images || '[]');

                            // Open gallery with the parsed data
                            this.openGallery(
                                { title: productTitle, id: productId },
                                images,
                                0
                            );
                        } catch (e) {
                            console.error('Error parsing image data:', e);
                        }
                    } else {
                        // Fallback to old method if button not found
                        this.openGallery(
                            { title: productTitle, id: productId },
                            event.detail.images || [],
                            0
                        );
                    }
                },

                openGallery(product, images, startIndex = 0) {
                    // Reset images first to avoid showing previous product's images
                    this.images = [];
                    this.isLoading = true;

                    // Set product info immediately
                    this.currentProduct = product;
                    this.currentImageIndex = 0;
                    this.showGallery = true;

                    // Use requestAnimationFrame for better performance
                    requestAnimationFrame(() => {
                        this.images = images;
                        this.currentImageIndex = startIndex;
                        this.isLoading = false;
                    });
                },

                closeGallery() {
                    this.showGallery = false;
                    // Clear images when gallery is closed
                    setTimeout(() => {
                        if (!this.showGallery) {
                            this.images = [];
                            this.currentProduct = null;
                        }
                    }, 300); // Wait for transition to complete
                },

                nextImage() {
                    if (this.images.length > 1) {
                        this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
                    }
                },

                prevImage() {
                    if (this.images.length > 1) {
                        this.currentImageIndex = (this.currentImageIndex - 1 + this.images.length) % this.images.length;
                    }
                },

                handleKeydown(event) {
                    if (this.showGallery) {
                        if (event.key === 'Escape') {
                            this.closeGallery();
                        } else if (event.key === 'ArrowRight' || event.key === 'd') {
                            this.nextImage();
                        } else if (event.key === 'ArrowLeft' || event.key === 'a') {
                            this.prevImage();
                        } else if (event.key === 'ArrowUp' || event.key === 'w') {
                            this.prevImage();
                        } else if (event.key === 'ArrowDown' || event.key === 's') {
                            this.nextImage();
                        } else if (event.key === ' ') {
                            event.preventDefault();
                            this.nextImage();
                        }
                    }
                }
            }));

            // Recursive category tree component
            window.categoryTreeComponent = function() {
                // Add global normalization helper if not present
                if (!window.normalizeText) {
                    window.normalizeText = function(text) {
                        if (!text) return '';
                        return text.toLowerCase()
                            .normalize('NFD')
                            .replace(/[\u0300-\u036f]/g, '')
                            .replace(/[^a-z0-9\s]/g, '');
                    };
                }
                return {
                    searchQuery: '',
                    categoryTree: [],
                    categoryCounts: {},
                    init() {
                        // Initial data loading
                        this.loadCategoryTree();
                        this.loadCounts();

                        // Listening for HTMX OOB updates on the main component element
                        // so we can update the internal `categoryCounts` state
                        if (this.$el) {
                            this.$el.addEventListener('htmx:oobAfterSwap', () => {
                                this.loadCounts();
                            });
                        } else {
                            console.warn('Category tree component $el not found during init. OOB count updates might not work.');
                        }

                        // Watch for search query changes to expand matching categories
                        this.$watch('searchQuery', (value) => {
                            if (value) {
                                this.expandMatchingCategories(value);
                            }
                        });
                    },

                    // Expand all categories that contain matching items
                    expandMatchingCategories(query) {
                        if (!query) return;

                        const normQuery = window.normalizeText(query);
                        const categoriesToExpand = new Set();

                        // Find all categories that match the query or have children that match
                        const findMatchingCategories = (categories, parentPaths = []) => {
                            categories.forEach(cat => {
                                const paths = [...parentPaths, cat.id];

                                // Check if this category matches (normalized)
                                const matches = window.normalizeText(cat.name).includes(normQuery);

                                // Check if any subcategories match
                                let hasMatchingChildren = false;
                                if (cat.subcategories && cat.subcategories.length) {
                                    hasMatchingChildren = findMatchingCategories(cat.subcategories, paths);
                                }

                                // If this category or any of its children match, expand all parent categories
                                if (matches || hasMatchingChildren) {
                                    parentPaths.forEach(id => categoriesToExpand.add(id));
                                    if (hasMatchingChildren) {
                                        categoriesToExpand.add(cat.id);
                                    }
                                    return true;
                                }
                                return false;
                            });

                            // Return true if any category in this level matched
                            return categories.some(cat =>
                                window.normalizeText(cat.name).includes(normQuery) ||
                                (cat.subcategories && cat.subcategories.some(subcat =>
                                    window.normalizeText(subcat.name).includes(normQuery)
                                ))
                            );
                        };

                        findMatchingCategories(this.categoryTree);

                        // Expand all categories in the set
                        categoriesToExpand.forEach(id => {
                            Alpine.store('filters').state.expanded[id] = true;
                        });
                    },
                    loadCategoryTree() {
                        const treeEl = document.getElementById('category-tree-json');
                        if (treeEl) {
                            try {
                                this.categoryTree = JSON.parse(treeEl.textContent || '[]');
                            } catch (e) {
                                console.error('Error parsing category tree JSON:', e);
                                this.categoryTree = [];
                            }
                        } else {
                            this.categoryTree = [];
                        }
                    },
                    loadCounts() {
                        const countsEl = document.getElementById('category-counts-json');
                        if (countsEl) {
                            try {
                                this.categoryCounts = JSON.parse(countsEl.textContent || '{}');
                            } catch (e) {
                                console.error('Error parsing category counts JSON:', e);
                                this.categoryCounts = {};
                            }
                        } else {
                            // If the element doesn't exist (e.g., during initial HTML rendering without HTMX),
                            // leave the existing counts or an empty object.
                            // This is important to ensure OOB updates don't destroy the counts,
                            // if the `category-counts-json` script tag is not loaded with each OOB.
                            if (Object.keys(this.categoryCounts).length === 0) {
                                this.categoryCounts = {};
                            }
                        }
                    },
                    normalizeCompare(text, query) {
                        if (!text || !query) return false;
                        return window.normalizeText(text).includes(window.normalizeText(query));
                    },
                    matchesSearch(category) {
                        return !this.searchQuery || this.normalizeCompare(category.name, this.searchQuery);
                    },
                    get filteredCategories() {
                        if (!this.searchQuery) return this.categoryTree;
                        const query = window.normalizeText(this.searchQuery);
                        function filterTree(tree) {
                            return tree
                                .map(cat => {
                                    const children = cat.subcategories ? filterTree(cat.subcategories) : [];
                                    if (window.normalizeText(cat.name).includes(query) || children.length) {
                                        return { ...cat, subcategories: children };
                                    }
                                    return null;
                                })
                                .filter(Boolean);
                        }
                        return filterTree(this.categoryTree);
                    },
                    get selectedCategories() {
                        const ids = Alpine.store('filters').state.categories;
                        const all = [];
                        function findAll(cats) {
                            for (const cat of cats) {
                                if (ids.includes(cat.id)) all.push(cat);
                                if (cat.subcategories && cat.subcategories.length) findAll(cat.subcategories);
                            }
                        }
                        findAll(this.categoryTree);
                        return all;
                    },
                    isSelected(cat) {
                        return Alpine.store('filters').state.categories.includes(cat.id);
                    },
                    toggleCategory(cat) {
                        Alpine.store('filters').toggleCategory(cat.id);
                    },
                    isExpanded(cat) {
                        return !!Alpine.store('filters').state.expanded[cat.id];
                    },
                    toggleExpanded(cat) {
                        Alpine.store('filters').toggleExpanded(cat.id);
                    },
                    getCount(cat) {
                        return this.categoryCounts[cat.path] || 0;
                    },
                    clearSearch() {
                        this.searchQuery = '';
                        // Reset expanded state to initial state (all collapsed)
                        Alpine.store('filters').initCategoryTree();
                        this.$refs.searchInput.focus();
                    },
                    renderCategory(cat) {
                        let html = `<div class=\"flex items-center py-1\">`;
                        if (cat.subcategories && cat.subcategories.length) {
                            html += `<button onclick=\"window.categoryTreeComponentInstance.toggleExpanded(window.categoryTreeComponentInstance.findCatById(${cat.id}))\" class=\"mr-1 text-gray-500 hover:text-gray-700 focus:outline-none\"><span>${this.isExpanded(cat) ? '▼' : '▶'}</span></button>`;
                        }
                        html += `<input type=\"checkbox\" id=\"cat-${cat.id}\" ${this.isSelected(cat) ? 'checked' : ''} onchange=\"window.categoryTreeComponentInstance.toggleCategory(window.categoryTreeComponentInstance.findCatById(${cat.id}))\" class=\"mr-2 accent-gray-500 h-4 w-4 rounded border-gray-300 focus:ring-gray-500\">`;
                        html += `<label for=\"cat-${cat.id}\" class=\"cursor-pointer text-gray-700 break-words block${this.searchQuery && cat.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ? ' bg-yellow-100' : ''}\">${cat.name}</label>`;
                        if (this.getCount(cat) > 0) {
                            html += `<span class=\"ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block\">${this.getCount(cat)}</span>`;
                        }
                        html += `</div>`;
                        if (this.isExpanded(cat) && cat.subcategories && cat.subcategories.length) {
                            html += `<ul class=\"ml-4\">`;
                            for (const subcat of cat.subcategories) {
                                html += `<li>${this.renderCategory(subcat)}</li>`;
                            }
                            html += `</ul>`;
                        }
                        return html;
                    },
                    findCatById(id) {
                        let found = null;
                        function search(cats) {
                            for (const cat of cats) {
                                if (cat.id === id) { found = cat; return; }
                                if (cat.subcategories && cat.subcategories.length) search(cat.subcategories);
                            }
                        }
                        search(this.categoryTree);
                        return found;
                    }
                };
            };
        });
    </script>
    {% endcompress %}
    
    <!-- Initial data for Alpine.js -->
    <script type="text/json" id="initial-filters">
        {
            "categories": {{ initial_categories_json|safe }},
            "manufacturer": {{ initial_manufacturer_json|safe }},
            "models": {{ initial_models_json|safe }},
            "types": {{ initial_types_json|safe }},
            "extra_attributes": {{ initial_extra_attributes_json|safe }},
            "search": "{{ request.GET.q|default:'' }}",
            "sort": "{{ request.GET.sort_by|default:'date_desc' }}",
            "per_page": "{{ request.GET.per_page|default:'20' }}",
            "year_range": {
                "from": "{{ request.GET.year_from|default:'' }}",
                "to": "{{ request.GET.year_to|default:'' }}"
            },
            "price_range": {
                "from": "{{ request.GET.price_from|default:'' }}",
                "to": "{{ request.GET.price_to|default:'' }}"
            }
        }
    </script>
    <script type="text/json" id="manufacturers-data">{{ manufacturers_json|safe }}</script>
    <script type="text/json" id="catalogue-base-url">{{ catalogue_base_url }}</script>
    <script type="text/json" id="category-ids">{{ category_ids_json|safe }}</script>
    <script type="application/json" id="attribute-counts-data">{{ extra_attributes_json|safe }}</script>
{% endblock %}

{% block extra_head %}
{% endblock %}

{% block column_left %}
{% endblock %}

{% block content_wrapper %}
<div class="container mx-auto px-4">
    <!-- Loading indicator - Positioned at the top of the product container -->
    <div class="htmx-indicator flex justify-center items-center my-2 sticky top-0 z-50 bg-white/80">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>

    <div x-data="{
        mobileFiltersOpen: {
            categories: false,
            extraAttributes: false,
            rangeFilters: false,
            vehicleFilter: false
        },
        init() {
            // Check if we're on a mobile device
            const isMobile = window.innerWidth < 1024; // 1024px is the lg breakpoint in Tailwind

            // On desktop, show all filters by default
            if (!isMobile) {
                this.mobileFiltersOpen.categories = true;
                this.mobileFiltersOpen.extraAttributes = true;
                this.mobileFiltersOpen.rangeFilters = true;
                this.mobileFiltersOpen.vehicleFilter = true;
            }

            // Listen for window resize events to update filter visibility
            window.addEventListener('resize', () => {
                const isNowMobile = window.innerWidth < 1024;
                if (!isNowMobile) {
                    this.mobileFiltersOpen.categories = true;
                    this.mobileFiltersOpen.extraAttributes = true;
                    this.mobileFiltersOpen.rangeFilters = true;
                    this.mobileFiltersOpen.vehicleFilter = true;
                }
            });
        }
    }"
    x-init="init()"
    class="flex flex-col lg:flex-row gap-0 lg:gap-6">

        <!-- Mobile filter toggles - Only visible on mobile -->
        <div x-cloak class="lg:hidden z-10 bg-white py-2">
            <!-- Filter buttons in a 2-column grid -->
            <div class="grid grid-cols-2 gap-2">
                <button @click="mobileFiltersOpen.categories = !mobileFiltersOpen.categories"
                        :class="mobileFiltersOpen.categories ?
                               'order-2 flex items-center justify-center gap-1 px-3 py-2 bg-blue-950 text-white rounded-lg text-sm font-normal transition w-full' :
                               'order-2 flex items-center justify-center gap-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-normal transition w-full'">
                    <!-- Heroicons outline folder -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z" />
                    </svg>
                    <span>{% trans "Categories" %}</span>
                    <!-- Active filter indicator dot -->
                    <span x-show="$store.filters.state.categories.length > 0"
                          class="relative flex h-2 w-2 ml-1">
                        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                        <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                         class="w-4 h-4 ml-1 transition-transform" :class="mobileFiltersOpen.categories ? 'rotate-180' : ''">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <button @click="mobileFiltersOpen.vehicleFilter = !mobileFiltersOpen.vehicleFilter"
                        :class="mobileFiltersOpen.vehicleFilter ?
                               'order-1 flex items-center justify-center gap-1 px-3 py-2 bg-blue-950 text-white rounded-lg text-sm font-normal transition w-full' :
                               'order-1 flex items-center justify-center gap-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-normal transition w-full'">
                    <!-- Heroicons outline truck -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
                    </svg>
                    <span>{% trans "Vehicle Filter" %}</span>
                    <!-- Active filter indicator dot -->
                    <span x-show="$store.filters.state.manufacturer || $store.filters.state.models.length > 0 || $store.filters.state.types.length > 0"
                          class="relative flex h-2 w-2 ml-1">
                        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                        <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                         class="w-4 h-4 ml-1 transition-transform" :class="mobileFiltersOpen.vehicleFilter ? 'rotate-180' : ''">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <button @click="mobileFiltersOpen.extraAttributes = !mobileFiltersOpen.extraAttributes"
                        :class="mobileFiltersOpen.extraAttributes ?
                               'order-3 flex items-center justify-center gap-1 px-3 py-2 bg-blue-950 text-white rounded-lg text-sm font-normal transition w-full' :
                               'order-3 flex items-center justify-center gap-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-normal transition w-full'">
                    <!-- Heroicons outline tag -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6Z" />
                    </svg>
                    <span>{% trans "Attributes" %}</span>
                    <!-- Active filter indicator dot -->
                    <span x-show="$store.filters.state.extra_attributes.length > 0"
                          class="relative flex h-2 w-2 ml-1">
                        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                        <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                         class="w-4 h-4 ml-1 transition-transform" :class="mobileFiltersOpen.extraAttributes ? 'rotate-180' : ''">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
                <button @click="mobileFiltersOpen.rangeFilters = !mobileFiltersOpen.rangeFilters"
                        :class="mobileFiltersOpen.rangeFilters ?
                               'order-4 flex items-center justify-center gap-1 px-3 py-2 bg-blue-950 text-white rounded-lg text-sm font-normal transition w-full' :
                               'order-4 flex items-center justify-center gap-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-normal transition w-full'">
                    <!-- Heroicons outline adjustments-horizontal -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" />
                    </svg>
                    <span>{% trans "Range Filters" %}</span>
                    <!-- Active filter indicator dot -->
                    <span x-show="$store.filters.state.year_range.from || $store.filters.state.year_range.to || $store.filters.state.price_range.from || $store.filters.state.price_range.to"
                          class="relative flex h-2 w-2 ml-1">
                        <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-400 opacity-75"></span>
                        <span class="relative inline-flex rounded-full h-2 w-2 bg-blue-500"></span>
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                         class="w-4 h-4 ml-1 transition-transform" :class="mobileFiltersOpen.rangeFilters ? 'rotate-180' : ''">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                    </svg>
                </button>
            </div>
            <!-- Clear all filters button - Only visible when filters are active -->
            <div class="mt-2">
                <button @click="$store.filters.clearFilters()"
                        x-show="$store.filters.state.categories.length > 0 ||
                                $store.filters.state.manufacturer ||
                                $store.filters.state.models.length > 0 ||
                                $store.filters.state.types.length > 0 ||
                                $store.filters.state.extra_attributes.length > 0 ||
                                $store.filters.state.year_range.from ||
                                $store.filters.state.year_range.to ||
                                $store.filters.state.price_range.from ||
                                $store.filters.state.price_range.to"
                        class="flex items-center justify-center gap-1 px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg text-sm font-normal transition w-full">
                    <!-- Heroicons outline x-circle -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                    <span>{% trans "Clear All Filters" %}</span>
                </button>
            </div>
        </div>

        <!-- Left sidebar - Category tree -->
        <aside class="w-full lg:w-1/4">
            <!-- Category tree - Collapsible on mobile -->
            <div x-cloak x-show="mobileFiltersOpen.categories"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="lg:block">
                {% include "oscar/catalogue/partials/category_tree.html" %}
            </div>

            <!-- Extra attributes - Collapsible on mobile -->
            <div x-cloak x-show="mobileFiltersOpen.extraAttributes"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="lg:block">
                {% include "oscar/catalogue/partials/extraattribute_filter.html" %}
            </div>

            <!-- Range filters - Collapsible on mobile -->
            <div x-cloak x-show="mobileFiltersOpen.rangeFilters"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 class="lg:block">
                {% include "oscar/catalogue/partials/range_filters.html" %}
            </div>

            <!-- Clear all filters button - Desktop version, only visible when filters are active -->
            <div class="hidden lg:block mt-4" x-cloak>
                <button @click="$store.filters.clearFilters()"
                        x-show="$store.filters.state.categories.length > 0 ||
                                $store.filters.state.manufacturer ||
                                $store.filters.state.models.length > 0 ||
                                $store.filters.state.types.length > 0 ||
                                $store.filters.state.extra_attributes.length > 0 ||
                                $store.filters.state.year_range.from ||
                                $store.filters.state.year_range.to ||
                                $store.filters.state.price_range.from ||
                                $store.filters.state.price_range.to"
                        class="flex items-center justify-center gap-1 px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg text-sm font-normal transition w-full">
                    <!-- Heroicons outline x-circle -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                    <span>{% trans "Clear All Filters" %}</span>
                </button>
            </div>
        </aside>

        <!-- Main content area -->
        <main class="w-full lg:w-3/4">
            <div>
                <!-- Vehicle filter - Collapsible on mobile -->
                <div x-cloak x-show="mobileFiltersOpen.vehicleFilter"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 transform translate-y-0"
                     x-transition:leave-end="opacity-0 transform -translate-y-2"
                     class="lg:block">
                    {% include "oscar/catalogue/partials/vehicle_filter.html" %}
                </div>

                <div id="error-message" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"></div>

                <div id="products-container"
                     hx-target="this"
                     hx-preserve="true"
                     hx-history="false"
                     hx-on:htmx:after-request="if(event.detail.failed) { document.getElementById('error-message').textContent = 'Nepavyko atnaujinti produktų sąrašo'; document.getElementById('error-message').classList.remove('hidden'); } else { document.getElementById('error-message').classList.add('hidden'); };"
                     class="relative mb-6">
                    {% include "oscar/catalogue/partials/htmx_products.html" %}
                </div>

                <!-- Global Image Gallery Modal -->
                {% include "oscar/catalogue/partials/product_gallery_modal.html" %}
            </div>
        </main>
    </div>
</div>
{% endblock content_wrapper %}

{% block promotions_horizontal %}
{% endblock %}

{% block trust %}
{% endblock %}

{% block onbodyload %}
{% endblock %}
