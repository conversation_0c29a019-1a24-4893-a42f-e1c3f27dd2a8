{% load i18n %}

<div class="bg-gray-100 p-4 rounded-lg mb-6" 
    x-data="{
        // Local filter state
        localManufacturer: '',
        localModels: [],
        localTypes: [],
        allModels: [],
        allTypes: [],
        // Dropdown UI state
        openModelDropdown: false,
        openTypeDropdown: false,
        modelSearch: '',
        typeSearch: '',
        isLoadingModels: false,
        isLoadingTypes: false,
        // Alpine init
        init() {
            // Wait for data to be loaded
            if (!$store.filters.dataLoaded) {
                this.$watch('$store.filters.dataLoaded', (value) => {
                    if (value) {
                        this.initLocalFilters();
                    }
                });
            } else {
                this.initLocalFilters();
            }
        },
        initLocalFilters() {
            // If no manufacturer is selected in the global store, set it to 'all'
            if (!$store.filters.state.manufacturer) {
                this.localManufacturer = 'all';
            } else {
                this.localManufacturer = $store.filters.state.manufacturer;
            }
            this.localModels = [...$store.filters.state.models];
            this.localTypes = [...$store.filters.state.types];
            
            // If manufacturer is already selected and it's not 'all', fetch its models
            if (this.localManufacturer && this.localManufacturer !== 'all') {
                this.fetchModels();
            }
            
            // If models are already selected, fetch their types
            if (this.localModels.length > 0) {
                this.fetchTypes();
            }
        },
        // Fetch models data from API based on selected manufacturer
        fetchModels() {
            if (!this.localManufacturer) return;
            
            const manufacturerId = this.localManufacturerId();
            if (!manufacturerId) return;
            
            this.isLoadingModels = true;
            fetch(`/ctg/storefront/manufacturer/get_models/?manufacturers=${manufacturerId}`)
                .then(response => response.json())
                .then(data => {
                    // Convert array format to object format for consistency with the rest of the code
                    this.allModels = data.map(model => ({
                        id: model[0],
                        name: model[1],
                        // We don't have manufacturer_id in the response, but we know it from the request
                        manufacturer_id: manufacturerId
                    }));
                    this.isLoadingModels = false;
                })
                .catch(error => {
                    console.error('Error fetching models:', error);
                    this.isLoadingModels = false;
                });
        },
        // Fetch types data from API based on selected models
        fetchTypes() {
            if (this.localModels.length === 0) {
                this.allTypes = [];
                return;
            }
            
            this.isLoadingTypes = true;
            const modelParams = this.localModels.join(',');
            fetch(`/ctg/storefront/model/get_types/?models=${modelParams}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Convert array format to object format
                    this.allTypes = data.map(type => ({
                        id: type[0],
                        name: type[1],
                        // We can't reliably determine the model_id from the API response
                        // For filtering, this is actually ok since we filter by type ID directly
                        model_id: ''
                    }));
                    this.isLoadingTypes = false;
                })
                .catch(error => {
                    console.error('Error fetching types:', error);
                    this.isLoadingTypes = false;
                    // Reset types on error
                    this.allTypes = [];
                });
        },
        // Set manufacturer and reset dependent filters
        setLocalManufacturer(slug) {
            this.localManufacturer = slug;
            this.localModels = [];
            this.localTypes = [];
            this.allTypes = [];
            
            // Fetch models when manufacturer changes
            if (slug !== 'all') {
                this.fetchModels();
            } else {
                this.allModels = [];
            }
        },
        // Toggle model selection
        toggleLocalModel(modelId) {
            const index = this.localModels.indexOf(modelId);
            if (index === -1) {
                this.localModels.push(modelId);
            } else {
                this.localModels.splice(index, 1);
            }
            this.localTypes = []; // Reset types when models change
            
            // Fetch types when models change
            this.fetchTypes();
        },
        // Toggle type selection
        toggleLocalType(typeId) {
            const index = this.localTypes.indexOf(typeId);
            if (index === -1) {
                this.localTypes.push(typeId);
            } else {
                this.localTypes.splice(index, 1);
            }
        },
        // Apply local state to global store and submit
        applyLocalFiltersToGlobalStoreAndSubmit() {
            $store.filters.state.manufacturer = this.localManufacturer;
            $store.filters.state.models = [...this.localModels];
            $store.filters.state.types = [...this.localTypes];
            $store.filters.state.extra_attributes = [];
            $store.filters.submitFilters();
        },
        // Get manufacturer ID from slug
        localManufacturerId() {
            const m = $store.filters.metadata.manufacturers.find(m => m.slug === this.localManufacturer);
            return m ? m.id : null;
        },
        // Filtered models for selected manufacturer
        get filteredModels() {
            return this.allModels;
        },
        // Filtered types for selected models
        get filteredTypes() {
            return this.allTypes;
        },
        // Filtered models for search
        get filteredModelsList() {
            let models = this.filteredModels;
            if (this.modelSearch !== '') {
                models = models.filter(m => m.name.toLowerCase().includes(this.modelSearch.toLowerCase()));
            }
            // Sort selected models first
            return models.sort((a, b) => {
                const aSelected = this.localModels.includes(a.id.toString());
                const bSelected = this.localModels.includes(b.id.toString());
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return 0;
            });
        },
        // Filtered types for search
        get filteredTypesList() {
            let types = this.filteredTypes;
            if (this.typeSearch !== '') {
                types = types.filter(t => t.name.toLowerCase().includes(this.typeSearch.toLowerCase()));
            }
            // Sort selected types first
            return types.sort((a, b) => {
                const aSelected = this.localTypes.includes(a.id.toString());
                const bSelected = this.localTypes.includes(b.id.toString());
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                return 0;
            });
        },
        // Selected models text for button
        get selectedModelsText() {
            if (this.localModels.length === 0) return '{% trans "Select" %}';
            if (this.localModels.length === 1) {
                const model = this.filteredModels.find(m => m.id.toString() === this.localModels[0]);
                return model ? model.name : '';
            }
            return `${this.localModels.length} {% trans 'selected' %}`;
        },
        // Selected types text for button
        get selectedTypesText() {
            if (this.localTypes.length === 0) return '{% trans "Select" %}';
            if (this.localTypes.length === 1) {
                const type = this.filteredTypes.find(t => t.id.toString() === this.localTypes[0]);
                return type ? type.name : '';
            }
            return `${this.localTypes.length} {% trans 'selected' %}`;
        },
        // Reset scroll position for dropdowns
        resetScroll(element) {
            if (element) {
                element.scrollTop = 0;
            }
        }
    }"
    @filters-cleared.window="initLocalFilters()"
    x-on:manufacturer-selected="setLocalManufacturer($event.detail)">
    <h4 class="text-lg font-semibold mb-3">{% trans "Vehicle" %}</h4>
    <div class="grid grid-cols-1 md:grid-cols-[1fr_1fr_1fr_auto] gap-4">
        <!-- Manufacturer dropdown with search -->
        <div x-data="{ 
            open: false, 
            search: '', 
            get filteredManufacturers() {
                return this.search === '' 
                    ? $store.filters.metadata.manufacturers 
                    : $store.filters.metadata.manufacturers.filter(m => 
                        m.brand.toLowerCase().includes(this.search.toLowerCase())
                      )
            }
        }" x-init="$watch('open', value => { 
            if(value) {
                search = '';
            }
        })">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Manufacturer" %}</label>
                <div class="relative">
                    <!-- Dropdown button -->
                    <button 
                        @click="open = !open; if(open) $nextTick(() => $refs.manufacturerSearch.focus())" 
                        @click.away="open = false"
                        type="button" 
                        class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
                    >
                        <span x-text="localManufacturer === 'all' ? '{% trans "All manufacturers" %}' : (localManufacturer ? $store.filters.metadata.manufacturers.find(m => m.slug === localManufacturer)?.brand : '{% trans "Select" %}')"></span>
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': open}">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>

                    <!-- Dropdown menu -->
                    <div 
                        x-show="open"
                        x-transition:enter="transition ease-out duration-100"
                        x-transition:enter-start="transform opacity-0 scale-95"
                        x-transition:enter-end="transform opacity-100 scale-100"
                        x-transition:leave="transition ease-in duration-75"
                        x-transition:leave-start="transform opacity-100 scale-100"
                        x-transition:leave-end="transform opacity-0 scale-95"
                        class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
                    >
                        <!-- Search input -->
                        <div class="px-3 py-2 bg-white border-b border-gray-200">
                            <input 
                                type="text" 
                                x-model="search" 
                                x-ref="manufacturerSearch"
                                @click.stop
                                placeholder="{% trans 'Quick search...' %}"
                                spellcheck="false"
                                class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                        </div>
                        <!-- Manufacturers list -->
                        <div class="overflow-y-auto max-h-60" x-ref="manufacturersList">
                            <!-- All manufacturers option -->
                            <div 
                                @click="$dispatch('manufacturer-selected', 'all'); open = false"
                                class="cursor-pointer select-none relative py-2 pl-4 pr-9 hover:bg-gray-100"
                                :class="{'bg-blue-100': $root.localManufacturer === 'all'}"
                            >
                                <span class="block truncate">{% trans "All manufacturers" %}</span>
                            </div>
                            <template x-for="manufacturer in filteredManufacturers" :key="manufacturer.id">
                                <div 
                                    @click="$dispatch('manufacturer-selected', manufacturer.slug); open = false"
                                    class="cursor-pointer select-none relative py-2 pl-4 pr-9 hover:bg-gray-100"
                                    :class="{'bg-blue-100': manufacturer.slug === $root.localManufacturer}"
                                >
                                    <span x-text="manufacturer.brand" class="block truncate"></span>
                                </div>
                            </template>
                            <!-- Empty state -->
                            <div x-show="filteredManufacturers.length === 0" class="py-3 px-4 text-sm text-gray-500 italic">
                                {% trans "No manufacturers found" %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Model dropdown with checkboxes and search -->
        <div class="mb-4" id="model-container">
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Model" %}</label>
            <div class="relative">
                <!-- Dropdown button -->
                <button 
                    @click="filteredModels.length > 0 && (openModelDropdown = !openModelDropdown); if(openModelDropdown) { modelSearch = ''; $nextTick(() => { $refs.modelSearch.focus(); resetScroll($refs.modelsList); }) }" 
                    @click.away="openModelDropdown = false"
                    type="button" 
                    class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
                    :class="{'bg-gray-100 cursor-not-allowed': filteredModels.length === 0}"
                >
                    <span x-text="isLoadingModels ? '{% trans "Loading..." %}' : (filteredModels.length > 0 ? selectedModelsText : '{% trans 'Select a manufacturer first' %}')"
                          class="block truncate"
                          :class="{'text-gray-500 italic': filteredModels.length === 0 || isLoadingModels}"></span>
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': openModelDropdown}">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- Dropdown menu -->
                <div 
                    x-show="openModelDropdown"
                    x-transition:enter="transition ease-out duration-100"
                    x-transition:enter-start="transform opacity-0 scale-95"
                    x-transition:enter-end="transform opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-75"
                    x-transition:leave-start="transform opacity-100 scale-100"
                    x-transition:leave-end="transform opacity-0 scale-95"
                    class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
                >
                    <!-- Search input -->
                    <div class="px-3 py-2 bg-white border-b border-gray-200">
                        <input 
                            type="text" 
                            x-model="modelSearch" 
                            x-ref="modelSearch"
                            @click.stop
                            placeholder="{% trans 'Quick search...' %}"
                            spellcheck="false"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                    </div>
                    <!-- Loading indicator -->
                    <div x-show="isLoadingModels" class="py-3 px-4 text-sm text-gray-500 flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {% trans "Loading..." %}
                    </div>
                    <!-- Models list -->
                    <div class="overflow-y-auto max-h-64" x-show="!isLoadingModels" x-ref="modelsList">
                        <template x-for="model in filteredModelsList" :key="model.id">
                            <div class="px-4 py-2 cursor-pointer hover:bg-gray-100">
                                <label class="flex items-center gap-x-2 cursor-pointer">
                                    <input 
                                        type="checkbox" 
                                        :value="model.id" 
                                        :checked="localModels.includes(model.id.toString())"
                                        @change="toggleLocalModel(model.id.toString())"
                                        @click.stop
                                    >
                                    <span x-text="model.name" class="text-sm"></span>
                                </label>
                            </div>
                        </template>
                        <!-- Empty state -->
                        <div x-show="filteredModelsList.length === 0 && filteredModels.length > 0 && !isLoadingModels" class="py-3 px-4 text-sm text-gray-500 italic">
                            {% trans "No models found" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Type dropdown with checkboxes and search -->
        <div class="mb-4" id="type-container">
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Type" %}</label>
            <div class="relative">
                <!-- Dropdown button -->
                <button 
                    @click="filteredTypes.length > 0 && (openTypeDropdown = !openTypeDropdown); if(openTypeDropdown) { typeSearch = ''; $nextTick(() => { $refs.typeSearch.focus(); resetScroll($refs.typesList); }) }" 
                    @click.away="openTypeDropdown = false"
                    type="button" 
                    class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
                    :class="{'bg-gray-100 cursor-not-allowed': filteredTypes.length === 0}"
                >
                    <span x-text="isLoadingTypes ? '{% trans "Loading..." %}' : (filteredTypes.length > 0 ? selectedTypesText : '---')"
                          class="block truncate"
                          :class="{'text-gray-500 italic': filteredTypes.length === 0 || isLoadingTypes}"></span>
                    <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': openTypeDropdown}">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- Dropdown menu -->
                <div 
                    x-show="openTypeDropdown"
                    x-transition:enter="transition ease-out duration-100"
                    x-transition:enter-start="transform opacity-0 scale-95"
                    x-transition:enter-end="transform opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-75"
                    x-transition:leave-start="transform opacity-100 scale-100"
                    x-transition:leave-end="transform opacity-0 scale-95"
                    class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
                >
                    <!-- Search input -->
                    <div class="px-3 py-2 bg-white border-b border-gray-200">
                        <input 
                            type="text" 
                            x-model="typeSearch" 
                            x-ref="typeSearch"
                            @click.stop
                            placeholder="{% trans 'Quick search...' %}"
                            spellcheck="false"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        >
                    </div>
                    <!-- Loading indicator -->
                    <div x-show="isLoadingTypes" class="py-3 px-4 text-sm text-gray-500 flex items-center justify-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {% trans "Loading..." %}
                    </div>
                    <!-- Types list -->
                    <div class="overflow-y-auto max-h-48" x-show="!isLoadingTypes" x-ref="typesList">
                        <template x-for="type in filteredTypesList" :key="type.id">
                            <div class="px-4 py-2 cursor-pointer hover:bg-gray-100">
                                <label class="flex items-center gap-x-2 cursor-pointer">
                                    <input 
                                        type="checkbox" 
                                        :value="type.id" 
                                        :checked="localTypes.includes(type.id.toString())"
                                        @change="toggleLocalType(type.id.toString())"
                                        @click.stop
                                    >
                                    <span x-text="type.name" class="text-sm"></span>
                                </label>
                            </div>
                        </template>
                        <!-- Empty state -->
                        <div x-show="filteredTypesList.length === 0 && filteredTypes.length > 0 && !isLoadingTypes" class="py-3 px-4 text-sm text-gray-500 italic">
                            {% trans "No types found" %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Apply filters button -->
        <div class="mb-4 flex items-end">
            <button 
                @click="applyLocalFiltersToGlobalStoreAndSubmit()"
                class="h-[42px] px-4 bg-gray-700 hover:bg-blue-700 text-white font-normal rounded-md transition-colors flex items-center justify-center gap-2 whitespace-nowrap"
            >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z" />
                </svg>
                {% trans "Apply" %}
            </button>
        </div>
    </div>
</div>
