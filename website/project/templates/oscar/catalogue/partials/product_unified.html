{% load reviews_tags %}
{% load image_tags %}
{% load i18n %}
{% load custom_currency_filters %}
{% load display_tags %}
{% load static %}
{% load contact_info_tags %}
{% load custom_purchase_info_tags %}
{% load thumbnail %}
{% load catalogue_tags %}
{% load custom_product_tags %}
{% get_saved_products as saved_products %}

{% block product %}
<div
    x-data="{
        get viewMode() { return Alpine.store('filters').state.viewMode; },
        isMobile: window.innerWidth < 768,
        loaded: false,
        imageError: false,
        init() {
            // Use a single resize observer instead of window resize event
            if (window.ResizeObserver) {
                const ro = new ResizeObserver(entries => {
                    this.isMobile = window.innerWidth < 768;
                });
                ro.observe(document.documentElement);
            }
        }
    }"
    x-bind:class="{
        'bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-300 overflow-hidden h-full flex flex-col border border-gray-300': viewMode === 'grid',
        'bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-300 overflow-hidden w-full border border-gray-300 flex flex-wrap md:flex-row': viewMode === 'list'
    }"
    hx-preserve="true"
    data-product-id="{{ product.id }}"
    x-cloak>

    <!-- Single Image Component for All Views -->
    {% with image=product.images.first %}
    <a href="{{ product.get_absolute_url }}" title="{{ product.get_title }}"
       class="relative overflow-hidden flex items-center justify-center"
       x-bind:class="{
           'aspect-[4/3]': viewMode === 'grid',
           'w-1/4 aspect-square': viewMode === 'list' && isMobile,
           'w-full md:w-1/4 aspect-[4/3] md:aspect-auto md:min-h-[150px]': viewMode === 'list' && !isMobile
       }">
        <!-- Placeholder -->
        <div x-show="!loaded" class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <svg class="w-8 h-8 md:w-12 md:h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
        </div>

        <!-- Main image - single picture element that adapts to view mode -->
        {% if image and image.original %}
            <picture class="absolute inset-0 w-full h-full" x-show="!imageError">
                <!-- WebP sources with different sizes for different viewports -->
                {% thumbnail image.original "200x150" crop="center" format="WEBP" as thumb_small_webp %}
                {% thumbnail image.original "300x225" crop="center" format="WEBP" as thumb_medium_webp %}
                {% thumbnail image.original "400x300" crop="center" format="WEBP" as thumb_large_webp %}
                
                <source 
                    srcset="{{ thumb_small_webp.url }} 200w, {{ thumb_medium_webp.url }} 300w, {{ thumb_large_webp.url }} 400w"
                    sizes="(max-width: 768px) 25vw, (max-width: 1024px) 33vw, 300px"
                    type="image/webp" 
                    onerror="this.remove()"
                >
                {% endthumbnail %}
                {% endthumbnail %}
                {% endthumbnail %}
                
                <!-- Fallback JPEG sources with different sizes -->
                {% thumbnail image.original "200x150" crop="center" as thumb_small %}
                {% thumbnail image.original "300x225" crop="center" as thumb_medium %}
                {% thumbnail image.original "400x300" crop="center" as thumb_large %}
                
                <img
                    class="w-full h-full object-cover object-center"
                    srcset="{{ thumb_small.url }} 200w, {{ thumb_medium.url }} 300w, {{ thumb_large.url }} 400w"
                    sizes="(max-width: 768px) 25vw, (max-width: 1024px) 33vw, 300px"
                    src="{{ thumb_medium.url }}"
                    alt="{{ product.get_title }}"
                    @load="loaded = true"
                    @error="imageError = true; loaded = true"
                    fetchpriority="{% if forloop.counter <= 8 %}high{% else %}auto{% endif %}"
                    decoding="async"
                    hx-preserve="true"
                    {% if forloop.counter <= 8 %}loading="eager"{% else %}loading="lazy"{% endif %}
                >
                {% endthumbnail %}
                {% endthumbnail %}
                {% endthumbnail %}
            </picture>
        {% else %}
            <script>
                // Set loaded and imageError immediately without waiting for DOMContentLoaded
                setTimeout(() => {
                    if (typeof Alpine !== 'undefined') {
                        Alpine.data.loaded = true;
                        Alpine.data.imageError = true;
                    }
                }, 0);
            </script>
        {% endif %}

        <!-- Fallback image -->
        <img
            class="absolute inset-0 w-full h-full object-cover object-center"
            src="{% static 'oscar/img/image_not_found.jpg' %}"
            alt="No image available"
            x-show="imageError"
            @load="loaded = true"
            decoding="async"
            hx-preserve="true"
        >

        <!-- Quick View Button -->
        {% if product.images.all|length > 0 %}
        <button
            @click.stop.prevent="$dispatch('open-gallery', {
                productId: {{ product.id }},
                productTitle: '{{ product.get_title|escapejs }}'
            })"
            data-product-id="{{ product.id }}"
            data-product-title="{{ product.get_title|escapejs }}"
            data-images='[
                {% for image in product.images.all %}
                    {% thumbnail image.original '1200x900' upscale=False crop='center' as large_thumb %}
                    {% thumbnail image.original '100x75' crop='center' as thumb %}
                    {
                        "url": "{{ large_thumb.url }}",
                        "thumbnail": "{{ thumb.url }}"
                    }{% if not forloop.last %},{% endif %}
                    {% endthumbnail %}
                    {% endthumbnail %}
                {% endfor %}
            ]'
            class="absolute right-1.5 top-1.5 bg-white/70 hover:bg-opacity-100 rounded-full p-1.5 shadow-md transition-all duration-200 z-10 focus:outline-none quick-view-btn"
            title="{% trans 'Quick view' %}"
            hx-preserve="true"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </button>
        {% endif %}
    </a>
    {% endwith %}

    <!-- Product Info - Unified for all views with conditional display -->
    <div class="p-4"
         x-bind:class="{
            'w-3/4': viewMode === 'list' && isMobile,
            'flex-grow flex flex-col justify-between': !isMobile || viewMode === 'grid',
            'md:w-1/2': viewMode === 'list' && !isMobile
         }">
        <div>
            <h3 class="text-sm md:text-base font-semibold text-gray-800 mb-2">
                <a href="{{ product.get_absolute_url }}" title="{{ product.get_title }}" class="hover:text-blue-600"
                   x-bind:class="{
                       'line-clamp-1': viewMode === 'list' && isMobile,
                       'line-clamp-2': viewMode === 'grid',
                       'line-clamp-1': viewMode === 'list' && !isMobile
                   }">
                    {{ product.main_category.name }}
                </a>
            </h3>

            <!-- Product description: always visible -->
            <p class="text-sm text-gray-600 mb-2"
               x-bind:class="{
                   'line-clamp-3': viewMode === 'list' && isMobile,
                   'line-clamp-5': viewMode === 'grid' || (viewMode === 'list' && !isMobile)
               }">
                {{ product.title }}
            </p>

            <!-- Type information - shown in list view (both mobile and desktop) -->
            {% with typ=product.tecpap_attributes.all.0.typ.all.0 %}
                {% if typ %}
                <p class="text-xs text-gray-500 mt-1" x-show="viewMode === 'list'">
                    <span class="font-medium">{% trans "Type" %}:</span>
                    {{ typ.name }}
                    {% if typ.main_engine %}
                        ({{ typ.main_engine.eng.code }})
                    {% endif %}
                    {% if typ.kw_from %}<span class="ml-2">{{ typ.kw_from }} kW</span>{% endif %}
                    {% if typ.hp_from %}<span class="ml-2">{{ typ.hp_from }} hp</span>{% endif %}
                    {% if typ.ccm %}<span class="ml-2">{{ typ.ccm }} cc</span>{% endif %}
                    {% if typ.fuel %}<span class="ml-2">{{ typ.fuel.name }}</span>{% endif %}
                </p>
                {% endif %}
            {% endwith %}

            <!-- Extra attributes - shown in list view (both mobile and desktop) -->
            {% if product.extra_attributes.all %}
            <div class="flex flex-wrap gap-1 mt-1" x-show="viewMode === 'list'">
                {% for attr in product.extra_attributes.all %}
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {{ attr.name }}
                </span>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Unified Price and Buy Section with conditional display -->
    <div x-bind:class="{
            'w-full px-4 pb-4 flex items-center justify-between': viewMode === 'list' && isMobile,
            'px-4 pb-4 flex flex-col justify-center items-end text-right': viewMode === 'grid',
            'px-4 pb-4 flex flex-col justify-center items-end md:w-1/4 border-l border-gray-100': viewMode === 'list' && !isMobile
         }">

        <!-- Mobile List View: Price and Availability -->
        <div x-show="viewMode === 'list' && isMobile" class="flex items-center">
            <div>
                {% with session=product.purchase_info %}
                    {% if session and session.price.exists %}
                        <div class="flex flex-col">
                            <span class="text-lg font-bold text-gray-900">
                            {% if session.price.is_tax_known %}
                                {{ session.price.incl_tax|user_currency:user_currency }}
                            {% else %}
                                {{ session.price.excl_tax|user_currency:user_currency }}
                            {% endif %}
                            </span>
                            {% if session.stockrecord %}
                                <span class="text-sm {% if session.stockrecord.net_stock_level > 0 %}text-green-600{% else %}text-red-600{% endif %}">
                                    {% if session.stockrecord.net_stock_level > 0 %}
                                        {% trans "In stock" %}: {{ session.stockrecord.net_stock_level }}
                                        {% if product.condition == 'defect' or product.condition == 'broken' %}
                                            <svg class="inline w-4 h-4 text-red-500 ml-1 align-text-bottom" fill="none" stroke="currentColor" viewBox="0 0 24 24" role="img" aria-label="{% trans 'With defect' %}">
                                                <title>{% trans "With defect" %}</title>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                        {% endif %}
                                    {% else %}
                                        {% trans "Out of stock" %}
                                    {% endif %}
                                </span>
                            {% endif %}
                        </div>
                    {% else %}
                        <span class="text-lg font-bold text-gray-500">{% trans 'N/A' %}</span>
                    {% endif %}
                {% endwith %}
            </div>
        </div>

        <!-- Desktop/Grid View: Price and Availability -->
        <div x-show="!isMobile || viewMode === 'grid'" class="mb-3 w-full md:w-auto">
            {% include "oscar/catalogue/partials/stock_record_new.html" with user_currency=user_currency %}
        </div>

        <!-- Buy Now Button - Different for mobile list view -->
        <div x-show="viewMode === 'list' && isMobile" class="flex items-center space-x-2">
            {% include "oscar/catalogue/partials/add_to_basket_form_compact_new.html" with mobile=True %}
        </div>

        <!-- Buy Now Button - Desktop/Grid View -->
        <div x-show="!isMobile || viewMode === 'grid'">
            {% include "oscar/catalogue/partials/add_to_basket_form_compact_new.html" %}
        </div>
    </div>
</div>
{% endblock %}
