{% load i18n %}
{% load catalogue_tags %}
{% load custom_product_tags %}

{# Get all saved products for the current user in a single query #}
{% get_saved_products as saved_products %}

{# Assume 'product' object with 'purchase_info' attribute is available in context #}
{% with session=product.purchase_info %}
    {% if session and session.availability.is_available_to_buy %}
        {% if not product.is_full_car %}
            <div class="flex {% if not mobile %}w-full{% endif %} space-x-1 items-center">
                {% if user.is_authenticated %}
                    <div
                        x-data="{
                            saved: {% if product.id|is_saved_product:saved_products %}true{% else %}false{% endif %},
                            saving: false,
                            error: false,
                            errorMessage: '',
                            saveForLater() {
                                this.saving = true;
                                this.error = false;

                                const formData = new FormData();
                                formData.append('product_id', '{{ product.id }}');
                                formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

                                fetch('{% url 'basket:save-for-later' %}', {
                                    method: 'POST',
                                    body: formData,
                                    headers: {
                                        'X-Requested-With': 'XMLHttpRequest',
                                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                                    }
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        return response.json().then(data => {
                                            throw new Error(data.message || 'Network response was not ok');
                                        });
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    this.saving = false;
                                    // If the product is successfully saved, change the bookmark to filled
                                    if (data.success) {
                                        this.saved = true;
                                        // No need to reset the saved state if it's already saved
                                        if (!data.already_saved) {
                                            // Show a temporary notification that it was saved
                                            setTimeout(() => {
                                                // Keep the heart filled, just remove any notification
                                            }, 2000);
                                        }
                                    } else {
                                        // If the product is not successfully saved, the bookmark remains unfilled
                                        this.error = true;
                                        this.errorMessage = 'Failed to save product';
                                        setTimeout(() => {
                                            this.error = false;
                                        }, 3000);
                                    }
                                })
                                .catch(error => {
                                    this.saving = false;
                                    this.error = true;
                                    this.errorMessage = error.message || 'An error occurred';
                                    console.error('Error:', error);
                                    setTimeout(() => {
                                        this.error = false;
                                    }, 3000);
                                });
                            }
                        }"
                        class="flex-shrink-0"
                    >
                        {% csrf_token %}
                        <button type="button"
                                @click="!saved && saveForLater()"
                                class="p-2 focus:outline-none"
                                x-bind:disabled="saving || saved"
                                x-bind:title="error ? errorMessage : (saved ? '{% trans "Saved to your list" %}' : '{% trans "Save for later" %}')"
                        >
                            <!-- Empty bookmark (when not saved) -->
                            <svg x-show="!saved && !saving" class="w-6 h-6 text-blue-900" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
                            </svg>

                            <!-- Filled bookmark (when saved) -->
                            <svg x-show="saved" x-cloak class="w-6 h-6 text-blue-900" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z" clip-rule="evenodd" />
                            </svg>

                            <!-- Spinner (when saving) -->
                            <svg x-show="saving" x-cloak class="w-6 h-6 text-blue-900 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </div>
                {% endif %}

                <form action="{% url 'basket:add' pk=product.pk %}" method="post" class="{% if not mobile %}flex-1{% endif %}">
                    {% csrf_token %}
                    <input name="product_id" value="{{ product.id }}" type="hidden">
                    <input name="quantity" value="1" type="hidden">
                    <button type="submit"
                            class="inline-block {% if not mobile %}w-full{% endif %} px-4 py-2 bg-black text-white text-sm font-normal rounded shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out text-center">
                        {% trans "Buy now" %}
                    </button>
                </form>
            </div>
        {% else %}
            <a class="inline-block {% if not mobile %}w-full{% endif %} px-4 py-2 bg-blue-600 text-white text-sm font-normal rounded shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out text-center"
               {% if not mobile %}
               x-bind:class="Alpine.store('filters').state.viewMode === 'grid' ? 'w-full' : 'w-full md:w-auto'"
               {% endif %}
               href="{% url 'contact-vendor' pk=product.id %}">{% trans 'Contact vendor' %}</a>
        {% endif %}
    {% else %}
        {# Use the availability message if available, otherwise default to Unavailable #}
        <button type="button"
                class="inline-block {% if not mobile %}w-full{% endif %} px-4 py-2 bg-gray-300 text-gray-500 text-sm font-normal rounded shadow-sm cursor-not-allowed hover:bg-gray-300 text-center"
                {% if not mobile %}
                x-bind:class="Alpine.store('filters').state.viewMode === 'grid' ? 'w-full' : 'w-full md:w-auto'"
                {% endif %}
                disabled>
            {% if session and session.availability.message %}{{ session.availability.message }}{% else %}{% trans "Unavailable" %}{% endif %}
        </button>
    {% endif %}
{% endwith %}
