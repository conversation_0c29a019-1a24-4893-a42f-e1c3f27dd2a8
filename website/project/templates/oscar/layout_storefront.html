{% extends "oscar/base_storefront.html" %}
{% load i18n %}
{% load compress %}
{% load promotion_tags custom_product_tags %}
{% load static %}

{% block extrastyles %}
    {{ block.super }}
{% endblock %}

{% block layout %}
    {# Legacy browser warning banner #}
    <div id="legacy-browser-warning" class="hidden" style="position: sticky; top: 0; background-color: #ffe5b4; color: #333; padding: 10px; text-align: center; z-index: 9999; display: none;">
        <div style="display: flex; justify-content: space-between; align-items: center; max-width: 1200px; margin: 0 auto; padding: 0 10px;">
            <div> {% trans "You are using a legacy browser and some features may not work as expected. (Firefox is recommended on Win7)" %}</div>
            <button id="close-legacy-warning" style="background: none; border: none; cursor: pointer; font-size: 16px; margin-left: 15px;">✕</button>
        </div>
    </div>

    {% block primary_header %}
    {# Top-horizontal bar with account, notifictions, dashboard links #}
    {% include "oscar/partials/nav_accounts_storefront.html" %}

    {# Site logo/title, mini-basket, browse dropdown and searchbox #}
    <div class="bg-black">
        <div class="container mx-auto px-4 pb-4">
            <div class="flex flex-wrap items-center py-4">
                <!-- Logo - visible only on desktop -->
                <div class="hidden lg:block lg:w-1/5">
                    <a href="{% url 'promotions:home' %}" class="block">
                        <img 
                          src="{{ STATIC_URL }}img/partan_logo.png" 
                          srcset="{{ STATIC_URL }}img/partan_logo.webp 1x, {{ STATIC_URL }}img/partan_logo.png 1x"
                          class="max-h-20 w-auto mx-auto px-4"
                          alt="Partan"
                          width="217"
                          height="85"
                          loading="eager"
                          decoding="async">
                    </a>
                </div>
                <!-- Search - full width on mobile, partial width on desktop -->
                <div class="w-full lg:w-4/5">
                    {% include "oscar/partials/search_storefront.html" %}
                </div>
            </div>
        </div>
    </div>
    {% endblock %}

    <div class="container mx-auto h-1.5 rounded-b-full bg-blue-900 shadow"></div>

    {# Main content of page - other layout templates may override this block #}
    <main role="main" class="container mx-auto px-4 py-2">
        {% comment %} <div class="mb-4">
            {% block breadcrumbs %}{% endblock %}
        </div>
        {% block header %}
            <div class="mb-6">
                <h1 class="text-2xl font-bold">{% block headertext %}{% endblock %}</h1>
            </div>
        {% endblock %} {% endcomment %}
        {% include "oscar/partials/alert_messages.html" %}
        {% block subnavigation %}{% endblock %}
        {% block content_wrapper %}
        <div class="content-wrapper">
            {% block subheader %}{% endblock subheader %}

            {# Div exists for AJAX updates to entire content section #}
            <div id="content_inner">{% block content %}{% endblock %}</div>
        </div>
        {% endblock %}

        {% comment %} {% block promotions_horizontal %}
        <div class="mt-5">
            <div class="w-full">
                {% for promotion in promotions_horizontal %}
                    {% render_promotion promotion %}
                {% endfor %}
            </div>
        </div>
        {% endblock %} {% endcomment %}
    </main>

    <div class="container mx-auto h-1.5 rounded-t-full bg-blue-900"></div>

    {% block footer %}
    {% include "oscar/partials/footer_storefront.html" %}
    {% endblock %}
{% endblock %}


{% block extrahead %}
<!-- Minimal browser detection - loads full polyfills only when needed -->
<script>
  function isOldBrowser() {
    const ua = navigator.userAgent;
    // Simple checks for major legacy browsers
    return (ua.includes('Chrome/') && parseInt(ua.split('Chrome/')[1]) < 110) || 
           (ua.includes('Firefox/') && parseInt(ua.split('Firefox/')[1]) < 116) ||
           ua.includes('Edge/') || 
           ua.includes('Trident/') || 
           ua.includes('MSIE');
  }
  
  if (isOldBrowser()) {
    document.documentElement.classList.add('legacy-browser');
    // Asynchronously load the legacy browser fixes script
    var legacyScript = document.createElement('script');
    legacyScript.src = '{% static "js/legacy-browser-fixes.js" %}';
    document.head.appendChild(legacyScript);
  }
</script>

<!-- Link to CSS fixes (loaded only for legacy browsers) -->
<script>
  if (document.documentElement.classList.contains('legacy-browser')) {
    var legacyStyles = document.createElement('link');
    legacyStyles.rel = 'stylesheet';
    legacyStyles.href = '{% static "css/legacy-browser-fixes.css" %}';
    document.head.appendChild(legacyStyles);
  }
</script>

{{ block.super }}
{% endblock %}

{% block cdn_scripts %}
{{ block.super }}
{% endblock %}

{% block extrascripts %}
{{ block.super }}
{% endblock %}