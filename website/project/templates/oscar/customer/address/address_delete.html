{% extends "oscar/customer/baseaccountpage.html" %}

{% load i18n %}

{% block extra_breadcrumbs %}
    <li>
        <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="{% url 'customer:address-list' %}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">{% trans 'Address book' %}</a>
        </div>
    </li>
{% endblock %}

{% block tabcontent %}
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium leading-6 text-gray-900">{% trans 'Delete address?' %}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{% trans 'Are you sure you want to delete this address?' %}</p>
        </div>
        
        <div class="px-4 py-5 sm:p-6">
            <div class="bg-gray-50 p-4 rounded-md mb-6">
                <address class="text-sm text-gray-700 not-italic">
                    <span class="font-semibold text-gray-900 block mb-1">
                        {% with field=object.active_address_fields.0 %}
                            {{ field }}
                        {% endwith %}
                    </span>
                    {% for field in object.active_address_fields|slice:"1:" %}
                        {{ field }}<br>
                    {% endfor %}
                </address>
            </div>
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div class="flex items-center justify-end space-x-4">
                    <a href="{% url 'customer:address-list' %}" class="text-sm font-medium text-gray-700 hover:text-blue-600">
                        {% trans "Cancel" %}
                    </a>
                    <button type="submit" class="btn-danger" data-loading-text="{% trans 'Deleting...' %}">
                        {% trans "Delete address" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock tabcontent %}
