{% extends "oscar/customer/baseaccountpage.html" %}

{% load i18n %}

{% block extra_breadcrumbs %}
    <li>
        <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <a href="{% url 'customer:email-list' %}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">{% trans 'Email history' %}</a>
        </div>
    </li>
{% endblock %}

{% block tabcontent %}
    <div class="overflow-hidden bg-white shadow-md rounded-lg">
        <div class="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
            <h3 class="text-lg font-medium leading-6 text-gray-900">{% trans 'Email Details' %}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{% trans 'Information about this email.' %}</p>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 border-b border-gray-100">
                    <dt class="text-sm font-medium text-gray-500">{% trans 'Date sent' %}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ email.date_sent }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 border-b border-gray-100">
                    <dt class="text-sm font-medium text-gray-500">{% trans 'Subject' %}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">{{ email.subject }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:px-6 border-b border-gray-100">
                    <dt class="text-sm font-medium text-gray-500 mb-2">{% trans 'Body' %}</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {% if email.body_html %}
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <iframe id="preview_box" width="100%" height="400" style="width: 100%; height: 400px; border: 0;"></iframe>
                            </div>
                        {% else %}
                            <div class="border border-gray-200 rounded-lg overflow-hidden p-4 whitespace-pre-wrap">
                                {{ email.body_text }}
                            </div>
                        {% endif %}
                    </dd>
                </div>
            </dl>
        </div>
    </div>

    <div class="mt-6">
        <a href="{% url 'customer:email-list' %}" class="btn-secondary">{% trans 'Back to Email History' %}</a>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        var iframe = document.getElementById('preview_box');
        if (iframe) {
            // Get the raw HTML content
            var htmlContent = `{{ email.body_html|escapejs }}`;
            
            // Create a blob with the HTML content
            var blob = new Blob([htmlContent], {type: 'text/html'});
            
            // Create a URL for the blob
            var url = URL.createObjectURL(blob);
            
            // Set the iframe src to the blob URL
            iframe.src = url;
            
            // Clean up the URL when the iframe loads
            iframe.onload = function() {
                setTimeout(function() {
                    URL.revokeObjectURL(url);
                }, 1000);
            };
        }
    });
    </script>
{% endblock tabcontent %}
