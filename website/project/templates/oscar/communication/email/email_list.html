{% extends "oscar/customer/baseaccountpage.html" %}
{% load i18n %}

{% block tabcontent %}
    {% if emails %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans 'Subject' %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {% trans 'Date sent' %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for email in emails %}
                        <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                            <td class="px-6 py-4 text-sm font-medium text-blue-600">
                                <a href="{% url 'customer:email-detail' email_id=email.id %}" class="hover:underline">
                                    {{ email.subject }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ email.date_sent }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="mt-4">
            {% include "oscar/partials/pagination.html" %}
        </div>
    {% else %}
        <div class="bg-white shadow-md rounded-lg p-6">
            <p class="text-gray-500">{% trans 'No emails found' %}</p>
        </div>
    {% endif %}
{% endblock tabcontent %}
