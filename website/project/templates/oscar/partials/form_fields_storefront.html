{% load i18n %}
{% load form_tags %}

{% if form.is_bound and not form.is_valid %}
    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">{% trans "Oops! We found some errors" %}</h3>
                <p class="text-sm text-red-700">{% trans "Please check the error messages below and try again" %}</p>
            </div>
        </div>
    </div>
{% endif %}

{% if form.non_field_errors %}
    {% for error in form.non_field_errors %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
            <div class="flex">
                <div class="ml-3">
                    <p class="text-sm text-red-700">{{ error }}</p>
                </div>
            </div>
        </div>
    {% endfor %}
{% endif %}

{% for field in form.hidden_fields %}
    {{ field }}
{% endfor %}

{% for field in form.visible_fields %}
    {% annotate_form_field field %}
    
    {% if field.widget_type == 'CheckboxInput' %}
        <div class="mb-4">
            <div class="relative flex items-start">
                <div class="flex items-center h-5">
                    {{ field }}
                </div>
                <div class="ml-3 text-sm">
                    <label for="{{ field.auto_id }}" class="font-medium text-gray-700 {% if field.field.required %}required{% endif %}">
                        {{ field.label|safe }}
                    </label>
                    {% if field.help_text %}
                        <p class="text-gray-500">{{ field.help_text|safe }}</p>
                    {% endif %}
                </div>
            </div>
            {% for error in field.errors %}
                <p class="mt-2 text-sm text-red-600">{{ error }}</p>
            {% endfor %}
        </div>
    {% else %}
        <div class="mb-4">
            <label for="{{ field.auto_id }}" class="block text-sm font-medium text-gray-700 {% if field.field.required %}required{% endif %}">
                {{ field.label|safe }}
            </label>
            <div class="mt-1">
                {{ field }}
            </div>
            {% if field.errors %}
                {% for error in field.errors %}
                    <p class="mt-2 text-sm text-red-600">{{ error }}</p>
                {% endfor %}
            {% endif %}
            {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text|safe }}</p>
            {% endif %}
        </div>
    {% endif %}
{% endfor %}
