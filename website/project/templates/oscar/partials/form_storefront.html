{% load i18n %}

{% if form.is_bound and not form.is_valid %}
    <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">{% trans "Oops! We found some errors" %}</h3>
                <p class="text-sm text-red-700">{% trans "Please check the error messages below and try again" %}</p>
            </div>
        </div>
    </div>
{% endif %}

{% if form.non_field_errors %}
    {% for error in form.non_field_errors %}
        <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
            <div class="flex">
                <div class="ml-3">
                    <p class="text-sm text-red-700">{{ error }}</p>
                </div>
            </div>
        </div>
    {% endfor %}
{% endif %}

{% for field in form.hidden_fields %}
    {{ field }}
{% endfor %}

<div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
    {% for field in form.visible_fields %}
        <div class="{% if field.widget_type == 'Textarea' %}sm:col-span-2{% endif %}">
            {% if field.widget_type == 'CheckboxInput' %}
                <div class="relative flex items-start">
                    <div class="flex items-center h-5">
                        {{ field }}
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="{{ field.auto_id }}" class="font-medium text-gray-700 {% if field.field.required %}required{% endif %}">
                            {{ field.label|safe }}
                        </label>
                        {% if field.help_text %}
                            <p class="text-gray-500">{{ field.help_text|safe }}</p>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <label for="{{ field.auto_id }}" class="block text-sm font-medium text-gray-700 {% if field.field.required %}required{% endif %}">
                    {{ field.label|safe }}
                </label>
                <div class="mt-1">
                    <div class="w-full">
                        {{ field }}
                    </div>
                </div>
                {% if field.errors %}
                    {% for error in field.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ error }}</p>
                    {% endfor %}
                {% endif %}
                {% if field.help_text %}
                    <p class="mt-2 text-sm text-gray-500">{{ field.help_text|safe }}</p>
                {% endif %}
            {% endif %}
        </div>
    {% endfor %}
</div>

{% if include_buttons %}
    <div class="pt-5 mt-6">
        <div class="flex justify-end">
            {% if cancel_url %}
                {% if ':' in cancel_url %}
                    <a href="{% url cancel_url %}" class="btn-secondary mr-3">
                        {% trans "Cancel" %}
                    </a>
                {% else %}
                    <a href="{{ cancel_url }}" class="btn-secondary mr-3">
                        {% trans "Cancel" %}
                    </a>
                {% endif %}
            {% endif %}
            <button type="submit" class="btn-primary">
                {{ submit_text|default:_("Save") }}
            </button>
        </div>
    </div>
{% endif %}
