{% load display_tags %}
{% load i18n %}

{% if is_paginated %}
<div class="pagination-centered">
    <span class="page-links">
    {% if page_obj.has_previous %}
    <a data-page="1" href="?{% get_parameters page %}page=1"><i class="fa fa-fast-backward"></i></a>
    <a data-page="{{ page_obj.previous_page_number }}" href="?{% get_parameters page %}page={{ page_obj.previous_page_number }}"><i class="fa fa-backward"></i></a>
    {% endif %}
    {% for page in paginator.page_range %}
        {% if page < max_page and page > min_page and page_obj.number != page %}
            <a data-page="{{ page }}" href="?{% get_parameters page %}page={{ page }}">{{ page }}</a>
        {% elif page_obj.number == page %}
            <span>{{ page }}</span>
        {% endif %}
    {% endfor %}
    {% if page_obj.has_next %}
    <a data-page="{{ page_obj.next_page_number }}" href="?{% get_parameters page %}page={{ page_obj.next_page_number }}"><i class="fa fa-forward"></i></a>
    <a data-page="{{ paginator.num_pages }}" href="?{% get_parameters page %}page={{ paginator.num_pages }}"><i class="fa fa-fast-forward"></i></a>
    {% endif %}
    </span>
</div>
{% endif %}
