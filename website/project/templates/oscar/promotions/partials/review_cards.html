{% load i18n %}

{% for review in reviews %}
    <div
         x-data="{ expanded: false, showButton: false }"
         x-init="$nextTick(() => {
             const textEl = $refs.reviewText;
             showButton = textEl.scrollHeight > textEl.clientHeight;
         })"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         data-index="{{ forloop.counter0|add:start_index }}"
         class="notranslate bg-white rounded-lg shadow-md overflow-hidden flex flex-col h-full"
         translate="no">
        <div class="p-6 flex-grow">
            <!-- Review Score -->
            <div class="flex items-center mb-4">
                {% for i in "1234567890"|make_list %}
                    {% if forloop.counter <= review.score %}
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    {% else %}
                        <svg class="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    {% endif %}
                {% endfor %}
                <span class="ml-2 text-sm text-gray-600">{{ review.score }}/10</span>
            </div>

            <!-- Review Title -->
            <h3 class="notranslate text-lg font-semibold text-gray-800 mb-2" translate="no">{{ review.title }}</h3>

            <!-- Review Body -->
            <div class="mb-4">
                <p x-ref="reviewText"
                   class="text-gray-600"
                   :class="expanded ? '' : 'line-clamp-8'"
                   translate="no">
                   <span class="notranslate">{{ review.body|safe }}</span>
                </p>

                <!-- Expand/Collapse Button -->
                <button @click="expanded = !expanded"
                        class="mt-2 text-sm text-gray-600 hover:text-gray-800 focus:outline-none focus:underline transition-colors duration-200"
                        x-show="showButton || expanded"
                        x-text="expanded ? '{% trans "Show less" %}' : '{% trans "Show more" %}'">
                </button>
            </div>
        </div>

        <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
            <div class="flex justify-between items-center">
                <!-- Reviewer Name -->
                <div class="text-sm font-normal text-gray-800" translate="no">
                    {% if review.is_anonymous %}
                        {% if review.name and review.name|length < 20 %}
                            {{ review.name }}
                        {% else %}
                            {% trans "Anonymous" %}
                        {% endif %}
                    {% else %}
                        {% if review.user.get_full_name %}
                            {{ review.user.get_full_name }}
                        {% else %}
                            {# No full name, fallback to username. Check if username is long (potential ID) #}
                            {% if review.user.username|length >= 20 %}
                                {% trans "Anonymous" %}
                            {% else %}
                                {{ review.user.username }}
                            {% endif %}
                        {% endif %}
                    {% endif %}
                </div>

                <!-- Review Date -->
                <div class="text-xs text-gray-500">
                    {{ review.date_created|date:"Y M d" }}
                </div>
            </div>

            {% if review.country %}
            <div class="mt-2 flex items-center">
                <span class="text-xs text-gray-500" translate="no">{{ review.country }}</span>
            </div>
            {% endif %}
        </div>
    </div>
{% endfor %}
