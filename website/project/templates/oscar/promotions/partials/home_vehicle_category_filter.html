{% load i18n %}
{% if root_categories %}
{{ root_categories|json_script:"root-categories-data" }}
<script>
  try {
    window.rootCategories = JSON.parse(document.getElementById('root-categories-data').textContent);
  } catch (e) {
    window.rootCategories = [];
  }
</script>
{% else %}
<script>
  console.log("Debug: root_categories from Django is empty or None.");
  window.rootCategories = []; // Ensure it's an empty array if no categories
</script>
{% endif %}
<div class="bg-gray-100 p-4 rounded-lg my-4 shadow-md">
  <h3 class="text-xl md:text-2xl font-bold mb-6 text-gray-800 border-l-12 border-blue-800 pl-4">{% trans 'Search for parts' %}</h3>
  <div x-data="{
    // State
    step: 1,
    manufacturer: '',
    manufacturerSlug: '',
    models: [],
    types: [],
    categories: [],
    allModels: [],
    allTypes: [],
    allCategories: [],
    modelSearch: '',
    typeSearch: '',
    categorySearch: '',
    isLoadingModels: false,
    isLoadingTypes: false,
    isLoadingCategories: false,
    // Dropdown states
    openModelDropdown: false,
    openTypeDropdown: false,
    openCategoryDropdown: false,
    // Alpine init
    init() {
      // Optionally preload manufacturers/categories from window/global if available
      this.allCategories = window.rootCategories || []; // this.allCategories now holds the full tree
    },
    // Update manufacturer slug when manufacturer changes
    updateManufacturerSlug() {
      if (this.manufacturer) {
        const selectEl = document.getElementById('manufacturer-select');
        const selectedOption = selectEl.options[selectEl.selectedIndex];
        this.manufacturerSlug = selectedOption.getAttribute('data-slug');
      } else {
        this.manufacturerSlug = '';
      }
    },
    // Fetch models for selected manufacturer
    fetchModels() {
      if (!this.manufacturer) return;
      this.isLoadingModels = true;
      fetch(`/ctg/storefront/manufacturer/get_models/?manufacturers=${this.manufacturer}`)
        .then(r => r.json())
        .then(data => {
          this.allModels = data.map(m => ({id: m[0], name: m[1]}));
          this.isLoadingModels = false;
        })
        .catch(error => {
          console.error('Error fetching models:', error);
          this.isLoadingModels = false;
        });
    },
    // Fetch types for selected models
    fetchTypes() {
      if (!this.models.length) { this.allTypes = []; return; }
      this.isLoadingTypes = true;
      fetch(`/ctg/storefront/model/get_types/?models=${this.models.join(',')}`)
        .then(r => r.json())
        .then(data => {
          this.allTypes = data.map(t => ({id: t[0], name: t[1]}));
          this.isLoadingTypes = false;
        })
        .catch(error => {
          console.error('Error fetching types:', error);
          this.isLoadingTypes = false;
        });
    },
    // Category search and selection helpers
    filteredCategories() {
      const searchTerm = this.categorySearch.toLowerCase();
      let categories = [];
      
      // Function to normalize text by removing diacritics and special characters
      const normalizeText = (text) => {
        return text.toLowerCase()
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
          .replace(/[^a-z0-9\s]/g, ''); // Remove special characters except spaces
      };
      
      // Function to build category path and collect all categories
      const buildCategoryPath = (cats, parentPath = '') => {
        let result = [];
        for (const cat of cats || []) {
          const currentPath = parentPath ? `${parentPath} > ${cat.name}` : cat.name;
          const item = {...cat, fullPath: currentPath};
          result.push(item);
          
          if (cat.subcategories && cat.subcategories.length > 0) {
            result = result.concat(buildCategoryPath(cat.subcategories, currentPath));
          }
        }
        return result;
      };
      
      // Build flat list of all categories with their full paths
      const allCategoriesFlat = buildCategoryPath(this.allCategories || []);
      
      if (!searchTerm) {
        categories = allCategoriesFlat;
      } else {
        const normalizedSearchTerm = normalizeText(searchTerm);
        // Filter categories by normalized search term
        categories = allCategoriesFlat.filter(cat => 
          normalizeText(cat.name).includes(normalizedSearchTerm) || 
          normalizeText(cat.fullPath).includes(normalizedSearchTerm)
        );
      }
      
      // Sort with selected categories at the top
      return categories.sort((a, b) => {
        const aSelected = this.categories.includes(a.id.toString());
        const bSelected = this.categories.includes(b.id.toString());
        if (aSelected && !bSelected) return -1;
        if (!aSelected && bSelected) return 1;
        return 0;
      });
    },
    // Sort categories with selected ones at the top
    get sortedCategories() {
      return this.filteredCategories().sort((a, b) => {
        const aSelected = this.categories.includes(a.id.toString());
        const bSelected = this.categories.includes(b.id.toString());
        if (aSelected && !bSelected) return -1;
        if (!aSelected && bSelected) return 1;
        return 0;
      });
    },
    // Return UI text for selected items
    get selectedModelsText() {
      if (this.models.length === 0) return '{% trans "Select" %}';
      if (this.models.length === 1) {
        const model = this.allModels.find(m => m.id.toString() === this.models[0]);
        return model ? model.name : '';
      }
      return `${this.models.length} {% trans 'selected' %}`;
    },
    get selectedTypesText() {
      if (this.types.length === 0) return '{% trans "Select" %}';
      if (this.types.length === 1) {
        const type = this.allTypes.find(t => t.id.toString() === this.types[0]);
        return type ? type.name : '';
      }
      return `${this.types.length} {% trans 'selected' %}`;
    },
    // Filtered and sorted models list with selected models first
    get sortedModels() {
      if (!this.modelSearch) {
        return this.allModels.slice().sort((a, b) => {
          const aSelected = this.models.includes(a.id.toString());
          const bSelected = this.models.includes(b.id.toString());
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return 0;
        });
      } else {
        return this.allModels
          .filter(m => m.name.toLowerCase().includes(this.modelSearch.toLowerCase()))
          .sort((a, b) => {
            const aSelected = this.models.includes(a.id.toString());
            const bSelected = this.models.includes(b.id.toString());
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;
            return 0;
          });
      }
    },
    // Filtered and sorted types list with selected types first
    get sortedTypes() {
      if (!this.typeSearch) {
        return this.allTypes.slice().sort((a, b) => {
          const aSelected = this.types.includes(a.id.toString());
          const bSelected = this.types.includes(b.id.toString());
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return 0;
        });
      } else {
        return this.allTypes
          .filter(t => t.name.toLowerCase().includes(this.typeSearch.toLowerCase()))
          .sort((a, b) => {
            const aSelected = this.types.includes(a.id.toString());
            const bSelected = this.types.includes(b.id.toString());
            if (aSelected && !bSelected) return -1;
            if (!aSelected && bSelected) return 1;
            return 0;
          });
      }
    },
    get selectedCategoriesText() {
      if (this.categories.length === 0) return '{% trans "Select" %}';
      if (this.categories.length === 1) {
        // Find the category in the entire category tree
        const findCategory = (id, categories) => {
          for (const cat of categories) {
            if (cat.id.toString() === id) {
              return cat;
            }
            if (cat.subcategories && cat.subcategories.length > 0) {
              const found = findCategory(id, cat.subcategories);
              if (found) return found;
            }
          }
          return null;
        };
        const category = findCategory(this.categories[0], this.allCategories);
        return category ? category.name : '';
      }
      return `${this.categories.length} {% trans 'selected' %}`;
    },
    // Toggle selection for multi-selects
    toggleModel(modelId) {
      const index = this.models.indexOf(modelId);
      if (index === -1) {
        this.models.push(modelId);
      } else {
        this.models.splice(index, 1);
      }
      this.types = []; // Reset types when models change
      this.fetchTypes();
    },
    toggleType(typeId) {
      const index = this.types.indexOf(typeId);
      if (index === -1) {
        this.types.push(typeId);
      } else {
        this.types.splice(index, 1);
      }
    },
    toggleCategory(categoryId) {
      const index = this.categories.indexOf(categoryId);
      if (index === -1) {
        this.categories.push(categoryId);
      } else {
        this.categories.splice(index, 1);
      }
    },
    // Stepper navigation
    nextStep() {
      if (this.step < 4) this.step++;
    },
    prevStep() {
      if (this.step > 1) this.step--;
    },
    // Reset scroll for dropdowns
    resetScroll(element) {
      if (element) {
        element.scrollTop = 0;
      }
    },
    // Build URL and redirect
    submit() {
      let params = [];
      if (this.manufacturerSlug) {
        params.push(`manufacturer=${this.manufacturerSlug}`);
      }
      if (this.models.length) params.push(...this.models.map(m => `models=${m}`));
      if (this.types.length) params.push(...this.types.map(t => `types=${t}`));
      if (this.categories.length) params.push(...this.categories.map(c => `category_ids=${c}`));
      let url = '/catalogue/?' + params.join('&');
      window.location.href = url;
    }
  }" x-init="init()" class="w-full">
    <div class="grid grid-cols-1 lg:grid-cols-[1fr_1fr_1fr_1fr_auto] gap-4">
      <!-- Step 1: Manufacturer -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans 'Manufacturer' %}</label>
        <div class="relative">
          <select 
            id="manufacturer-select"
            x-model="manufacturer" 
            @change="updateManufacturerSlug(); fetchModels(); models=[]; types=[]; step=2" 
            class="w-full px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none pr-8"
          >
            <option value="">{% trans 'Select' %}</option>
            {% for m in manufacturers %}
              <option value="{{ m.id }}" data-slug="{{ m.slug }}">{{ m.brand }}</option>
            {% endfor %}
          </select>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Step 2: Model -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans 'Model' %}</label>
        <div class="relative">
          <!-- Dropdown button -->
          <button 
            @click="allModels.length > 0 && (openModelDropdown = !openModelDropdown); if(openModelDropdown) { modelSearch = ''; $nextTick(() => { $refs.modelSearch.focus(); resetScroll($refs.modelsList); }) }" 
            @click.away="openModelDropdown = false"
            type="button" 
            :class="{'bg-gray-100 cursor-not-allowed': allModels.length === 0}"
            class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
          >
            <span 
              x-text="isLoadingModels ? '{% trans "Loading..." %}' : (allModels.length > 0 ? selectedModelsText : '{% trans 'Select a manufacturer first' %}')"
              class="block truncate"
              :class="{'text-gray-500 italic': allModels.length === 0 || isLoadingModels}"
            ></span>
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': openModelDropdown}">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Dropdown menu -->
          <div 
            x-show="openModelDropdown"
            x-cloak
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
          >
            <!-- Search input -->
            <div class="px-3 py-2 bg-white border-b border-gray-200">
              <input 
                type="text" 
                x-model="modelSearch" 
                x-ref="modelSearch"
                @click.stop
                placeholder="{% trans 'Quick search...' %}"
                spellcheck="false"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
            </div>
            
            <!-- Loading indicator -->
            <div x-show="isLoadingModels" class="py-3 px-4 text-sm text-gray-500 flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {% trans "Loading..." %}
            </div>
            
            <!-- Models list with checkboxes -->
            <div class="overflow-y-auto max-h-48" x-show="!isLoadingModels" x-ref="modelsList">
              <template x-for="model in sortedModels" :key="model.id">
                <div class="px-4 py-2 cursor-pointer hover:bg-gray-100" :class="{'bg-blue-50': models.includes(model.id.toString())}">
                  <label class="flex items-center gap-x-2 cursor-pointer">
                    <input 
                      type="checkbox" 
                      :value="model.id" 
                      :checked="models.includes(model.id.toString())"
                      @change="toggleModel(model.id.toString())"
                      @click.stop
                    >
                    <span x-text="model.name" class="text-sm"></span>
                  </label>
                </div>
              </template>
              
              <!-- Empty state -->
              <div x-show="modelSearch && sortedModels.length === 0" class="py-3 px-4 text-sm text-gray-500 italic">
                {% trans "No models found" %}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Step 3: Type -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans 'Modification' %}</label>
        <div class="relative">
          <!-- Dropdown button -->
          <button 
            @click="allTypes.length > 0 && (openTypeDropdown = !openTypeDropdown); if(openTypeDropdown) { typeSearch = ''; $nextTick(() => { $refs.typeSearch.focus(); resetScroll($refs.typesList); }) }" 
            @click.away="openTypeDropdown = false"
            type="button" 
            :class="{'bg-gray-100 cursor-not-allowed': allTypes.length === 0}"
            class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
          >
            <span 
              x-text="isLoadingTypes ? '{% trans "Loading..." %}' : (allTypes.length > 0 ? selectedTypesText : '---')"
              class="block truncate"
              :class="{'text-gray-500 italic': allTypes.length === 0 || isLoadingTypes}"
            ></span>
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': openTypeDropdown}">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Dropdown menu -->
          <div 
            x-show="openTypeDropdown"
            x-cloak
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
          >
            <!-- Search input -->
            <div class="px-3 py-2 bg-white border-b border-gray-200">
              <input 
                type="text" 
                x-model="typeSearch" 
                x-ref="typeSearch"
                @click.stop
                placeholder="{% trans 'Quick search...' %}"
                spellcheck="false"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
            </div>
            
            <!-- Loading indicator -->
            <div x-show="isLoadingTypes" class="py-3 px-4 text-sm text-gray-500 flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {% trans "Loading..." %}
            </div>
            
            <!-- Types list with checkboxes -->
            <div class="overflow-y-auto max-h-48" x-show="!isLoadingTypes" x-ref="typesList">
              <template x-for="type in sortedTypes" :key="type.id">
                <div class="px-4 py-2 cursor-pointer hover:bg-gray-100" :class="{'bg-blue-50': types.includes(type.id.toString())}">
                  <label class="flex items-center gap-x-2 cursor-pointer">
                    <input 
                      type="checkbox" 
                      :value="type.id" 
                      :checked="types.includes(type.id.toString())"
                      @change="toggleType(type.id.toString())"
                      @click.stop
                    >
                    <span x-text="type.name" class="text-sm"></span>
                  </label>
                </div>
              </template>
              
              <!-- Empty state -->
              <div x-show="typeSearch && sortedTypes.length === 0" class="py-3 px-4 text-sm text-gray-500 italic">
                {% trans "No types found" %}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Step 4: Category -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans 'Category' %}</label>
        <div class="relative">
          <!-- Dropdown button -->
          <button 
            @click="openCategoryDropdown = !openCategoryDropdown; if(openCategoryDropdown) { categorySearch = ''; $nextTick(() => { $refs.categorySearch.focus(); resetScroll($refs.categoriesList); }) }" 
            @click.away="openCategoryDropdown = false"
            type="button" 
            class="relative w-full flex items-center justify-between px-3 py-2 bg-white border border-gray-300 rounded-md text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 cursor-pointer"
          >
            <span 
              x-text="selectedCategoriesText"
              class="block truncate"
            ></span>
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" :class="{'transform rotate-180': openCategoryDropdown}">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Dropdown menu -->
          <div 
            x-show="openCategoryDropdown"
            x-cloak
            x-transition:enter="transition ease-out duration-100"
            x-transition:enter-start="transform opacity-0 scale-95"
            x-transition:enter-end="transform opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-75"
            x-transition:leave-start="transform opacity-100 scale-100"
            x-transition:leave-end="transform opacity-0 scale-95"
            class="absolute z-50 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base focus:outline-none sm:text-sm"
          >
            <!-- Search input -->
            <div class="px-3 py-2 bg-white border-b border-gray-200">
              <input 
                type="text" 
                x-model="categorySearch" 
                x-ref="categorySearch"
                @click.stop
                placeholder="{% trans 'Quick search...' %}"
                spellcheck="false"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
            </div>
            
            <!-- Categories list with checkboxes -->
            <div class="overflow-y-auto max-h-48" x-ref="categoriesList">
              <template x-for="category in filteredCategories().slice(0, 30)" :key="category.id">
                <div class="px-4 py-2 cursor-pointer hover:bg-gray-100" :class="{'bg-blue-50': categories.includes(category.id.toString())}">
                  <label class="flex items-center gap-x-2 cursor-pointer">
                    <input 
                      type="checkbox" 
                      :value="category.id" 
                      :checked="categories.includes(category.id.toString())"
                      @change="toggleCategory(category.id.toString())"
                      @click.stop
                    >
                    <span x-text="category.fullPath" class="text-sm"></span>
                  </label>
                </div>
              </template>
              
              <!-- Empty state -->
              <div x-show="categorySearch && filteredCategories().length === 0" class="py-3 px-4 text-sm text-gray-500 italic">
                {% trans "No categories found" %}
              </div>
            </div>
            <div x-show="filteredCategories().length > 30" class="text-xs text-gray-500 px-4 py-2">
              Showing only 30 of <span x-text="filteredCategories().length"></span> results, please refine your search.
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search button -->
      <div class="flex items-end">
        <button @click="submit" class="h-[42px] px-4 bg-gray-700 hover:bg-blue-700 text-white font-normal rounded-md transition-colors flex items-center justify-center gap-2 whitespace-nowrap">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z" />
          </svg>
          {% trans 'Search' %}
        </button>
      </div>
    </div>
  </div>
</div> 