import logging
from decimal import Decimal as D

from django.conf import settings
from django.contrib.auth.models import User
from django.db import models
from django.db.models import signals
from django.utils.translation import gettext_lazy as _

from project.accounts.signals import create_profile
from project.apps.shipping.models import OrderAndItemCharges

logger = logging.getLogger(__name__)


class AbstractAccount(models.Model):
    # Contact information
    contact_name = models.CharField(_('Contact name'), max_length=100, blank=True)
    contact_address = models.CharField(_('Contact address'), max_length=100, blank=True)
    contact_phone = models.CharField(_('Contact phone'), max_length=100, blank=True)
    contact_fax = models.CharField(_('Contact fax'), max_length=100, blank=True, default='')
    contact_skype = models.CharField(_('Contact skype'), max_length=100, blank=True)
    contact_info = models.TextField(_('Additional contact info'), blank=True)
    # Payment information
    bank_info = models.TextField(_('Bank information'), blank=True)
    # Other information
    available_payment_methods = models.ManyToManyField(
        'order.PaymentMethod', related_name='available_for_%(class)s', verbose_name=_('Available payment methods')
    )
    available_shipping_methods = models.ManyToManyField(
        'order.ShippingMethod', related_name='available_for_%(class)s', verbose_name=_('Available shipping methods')
    )
    payment_methods = models.ManyToManyField(
        'order.PaymentMethod', related_name='selected_for_%(class)s', verbose_name=_('Payment method')
    )
    shipping_methods = models.ManyToManyField(
        'order.ShippingMethod', related_name='selected_for_%(class)s', verbose_name=_('Shipping methods')
    )

    class Meta:
        abstract = True

    def get_contact_info(self):
        return '<br>'.join(self.contact_info.split('\r\n'))

    def get_bank_info(self):
        return '<br>'.join(self.bank_info.split('\r\n'))


class Account(AbstractAccount):
    VENDOR_FIELDS = [
        'contact_info',
        'contact_name',
        'contact_address',
        'contact_fax',
        'contact_phone',
        'contact_skype',
        'bank_info',
        'payment_methods',
        'shipping_methods',
        'volume_weight_factor',
        'price_factor',
    ]

    user = models.OneToOneField('auth.User', related_name='account', on_delete=models.CASCADE)
    owner = models.ForeignKey('self', related_name='managers', null=True, blank=True, on_delete=models.CASCADE)
    price_factor = models.PositiveIntegerField(_('Price factor, %'), default=0)
    volume_weight_factor = models.IntegerField(_('Volume weight factor'), default=5000)

    def __str__(self):
        return str(self.user)

    @property
    def full_name(self):
        return '%s %s' % (self.user.first_name, self.user.last_name)

    def get_shipping_templates(self, weight, height=None, width=None, length=None):
        """Get shipping templates based on weight only"""
        weight = D(weight) if weight else D('0')

        shipping_templates = list(
            OrderAndItemCharges.objects.filter(
                weight_from__lt=weight,
                weight_to__gte=weight
            ).values_list('id', flat=True)
        )

        return shipping_templates

    @classmethod
    def get_shipping_templates_for_weight(cls, weight, height=None, width=None, length=None):
        """Get shipping templates based on weight only (class method)
        
        This is a static version of the instance method get_shipping_templates
        that can be used without an Account instance.
        """
        from project.apps.shipping.models import OrderAndItemCharges
        from decimal import Decimal
        
        weight = Decimal(weight) if weight else Decimal('0')

        shipping_templates = list(
            OrderAndItemCharges.objects.filter(
                weight_from__lt=weight,
                weight_to__gte=weight
            ).values_list('id', flat=True)
        )

        return shipping_templates

    @classmethod
    def get_owner(cls, user):
        if user.account.owner:
            owner = user.account.owner.user
            subowner = user
        else:
            owner = user
            subowner = None
        return owner, subowner


class Branch(AbstractAccount):
    BRANCH_CHOICES = [(b, b) for b in list(settings.BRANCH_SETTINGS.keys())]

    name = models.CharField(_('Name'), max_length=100)
    branch = models.CharField(
        _('Branch'), max_length=2, editable=False, choices=BRANCH_CHOICES, default=settings.BRANCH_DE
    )
    country = models.CharField(_('Country'), max_length=2, editable=False, default='')
    company_name = models.CharField(
        _('Company name'),
        max_length=100,
    )
    company_code = models.CharField(_('Company code'), max_length=100, blank=True, default='')
    company_vat_code = models.CharField(_('Company VAT code'), max_length=100, blank=True, default='')
    contact_email = models.CharField(_('Contact email'), max_length=100, blank=True, default='')
    owner = models.ForeignKey(Account, related_name='branches', null=True, blank=True, on_delete=models.CASCADE)
    shop_name = models.CharField(_('Shop name'), max_length=100, blank=True, default='PARTAN')
    shop_tagline = models.CharField(
        _('Shop tagline'),
        max_length=100,
        blank=True,
        default='',
    )
    shop_description = models.TextField(_('Shop description'), blank=True, default='')
    shop_keywords = models.CharField(_('Shop keywords'), max_length=100, blank=True, default='')

    class Meta:
        verbose_name = _('Branch')
        verbose_name_plural = _('Branches')

    def __str__(self):
        return self.branch


signals.post_save.connect(create_profile, sender=User)
