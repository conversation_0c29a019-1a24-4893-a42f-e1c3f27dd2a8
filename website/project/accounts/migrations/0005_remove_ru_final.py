from django.db import migrations

def update_existing_data(apps, schema_editor):
    FlatpageSite = apps.get_model('flatpages', 'flatpage_sites')
    # for obj in flatpage_sites.objects.all():
    #     if obj.site_id == 4:
    #         obj.delete()
    FlatpageSite.objects.filter(site_id=4).delete()

    Order = apps.get_model('order', 'Order')
    # for obj in orders.objects.all():
    #     if obj.site_id == 4:
    #         obj.site_id = 1
    #         obj.save()
    Order.objects.filter(site_id=4).update(site_id=1)

    Site = apps.get_model('sites', 'Site')
    # for obj in sites.objects.all():
    #     if obj.id == 4:
    #         obj.delete()
    Site.objects.get(id=4).delete()

    Branch = apps.get_model('accounts', 'Branch')
    # for obj in branch.objects.all():
    #     if obj.id == 4:
    #         obj.delete()
    Branch.objects.get(id=4).delete()

class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0004_alter_account_available_payment_methods_and_more'),
    ]

    operations = [
        migrations.RunPython(update_existing_data),
    ]