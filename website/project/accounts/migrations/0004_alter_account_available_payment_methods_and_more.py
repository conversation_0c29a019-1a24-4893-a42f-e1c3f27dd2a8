# Generated by Django 4.2.9 on 2024-06-21 15:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0019_alter_surcharge_options_line_tax_code_and_more'),
        ('accounts', '0003_alter_branch_branch'),
    ]

    operations = [
        migrations.AlterField(
            model_name='account',
            name='available_payment_methods',
            field=models.ManyToManyField(related_name='available_for_%(class)s', to='order.paymentmethod', verbose_name='Available payment methods'),
        ),
        migrations.AlterField(
            model_name='account',
            name='available_shipping_methods',
            field=models.ManyToManyField(related_name='available_for_%(class)s', to='order.shippingmethod', verbose_name='Available shipping methods'),
        ),
        migrations.AlterField(
            model_name='account',
            name='payment_methods',
            field=models.ManyToManyField(related_name='selected_for_%(class)s', to='order.paymentmethod', verbose_name='Payment method'),
        ),
        migrations.AlterField(
            model_name='account',
            name='shipping_methods',
            field=models.ManyToManyField(related_name='selected_for_%(class)s', to='order.shippingmethod', verbose_name='Shipping methods'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='available_payment_methods',
            field=models.ManyToManyField(related_name='available_for_%(class)s', to='order.paymentmethod', verbose_name='Available payment methods'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='available_shipping_methods',
            field=models.ManyToManyField(related_name='available_for_%(class)s', to='order.shippingmethod', verbose_name='Available shipping methods'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='payment_methods',
            field=models.ManyToManyField(related_name='selected_for_%(class)s', to='order.paymentmethod', verbose_name='Payment method'),
        ),
        migrations.AlterField(
            model_name='branch',
            name='shipping_methods',
            field=models.ManyToManyField(related_name='selected_for_%(class)s', to='order.shippingmethod', verbose_name='Shipping methods'),
        ),
    ]
