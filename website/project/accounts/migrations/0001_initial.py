# -*- coding: utf-8 -*-


from django.db import models, migrations
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0002_auto_20220713_1737'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('contact_name', models.CharField(max_length=100, verbose_name='Contact name', blank=True)),
                ('contact_address', models.CharField(max_length=100, verbose_name='Contact address', blank=True)),
                ('contact_phone', models.CharField(max_length=100, verbose_name='Contact phone', blank=True)),
                ('contact_fax', models.CharField(default=b'', max_length=100, verbose_name='Contact fax', blank=True)),
                ('contact_skype', models.Char<PERSON>ield(max_length=100, verbose_name='Contact skype', blank=True)),
                ('contact_info', models.TextField(verbose_name='Additional contact info', blank=True)),
                ('bank_info', models.TextField(verbose_name='Bank information', blank=True)),
                ('price_factor', models.PositiveIntegerField(default=0, verbose_name='Price factor, %')),
                ('volume_weight_factor', models.IntegerField(default=5000, verbose_name='Volume weight factor')),
                (
                    'available_payment_methods',
                    models.ManyToManyField(
                        related_name='available_for_account',
                        verbose_name='Available payment methods',
                        to='order.PaymentMethod',
                    ),
                ),
                (
                    'available_shipping_methods',
                    models.ManyToManyField(
                        related_name='available_for_account',
                        verbose_name='Available shipping methods',
                        to='order.ShippingMethod',
                    ),
                ),
                (
                    'owner',
                    models.ForeignKey(
                        related_name='managers', blank=True, to='accounts.Account', null=True, on_delete=models.CASCADE
                    ),
                ),
                (
                    'payment_methods',
                    models.ManyToManyField(
                        related_name='selected_for_account', verbose_name='Payment method', to='order.PaymentMethod'
                    ),
                ),
                (
                    'shipping_methods',
                    models.ManyToManyField(
                        related_name='selected_for_account', verbose_name='Shipping methods', to='order.ShippingMethod'
                    ),
                ),
                (
                    'user',
                    models.OneToOneField(
                        related_name='account', to=settings.AUTH_USER_MODEL, on_delete=models.CASCADE
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('contact_name', models.CharField(max_length=100, verbose_name='Contact name', blank=True)),
                ('contact_address', models.CharField(max_length=100, verbose_name='Contact address', blank=True)),
                ('contact_phone', models.CharField(max_length=100, verbose_name='Contact phone', blank=True)),
                ('contact_fax', models.CharField(default=b'', max_length=100, verbose_name='Contact fax', blank=True)),
                ('contact_skype', models.CharField(max_length=100, verbose_name='Contact skype', blank=True)),
                ('contact_info', models.TextField(verbose_name='Additional contact info', blank=True)),
                ('bank_info', models.TextField(verbose_name='Bank information', blank=True)),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                (
                    'branch',
                    models.CharField(
                        default=b'de',
                        verbose_name='Branch',
                        max_length=2,
                        editable=False,
                        choices=[
                            (b'ru', b'ru'),
                            (b'fr', b'fr'),
                            (b'de', b'de'),
                            (b'lt', b'lt'),
                            (b'pl', b'pl'),
                            (b'es', b'es'),
                        ],
                    ),
                ),
                ('country', models.CharField(default=b'', verbose_name='Country', max_length=2, editable=False)),
                ('company_name', models.CharField(max_length=100, verbose_name='Company name')),
                (
                    'company_code',
                    models.CharField(default=b'', max_length=100, verbose_name='Company code', blank=True),
                ),
                (
                    'company_vat_code',
                    models.CharField(default=b'', max_length=100, verbose_name='Company VAT code', blank=True),
                ),
                (
                    'contact_email',
                    models.CharField(default=b'', max_length=100, verbose_name='Contact email', blank=True),
                ),
                (
                    'shop_name',
                    models.CharField(default=b'PARTAN', max_length=100, verbose_name='Shop name', blank=True),
                ),
                (
                    'shop_tagline',
                    models.CharField(default=b'', max_length=100, verbose_name='Shop tagline', blank=True),
                ),
                ('shop_description', models.TextField(default=b'', verbose_name='Shop description', blank=True)),
                (
                    'shop_keywords',
                    models.CharField(default=b'', max_length=100, verbose_name='Shop keywords', blank=True),
                ),
                (
                    'available_payment_methods',
                    models.ManyToManyField(
                        related_name='available_for_branch',
                        verbose_name='Available payment methods',
                        to='order.PaymentMethod',
                    ),
                ),
                (
                    'available_shipping_methods',
                    models.ManyToManyField(
                        related_name='available_for_branch',
                        verbose_name='Available shipping methods',
                        to='order.ShippingMethod',
                    ),
                ),
                (
                    'owner',
                    models.ForeignKey(
                        related_name='branches', blank=True, to='accounts.Account', null=True, on_delete=models.CASCADE
                    ),
                ),
                (
                    'payment_methods',
                    models.ManyToManyField(
                        related_name='selected_for_branch', verbose_name='Payment method', to='order.PaymentMethod'
                    ),
                ),
                (
                    'shipping_methods',
                    models.ManyToManyField(
                        related_name='selected_for_branch', verbose_name='Shipping methods', to='order.ShippingMethod'
                    ),
                ),
            ],
            options={
                'verbose_name': 'Branch',
                'verbose_name_plural': 'Branches',
            },
            bases=(models.Model,),
        ),
    ]
