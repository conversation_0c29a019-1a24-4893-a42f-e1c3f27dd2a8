# Django
from django.contrib import admin
from django.contrib.auth import admin as djangoadmin
from django.contrib.auth.models import User

from project.accounts.models import Account, Branch


class UserAccountInline(admin.StackedInline):
    model = Account
    max_num = 1
    can_delete = False


class UserAdmin(djangoadmin.UserAdmin):
    inlines = [UserAccountInline]


class BranchAdmin(admin.ModelAdmin):
    list_display = ['country', 'branch', 'company_name', 'contact_email']


try:
    # unregister old user admin, it can raise exception if its already
    # unregistered
    admin.site.unregister(User)
finally:
    # register new user admin
    admin.site.register(User, UserAdmin)
admin.site.register(Branch, BranchAdmin)
